(()=>{var e={};e.id=1529,e.ids=[1489,1529],e.modules={507:(e,t,s)=>{"use strict";s.d(t,{Dc:()=>r,p2:()=>i});let i=[{id:"general_chat",name:"<PERSON> Chat",description:"Casual conversation, simple questions, greetings, and basic interactions that do not require specialized expertise."},{id:"logic_reasoning",name:"Logic & Reasoning",description:"Mathematical calculations, logical puzzles, analytical problem-solving, deductive reasoning, and complex analytical thinking."},{id:"writing",name:"Writing & Content Creation",description:"Creating written content like stories, articles, books, poems, scripts, marketing copy, blog posts, and creative narratives."},{id:"coding_frontend",name:"Coding - Frontend",description:"Building user interfaces with HTML, CSS, JavaScript, React, Vue, Angular, web design, and client-side development."},{id:"coding_backend",name:"Coding - Backend",description:"Programming server-side applications, APIs, databases, Python scripts, Node.js, Java, backend logic, and system architecture."},{id:"research_synthesis",name:"Research & Synthesis",description:"Gathering information from sources, conducting research, analyzing data, and synthesizing findings into comprehensive reports."},{id:"summarization_briefing",name:"Summarization & Briefing",description:"Condensing lengthy documents, extracting key points, creating executive summaries, and briefing complex information."},{id:"translation_localization",name:"Translation & Localization",description:"Converting text between different languages, linguistic translation, cultural adaptation, and multilingual communication."},{id:"data_extraction_structuring",name:"Data Extraction & Structuring",description:"Extracting specific information from unstructured text, organizing data into tables, lists, JSON, CSV, and structured formats."},{id:"brainstorming_ideation",name:"Brainstorming & Ideation",description:"Generating creative ideas, innovative concepts, brainstorming solutions, exploring possibilities, and creative thinking sessions."},{id:"education_tutoring",name:"Education & Tutoring",description:"Teaching concepts, explaining topics step-by-step, creating educational materials, tutoring, and providing learning guidance."},{id:"image_generation",name:"Image Generation",description:"Creating visual images, artwork, graphics, and illustrations from textual descriptions using AI image generation models."},{id:"audio_transcription",name:"Audio Transcription",description:"Converting speech from audio files into written text, transcribing recordings, and speech-to-text processing."},{id:"data_extractor",name:"Data Extractor",description:"Extracting specific data from web pages, scraping content, and gathering information from websites."},{id:"form_filler",name:"Form Filler",description:"Filling out web forms, submitting data, and handling form-based interactions on websites."},{id:"verification_agent",name:"Verification Agent",description:"Verifying information on websites, fact-checking, and validating data accuracy."},{id:"research_assistant",name:"Research Assistant",description:"Conducting web-based research, gathering information from multiple sources, and compiling research findings."},{id:"shopping_assistant",name:"Shopping Assistant",description:"Helping with online shopping, price comparisons, product research, and e-commerce tasks."},{id:"price_comparison",name:"Price Comparison",description:"Comparing prices across different websites, finding deals, and analyzing product pricing."},{id:"fact_checker",name:"Fact Checker",description:"Verifying facts and information across multiple web sources, cross-referencing data for accuracy."},{id:"task_executor",name:"Task Executor",description:"General task execution and automation, handling various web-based tasks and workflows."}],r=e=>i.find(t=>t.id===e)},2507:(e,t,s)=>{"use strict";s.d(t,{Q:()=>a,createSupabaseServerClientOnRequest:()=>n});var i=s(34386),r=s(44999);async function n(){let e=await (0,r.UL)();return(0,i.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,s,i){try{e.set({name:t,value:s,...i})}catch(e){}},remove(t,s){try{e.set({name:t,value:"",...s})}catch(e){}}}})}function a(e){return(0,i.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,s){},remove(e,t){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},43156:(e,t,s)=>{"use strict";s.d(t,{Nu:()=>o,iK:()=>a,zX:()=>n});var i=s(94473);let r={free:{name:"Free",price:"$0",priceId:i.Dm.FREE,productId:i.Zu.FREE,features:["Unlimited API requests","1 Custom Configuration","3 API Keys per config","All 300+ AI models","Strict fallback routing only","Basic analytics only","No custom roles, basic router only","Limited logs","Community support"],limits:{configurations:1,apiKeysPerConfig:3,apiRequests:999999,canUseAdvancedRouting:!1,canUseCustomRoles:!1,maxCustomRoles:0,canUsePromptEngineering:!1,canUseKnowledgeBase:!1,knowledgeBaseDocuments:0,canUseSemanticCaching:!1}},starter:{name:"Starter",price:"$19",priceId:i.Dm.STARTER,productId:i.Zu.STARTER,features:["Unlimited API requests","15 Custom Configurations","5 API Keys per config","All 300+ AI models","Intelligent routing strategies","Up to 3 custom roles","Intelligent role routing","Prompt engineering (no file upload)","Enhanced logs and analytics","Community support"],limits:{configurations:15,apiKeysPerConfig:5,apiRequests:999999,canUseAdvancedRouting:!0,canUseCustomRoles:!0,maxCustomRoles:3,canUsePromptEngineering:!0,canUseKnowledgeBase:!1,knowledgeBaseDocuments:0,canUseSemanticCaching:!1}},professional:{name:"Professional",price:"$49",priceId:i.Dm.PROFESSIONAL,productId:i.Zu.PROFESSIONAL,features:["Unlimited API requests","Unlimited Custom Configurations","Unlimited API Keys per config","All 300+ AI models","All advanced routing strategies","Unlimited custom roles","Prompt engineering + Knowledge base (5 documents)","Semantic caching","Advanced analytics and logging","Priority email support"],limits:{configurations:999999,apiKeysPerConfig:999999,apiRequests:999999,canUseAdvancedRouting:!0,canUseCustomRoles:!0,maxCustomRoles:999999,canUsePromptEngineering:!0,canUseKnowledgeBase:!0,knowledgeBaseDocuments:5,canUseSemanticCaching:!0}}};function n(e){return r[e]}function a(e,t,s){let i=r[e].limits;switch(t){case"create_config":return s<i.configurations;case"create_api_key":return s<i.apiKeysPerConfig;default:return!0}}function o(e,t){let s=r[e].limits;switch(t){case"custom_roles":return s.canUseCustomRoles;case"knowledge_base":return s.canUseKnowledgeBase;case"advanced_routing":return s.canUseAdvancedRouting;case"prompt_engineering":return s.canUsePromptEngineering;case"semantic_caching":return s.canUseSemanticCaching;case"configurations":return s.configurations>0;default:return!1}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},49722:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>f,routeModule:()=>g,serverHooks:()=>I,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>_});var i={};s.r(i),s.d(i,{GET:()=>l,POST:()=>p});var r=s(96559),n=s(48088),a=s(37719),o=s(32190),c=s(2507),u=s(507),d=s(43156);async function l(e,{params:t}){let s=(0,c.Q)(e),{apiKeyId:i}=await t;if(!i)return o.NextResponse.json({error:"API Key ID is required"},{status:400});try{let{data:e,error:t}=await s.from("api_key_role_assignments").select("role_name, created_at").eq("api_key_id",i);if(t)return o.NextResponse.json({error:"Failed to fetch role assignments",details:t.message},{status:500});let r=e.map(e=>{let t=(0,u.Dc)(e.role_name);return{...e,role_details:t||{id:e.role_name,name:e.role_name,description:"Custom role (details managed globally)"}}});return o.NextResponse.json(r||[],{status:200})}catch(e){return o.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function p(e,{params:t}){let s=(0,c.Q)(e),{apiKeyId:i}=await t,{data:{session:r},error:n}=await s.auth.getSession();if(n||!r?.user)return o.NextResponse.json({error:"Unauthorized: You must be logged in to assign roles."},{status:401});let a=r.user.id;if(!i)return o.NextResponse.json({error:"API Key ID is required"},{status:400});try{let{role_name:t}=await e.json();if(!t||"string"!=typeof t)return o.NextResponse.json({error:"Role name (role_id) is required and must be a string"},{status:400});let{data:r,error:n}=await s.from("api_keys").select(`
        custom_api_config_id,
        custom_api_configs ( user_id )
      `).eq("id",i).single();if(n||!r)return o.NextResponse.json({error:"API Key not found or failed to fetch its details"},{status:404});let c=r.custom_api_configs?.user_id;if(!c)return o.NextResponse.json({error:"Could not determine the config owner for the API Key."},{status:500});if(c&&a!==c)return o.NextResponse.json({error:"Forbidden. You do not own the configuration this API key belongs to."},{status:403});let{data:l}=await s.from("subscriptions").select("tier").eq("user_id",a).eq("status","active").single(),p=l?.tier||"free";if(!(0,d.Nu)(p,"custom_roles"))return o.NextResponse.json({error:`Role assignment is not available on the ${p} plan. Please upgrade to assign roles to your API keys.`},{status:403});let g=u.p2.some(e=>e.id===t),m=!1;if(!g){let{data:e,error:i}=await s.from("user_custom_roles").select("id").eq("user_id",a).eq("role_id",t).maybeSingle();if(i)return o.NextResponse.json({error:"Error validating role.",details:i.message},{status:500});e&&(m=!0)}if(!g&&!m)return o.NextResponse.json({error:`Invalid role_name: ${t}. Not a predefined role or a custom role you own.`},{status:400});let{custom_api_config_id:_}=r,{data:I,error:f}=await s.from("api_key_role_assignments").insert({api_key_id:i,custom_api_config_id:_,role_name:t}).select().single();if(f){if("23505"===f.code){if(f.message.includes("unique_api_key_role"))return o.NextResponse.json({error:"This API key already has this role assigned.",details:f.message},{status:409});if(f.message.includes("unique_role_per_custom_config"))return o.NextResponse.json({error:"This role is already assigned to another API key in this Custom Model (config). Check unique_role_per_custom_config constraint.",details:f.message},{status:409});return o.NextResponse.json({error:"Failed to assign role: This role may already be assigned in a way that violates a uniqueness constraint.",details:f.message,code:f.code},{status:409})}return o.NextResponse.json({error:"Failed to assign role to API key",details:f.message},{status:500})}return o.NextResponse.json(I,{status:201})}catch(e){if("SyntaxError"===e.name)return o.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});return o.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}let g=new r.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/keys/[apiKeyId]/roles/route",pathname:"/api/keys/[apiKeyId]/roles",filename:"route",bundlePath:"app/api/keys/[apiKeyId]/roles/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\keys\\[apiKeyId]\\roles\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:m,workUnitAsyncStorage:_,serverHooks:I}=g;function f(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:_})}},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94473:(e,t,s)=>{"use strict";s.d(t,{Dm:()=>r,Lj:()=>i,Zu:()=>n});let i={publishableKey:process.env.STRIPE_LIVE_PUBLISHABLE_KEY,secretKey:process.env.STRIPE_LIVE_SECRET_KEY,webhookSecret:process.env.STRIPE_LIVE_WEBHOOK_SECRET},r={FREE:process.env.STRIPE_LIVE_FREE_PRICE_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRICE_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID},n={FREE:process.env.STRIPE_LIVE_FREE_PRODUCT_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRODUCT_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID};process.env.STRIPE_LIVE_FREE_PRICE_ID,process.env.STRIPE_LIVE_STARTER_PRICE_ID,process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID,i.publishableKey&&i.publishableKey.substring(0,20),i.secretKey&&i.secretKey.substring(0,20),i.webhookSecret&&i.webhookSecret.substring(0,15)},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),i=t.X(0,[4447,580,9398,3410],()=>s(49722));module.exports=i})();