"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/HeroSection.tsx":
/*!************************************************!*\
  !*** ./src/components/landing/HeroSection.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/InstantLink */ \"(app-pages-browser)/./src/components/ui/InstantLink.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction HeroSection() {\n    _s();\n    const [activeFeatureCard, setActiveFeatureCard] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('routing');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative min-h-screen overflow-hidden\",\n                style: {\n                    backgroundImage: 'url(/Hero_Section_Background_Image.png)',\n                    backgroundSize: 'cover',\n                    backgroundPosition: 'center center',\n                    backgroundRepeat: 'no-repeat',\n                    backgroundAttachment: 'fixed'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-b from-black/60 via-black/40 to-black/60 md:bg-black/40\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.008)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.008)_1px,transparent_1px)] bg-[size:50px_50px] hidden md:block\",\n                        style: {\n                            boxShadow: 'inset 0 0 100px rgba(255,255,255,0.02)'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center min-h-screen py-8 lg:py-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.4\n                                    },\n                                    className: \"text-left order-2 lg:order-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight\",\n                                            children: [\n                                                \"Universal AI \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600\",\n                                                    children: \"gateway\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 43,\n                                                    columnNumber: 30\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 44,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600\",\n                                                    children: \"with your own keys\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm sm:text-base md:text-lg text-gray-300 mb-6 lg:mb-8 leading-relaxed max-w-lg\",\n                                            children: \"Connect to 50+ AI providers using your own API keys. Smart routing, fallback protection, and cost optimization. One unified interface for OpenAI, Claude, Gemini, and more - all with complete control over your data and costs.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 15\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.3,\n                                                delay: 0.2\n                                            },\n                                            className: \"flex flex-col sm:flex-row gap-3 sm:gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    href: \"/pricing\",\n                                                    className: \"inline-flex items-center px-6 py-3 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-orange-500/25\",\n                                                    children: \"Get started for free\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    href: \"/contact\",\n                                                    className: \"inline-flex items-center px-6 py-3 border border-white/20 text-white font-medium rounded-lg hover:bg-white/10 transition-all duration-200\",\n                                                    children: \"Talk to sales\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 15\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.3,\n                                                delay: 0.4\n                                            },\n                                            className: \"mt-6 flex justify-start\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"https://www.producthunt.com/products/roukey?embed=true&utm_source=badge-featured&utm_medium=badge&utm_source=badge-roukey\",\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"hover:scale-105 transition-transform duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: \"https://api.producthunt.com/widgets/embed-image/v1/featured.svg?post_id=993580&theme=light&t=1752770701168\",\n                                                    alt: \"RouKey - Route each task to the smartest AI for the job | Product Hunt\",\n                                                    style: {\n                                                        width: '250px',\n                                                        height: '54px'\n                                                    },\n                                                    width: \"250\",\n                                                    height: \"54\",\n                                                    className: \"rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.4,\n                                        delay: 0.2\n                                    },\n                                    className: \"relative flex justify-center items-center order-1 lg:order-2 mb-8 lg:mb-0 lg:mt-64\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl sm:text-5xl md:text-6xl lg:text-8xl xl:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-br from-yellow-400 via-orange-400 via-orange-500 to-orange-600 opacity-95 drop-shadow-2xl\",\n                                                children: \"⚡\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 text-4xl sm:text-5xl md:text-6xl lg:text-8xl xl:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-br from-yellow-200 via-orange-300 to-transparent opacity-40\",\n                                                children: \"⚡\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 text-4xl sm:text-5xl md:text-6xl lg:text-8xl xl:text-9xl font-bold text-orange-400/50 blur-xl lg:blur-2xl\",\n                                                children: \"⚡\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 text-4xl sm:text-5xl md:text-6xl lg:text-8xl xl:text-9xl font-bold text-yellow-400/45 blur-sm lg:blur-lg\",\n                                                children: \"⚡\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 text-4xl sm:text-5xl md:text-6xl lg:text-8xl xl:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-transparent via-yellow-300 to-transparent opacity-30 animate-pulse\",\n                                                children: \"⚡\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-20\",\n                style: {\n                    background: \"radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                backgroundImage: \"\\n                linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),\\n                linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px)\\n              \",\n                                backgroundSize: '50px 50px'\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-white mb-4\",\n                                        children: \"Stop switching between AI tools\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-300 max-w-2xl mx-auto\",\n                                        children: \"One API call. Any model. Your keys, your control, your costs.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative max-w-4xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-orange-500/20 via-purple-500/10 to-orange-500/20 rounded-2xl blur-2xl scale-105\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative bg-white/5 backdrop-blur-xl rounded-2xl border border-white/10 shadow-2xl overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-white/5 rounded-3xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 md:grid-cols-5 gap-2 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setActiveFeatureCard('routing'),\n                                                            className: \"group relative p-3 rounded-lg border transition-all duration-300 text-left overflow-hidden \".concat(activeFeatureCard === 'routing' ? 'bg-gradient-to-br from-orange-500/20 to-orange-600/10 border-orange-400/50 text-white shadow-lg shadow-orange-500/20 scale-105' : 'bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20 hover:scale-102'),\n                                                            children: [\n                                                                activeFeatureCard === 'routing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-br from-orange-500/20 to-transparent rounded-lg blur-lg\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 195,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center mb-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-5 h-5 rounded flex items-center justify-center mr-2 text-xs \".concat(activeFeatureCard === 'routing' ? 'bg-orange-500/30 text-orange-300' : 'bg-white/10 text-gray-400 group-hover:bg-white/20'),\n                                                                                    children: \"\\uD83C\\uDFAF\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 200,\n                                                                                    columnNumber: 23\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs font-semibold\",\n                                                                                    children: \"Model Routing\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 207,\n                                                                                    columnNumber: 23\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                            lineNumber: 199,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-400 ml-7\",\n                                                                            children: \"Smart selection\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                            lineNumber: 209,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 198,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setActiveFeatureCard('orchestration'),\n                                                            className: \"group relative p-3 rounded-lg border transition-all duration-300 text-left overflow-hidden \".concat(activeFeatureCard === 'orchestration' ? 'bg-gradient-to-br from-orange-500/20 to-orange-600/10 border-orange-400/50 text-white shadow-lg shadow-orange-500/20 scale-105' : 'bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20 hover:scale-102'),\n                                                            children: [\n                                                                activeFeatureCard === 'orchestration' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-br from-orange-500/20 to-transparent rounded-lg blur-lg\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 226,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center mb-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-5 h-5 rounded flex items-center justify-center mr-2 text-xs \".concat(activeFeatureCard === 'orchestration' ? 'bg-orange-500/30 text-orange-300' : 'bg-white/10 text-gray-400 group-hover:bg-white/20'),\n                                                                                    children: \"\\uD83D\\uDD17\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 231,\n                                                                                    columnNumber: 23\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs font-semibold\",\n                                                                                    children: \"Multi-Role\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 238,\n                                                                                    columnNumber: 23\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                            lineNumber: 230,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-400 ml-7\",\n                                                                            children: \"Chain AI roles\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                            lineNumber: 240,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 229,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setActiveFeatureCard('api'),\n                                                            className: \"group relative p-3 rounded-lg border transition-all duration-300 text-left overflow-hidden \".concat(activeFeatureCard === 'api' ? 'bg-gradient-to-br from-orange-500/20 to-orange-600/10 border-orange-400/50 text-white shadow-lg shadow-orange-500/20 scale-105' : 'bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20 hover:scale-102'),\n                                                            children: [\n                                                                activeFeatureCard === 'api' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-br from-orange-500/20 to-transparent rounded-lg blur-lg\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 257,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center mb-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-5 h-5 rounded flex items-center justify-center mr-2 text-xs \".concat(activeFeatureCard === 'api' ? 'bg-orange-500/30 text-orange-300' : 'bg-white/10 text-gray-400 group-hover:bg-white/20'),\n                                                                                    children: \"\\uD83D\\uDCBB\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 262,\n                                                                                    columnNumber: 23\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs font-semibold\",\n                                                                                    children: \"API Usage\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 269,\n                                                                                    columnNumber: 23\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                            lineNumber: 261,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-400 ml-7\",\n                                                                            children: \"Real code\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                            lineNumber: 271,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setActiveFeatureCard('costs'),\n                                                            className: \"group relative p-3 rounded-lg border transition-all duration-300 text-left overflow-hidden \".concat(activeFeatureCard === 'costs' ? 'bg-gradient-to-br from-orange-500/20 to-orange-600/10 border-orange-400/50 text-white shadow-lg shadow-orange-500/20 scale-105' : 'bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20 hover:scale-102'),\n                                                            children: [\n                                                                activeFeatureCard === 'costs' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-br from-orange-500/20 to-transparent rounded-lg blur-lg\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 288,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center mb-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-5 h-5 rounded flex items-center justify-center mr-2 text-xs \".concat(activeFeatureCard === 'costs' ? 'bg-orange-500/30 text-orange-300' : 'bg-white/10 text-gray-400 group-hover:bg-white/20'),\n                                                                                    children: \"\\uD83D\\uDCB0\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 293,\n                                                                                    columnNumber: 23\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs font-semibold\",\n                                                                                    children: \"Cost Savings\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 300,\n                                                                                    columnNumber: 23\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                            lineNumber: 292,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-400 ml-7\",\n                                                                            children: \"Save money\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                            lineNumber: 302,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 291,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setActiveFeatureCard('demo'),\n                                                            className: \"group relative p-3 rounded-lg border transition-all duration-300 text-left overflow-hidden \".concat(activeFeatureCard === 'demo' ? 'bg-gradient-to-br from-orange-500/20 to-orange-600/10 border-orange-400/50 text-white shadow-lg shadow-orange-500/20 scale-105' : 'bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20 hover:scale-102'),\n                                                            children: [\n                                                                activeFeatureCard === 'demo' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-br from-orange-500/20 to-transparent rounded-lg blur-lg\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center mb-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-5 h-5 rounded flex items-center justify-center mr-2 text-xs \".concat(activeFeatureCard === 'demo' ? 'bg-orange-500/30 text-orange-300' : 'bg-white/10 text-gray-400 group-hover:bg-white/20'),\n                                                                                    children: \"▶️\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 324,\n                                                                                    columnNumber: 23\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs font-semibold\",\n                                                                                    children: \"Live Demo\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 331,\n                                                                                    columnNumber: 23\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                            lineNumber: 323,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-400 ml-7\",\n                                                                            children: \"Watch video\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                            lineNumber: 333,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative mt-3 p-1 bg-black/20 rounded-xl border border-white/5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-[3/2] w-full\",\n                                                    children: [\n                                                        activeFeatureCard === 'routing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full h-full relative\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                src: \"/Example_1.png\",\n                                                                alt: \"RouKey Model Routing Example\",\n                                                                width: 1536,\n                                                                height: 1024,\n                                                                className: \"w-full h-full object-contain rounded-lg\",\n                                                                quality: 95,\n                                                                priority: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        activeFeatureCard === 'api' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full h-full bg-gray-900 rounded-lg border border-gray-700 p-6 overflow-auto\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-mono text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-green-400 mb-4\",\n                                                                        children: \"# RouKey API Usage Example\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 360,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-300 mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-blue-400\",\n                                                                                children: \"import\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                lineNumber: 362,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \" requests\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-300 mb-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-blue-400\",\n                                                                                children: \"import\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                lineNumber: 365,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \" json\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 364,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-300 mb-2\",\n                                                                        children: [\n                                                                            \"url = \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-yellow-300\",\n                                                                                children: '\"https://api.roukey.online/v1/chat/completions\"'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                lineNumber: 368,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 367,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-300 mb-2\",\n                                                                        children: [\n                                                                            \"headers = \",\n                                                                            \"{\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 370,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-300 mb-2 ml-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-yellow-300\",\n                                                                                children: '\"X-API-Key\"'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                lineNumber: 374,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \": \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-yellow-300\",\n                                                                                children: '\"your-roukey-api-key\"'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                lineNumber: 374,\n                                                                                columnNumber: 81\n                                                                            }, this),\n                                                                            \",\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 373,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-300 mb-2 ml-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-yellow-300\",\n                                                                                children: '\"Content-Type\"'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                lineNumber: 377,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \": \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-yellow-300\",\n                                                                                children: '\"application/json\"'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                lineNumber: 377,\n                                                                                columnNumber: 84\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-300 mb-4\",\n                                                                        children: \"}\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 379,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-300 mb-2\",\n                                                                        children: [\n                                                                            \"data = \",\n                                                                            \"{\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-300 mb-2 ml-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-yellow-300\",\n                                                                                children: '\"model\"'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                lineNumber: 384,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \": \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-yellow-300\",\n                                                                                children: '\"gpt-4\"'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                lineNumber: 384,\n                                                                                columnNumber: 77\n                                                                            }, this),\n                                                                            \",\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-300 mb-2 ml-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-yellow-300\",\n                                                                                children: '\"messages\"'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                lineNumber: 387,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \": [\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 386,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-300 mb-2 ml-8\",\n                                                                        children: \"{\\\"role\\\": \\\"user\\\", \\\"content\\\": \\\"Analyze this data\\\"}\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 389,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-300 mb-2 ml-4\",\n                                                                        children: \"]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 392,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-300 mb-4\",\n                                                                        children: \"}\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 393,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-300\",\n                                                                        children: \"response = requests.post(url, headers=headers, json=data)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 394,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        activeFeatureCard === 'orchestration' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full h-full bg-gradient-to-br from-white/5 to-white/10 rounded-lg border border-white/10 overflow-hidden backdrop-blur-sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                src: \"/Multi_Role_Orchastration.png\",\n                                                                alt: \"Multi-Role Orchestration Interface\",\n                                                                width: 800,\n                                                                height: 500,\n                                                                className: \"w-full h-full object-cover rounded-lg\",\n                                                                priority: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        activeFeatureCard === 'costs' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full h-full bg-gradient-to-br from-white/5 to-white/10 rounded-lg border border-white/10 overflow-hidden backdrop-blur-sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                src: \"/Cost_Savings.png\",\n                                                                alt: \"Cost Savings Dashboard Interface\",\n                                                                width: 800,\n                                                                height: 500,\n                                                                className: \"w-full h-full object-cover rounded-lg\",\n                                                                priority: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        activeFeatureCard === 'demo' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full h-full bg-gradient-to-br from-white/5 to-white/10 rounded-lg border border-white/10 backdrop-blur-sm overflow-hidden\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full h-full\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        position: 'relative',\n                                                                        paddingBottom: 'calc(45.27777777777778% + 41px)',\n                                                                        height: 0,\n                                                                        width: '100%'\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                                                        src: \"https://demo.arcade.software/vqf2O3JqMzbE89Lkrmtm?embed&embed_mobile=tab&embed_desktop=inline&show_copy_link=true\",\n                                                                        title: \"Configure Intelligent Role-Based API Routing for Custom Models\",\n                                                                        frameBorder: \"0\",\n                                                                        loading: \"lazy\",\n                                                                        allowFullScreen: true,\n                                                                        allow: \"clipboard-write\",\n                                                                        style: {\n                                                                            position: 'absolute',\n                                                                            top: 0,\n                                                                            left: 0,\n                                                                            width: '100%',\n                                                                            height: '100%',\n                                                                            colorScheme: 'light',\n                                                                            borderRadius: '8px'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 429,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 428,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(HeroSection, \"DD4hpsX5UOe1eTSBbII1ThmA1X0=\");\n_c = HeroSection;\nvar _c;\n$RefreshReg$(_c, \"HeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/HeroSection.tsx\n"));

/***/ })

});