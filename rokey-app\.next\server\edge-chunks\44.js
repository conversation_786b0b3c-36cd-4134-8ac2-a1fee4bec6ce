"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[44],{2710:(e,t,r)=>{r(6237),r(4318),r(4515),r(424),r(6937),r(4319),r(252);var n=r(2438);r(7223);let o=new WeakMap,i=(0,n.I)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})});function a(){return this.getAll().map(e=>[e.name,e]).values()}function s(e){for(let e of this.getAll())this.delete(e.name);return e}r(6464);let l=new WeakMap,c=(0,n.I)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})});function u(){let e=workAsyncStorage.getStore(),t=workUnitAsyncStorage.getStore();switch((!e||!t)&&throwForMissingRequestStore("draftMode"),t.type){case"request":return d(t.draftMode,e);case"cache":case"unstable-cache":let r=getDraftModeProviderForCacheScope(e,t);if(r)return d(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return p(null);default:return t}}function d(e,t){let r,n=f.get(u);return n||(r=p(e),f.set(e,r),r)}r(7);let f=new WeakMap;function p(e){let t=new h(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class h{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){m("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){m("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let g=(0,n.I)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function m(e){let t=workAsyncStorage.getStore(),r=workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});abortAndThrowOnSynchronousRequestDataAccess(t.route,e,n,r)}else if("prerender-ppr"===r.type)postponeWithTracking(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}},3339:(e,t,r)=>{r.d(t,{createServerClient:()=>y});var n=r(8739);function o(){return"undefined"!=typeof window&&void 0!==window.document}let i={path:"/",sameSite:"lax",httpOnly:!1,maxAge:3456e4},a=/^(.*)[.](0|[1-9][0-9]*)$/;function s(e,t){if(e===t)return!0;let r=e.match(a);return!!r&&r[1]===t}function l(e,t,r){let n=r??3180,o=encodeURIComponent(t);if(o.length<=n)return[{name:e,value:t}];let i=[];for(;o.length>0;){let e=o.slice(0,n),t=e.lastIndexOf("%");t>n-3&&(e=e.slice(0,t));let r="";for(;e.length>0;)try{r=decodeURIComponent(e);break}catch(t){if(t instanceof URIError&&"%"===e.at(-3)&&e.length>3)e=e.slice(0,e.length-3);else throw t}i.push(r),o=o.slice(e.length)}return i.map((t,r)=>({name:`${e}.${r}`,value:t}))}async function c(e,t){let r=await t(e);if(r)return r;let n=[];for(let r=0;;r++){let o=`${e}.${r}`,i=await t(o);if(!i)break;n.push(i)}return n.length>0?n.join(""):null}let u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),d=" 	\n\r=".split(""),f=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<d.length;t+=1)e[d[t].charCodeAt(0)]=-2;for(let t=0;t<u.length;t+=1)e[u[t].charCodeAt(0)]=t;return e})();function p(e){let t=[],r=0,n=0;if(function(e,t){for(let r=0;r<e.length;r+=1){let n=e.charCodeAt(r);if(n>55295&&n<=56319){let t=(n-55296)*1024&65535;n=(e.charCodeAt(r+1)-56320&65535|t)+65536,r+=1}!function(e,t){if(e<=127)return t(e);if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(n,t)}}(e,e=>{for(r=r<<8|e,n+=8;n>=6;){let e=r>>n-6&63;t.push(u[e]),n-=6}}),n>0)for(r<<=6-n,n=6;n>=6;){let e=r>>n-6&63;t.push(u[e]),n-=6}return t.join("")}function h(e){let t=[],r=e=>{t.push(String.fromCodePoint(e))},n={utf8seq:0,codepoint:0},o=0,i=0;for(let t=0;t<e.length;t+=1){let a=f[e.charCodeAt(t)];if(a>-1)for(o=o<<6|a,i+=6;i>=8;)(function(e,t,r){if(0===t.utf8seq){if(e<=127)return r(e);for(let r=1;r<6;r+=1)if((e>>7-r&1)==0){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}})(o>>i-8&255,n,r),i-=8;else if(-2===a)continue;else throw Error(`Invalid Base64-URL character "${e.at(t)}" at position ${t}`)}return t.join("")}let g="base64-";async function m({getAll:e,setAll:t,setItems:r,removedItems:n},o){let a=o.cookieEncoding,c=o.cookieOptions??null,u=await e([...r?Object.keys(r):[],...n?Object.keys(n):[]]),d=u?.map(({name:e})=>e)||[],f=Object.keys(n).flatMap(e=>d.filter(t=>s(t,e))),h=Object.keys(r).flatMap(e=>{let t=new Set(d.filter(t=>s(t,e))),n=r[e];"base64url"===a&&(n=g+p(n));let o=l(e,n);return o.forEach(e=>{t.delete(e.name)}),f.push(...t),o}),m={...i,...c,maxAge:0},b={...i,...c,maxAge:i.maxAge};delete m.name,delete b.name,await t([...f.map(e=>({name:e,value:"",options:m})),...h.map(({name:e,value:t})=>({name:e,value:t,options:b}))])}var b=r(918);function y(e,t,r){if(!e||!t)throw Error(`Your project's URL and Key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let{storage:a,getAll:u,setAll:d,setItems:f,removedItems:y}=function(e,t){let r,a,u=e.cookies??null,d=e.cookieEncoding,f={},b={};if(u)if("get"in u){let e=async e=>{let t=e.flatMap(e=>[e,...Array.from({length:5}).map((t,r)=>`${e}.${r}`)]),r=[];for(let e=0;e<t.length;e+=1){let n=await u.get(t[e]);(n||"string"==typeof n)&&r.push({name:t[e],value:n})}return r};if(r=async t=>await e(t),"set"in u&&"remove"in u)a=async e=>{for(let t=0;t<e.length;t+=1){let{name:r,value:n,options:o}=e[t];n?await u.set(r,n,o):await u.remove(r,o)}};else if(t)a=async()=>{console.warn("@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)")}else if("getAll"in u)if(r=async()=>await u.getAll(),"setAll"in u)a=u.setAll;else if(t)a=async()=>{console.warn("@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)");else throw Error(`@supabase/ssr: ${t?"createServerClient":"createBrowserClient"} requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).${o()?" As this is called in a browser runtime, consider removing the cookies option object to use the document.cookie API automatically.":""}`);else if(!t&&o()){let e=()=>{let e=(0,n.qg)(document.cookie);return Object.keys(e).map(t=>({name:t,value:e[t]??""}))};r=()=>e(),a=e=>{e.forEach(({name:e,value:t,options:r})=>{document.cookie=(0,n.lK)(e,t,r)})}}else if(t)throw Error("@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)");else r=()=>[],a=()=>{throw Error("@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed")};return t?{getAll:r,setAll:a,setItems:f,removedItems:b,storage:{isServer:!0,getItem:async e=>{if("string"==typeof f[e])return f[e];if(b[e])return null;let t=await r([e]),n=await c(e,async e=>{let r=t?.find(({name:t})=>t===e)||null;return r?r.value:null});if(!n)return null;let o=n;return"string"==typeof n&&n.startsWith(g)&&(o=h(n.substring(g.length))),o},setItem:async(t,n)=>{t.endsWith("-code-verifier")&&await m({getAll:r,setAll:a,setItems:{[t]:n},removedItems:{}},{cookieOptions:e?.cookieOptions??null,cookieEncoding:d}),f[t]=n,delete b[t]},removeItem:async e=>{delete f[e],b[e]=!0}}}:{getAll:r,setAll:a,setItems:f,removedItems:b,storage:{isServer:!1,getItem:async e=>{let t=await r([e]),n=await c(e,async e=>{let r=t?.find(({name:t})=>t===e)||null;return r?r.value:null});if(!n)return null;let o=n;return n.startsWith(g)&&(o=h(n.substring(g.length))),o},setItem:async(t,n)=>{let o=await r([t]),c=new Set((o?.map(({name:e})=>e)||[]).filter(e=>s(e,t))),u=n;"base64url"===d&&(u=g+p(n));let f=l(t,u);f.forEach(({name:e})=>{c.delete(e)});let h={...i,...e?.cookieOptions,maxAge:0},m={...i,...e?.cookieOptions,maxAge:i.maxAge};delete h.name,delete m.name;let b=[...[...c].map(e=>({name:e,value:"",options:h})),...f.map(({name:e,value:t})=>({name:e,value:t,options:m}))];b.length>0&&await a(b)},removeItem:async t=>{let n=await r([t]),o=(n?.map(({name:e})=>e)||[]).filter(e=>s(e,t)),l={...i,...e?.cookieOptions,maxAge:0};delete l.name,o.length>0&&await a(o.map(e=>({name:e,value:"",options:l})))}}}}({...r,cookieEncoding:r?.cookieEncoding??"base64url"},!0),w=(0,b.UU)(e,t,{...r,global:{...r?.global,headers:{...r?.global?.headers,"X-Client-Info":"supabase-ssr/0.6.1 createServerClient"}},auth:{...r?.cookieOptions?.name?{storageKey:r.cookieOptions.name}:null,...r?.auth,flowType:"pkce",autoRefreshToken:!1,detectSessionInUrl:!1,persistSession:!0,storage:a}});return w.auth.onAuthStateChange(async e=>{(Object.keys(f).length>0||Object.keys(y).length>0)&&("SIGNED_IN"===e||"TOKEN_REFRESHED"===e||"USER_UPDATED"===e||"PASSWORD_RECOVERY"===e||"SIGNED_OUT"===e||"MFA_CHALLENGE_VERIFIED"===e)&&await m({getAll:u,setAll:d,setItems:f,removedItems:y},{cookieOptions:r?.cookieOptions??null,cookieEncoding:r?.cookieEncoding??"base64url"})}),w}},8739:(e,t)=>{t.qg=function(e,t){let r=new s,n=e.length;if(n<2)return r;let o=t?.decode||u,i=0;do{let t=e.indexOf("=",i);if(-1===t)break;let a=e.indexOf(";",i),s=-1===a?n:a;if(t>s){i=e.lastIndexOf(";",t-1)+1;continue}let u=l(e,i,t),d=c(e,t,u),f=e.slice(u,d);if(void 0===r[f]){let n=l(e,t+1,s),i=c(e,s,n),a=o(e.slice(n,i));r[f]=a}i=s+1}while(i<n);return r},t.lK=function(e,t,s){let l=s?.encode||encodeURIComponent;if(!r.test(e))throw TypeError(`argument name is invalid: ${e}`);let c=l(t);if(!n.test(c))throw TypeError(`argument val is invalid: ${t}`);let u=e+"="+c;if(!s)return u;if(void 0!==s.maxAge){if(!Number.isInteger(s.maxAge))throw TypeError(`option maxAge is invalid: ${s.maxAge}`);u+="; Max-Age="+s.maxAge}if(s.domain){if(!o.test(s.domain))throw TypeError(`option domain is invalid: ${s.domain}`);u+="; Domain="+s.domain}if(s.path){if(!i.test(s.path))throw TypeError(`option path is invalid: ${s.path}`);u+="; Path="+s.path}if(s.expires){var d;if(d=s.expires,"[object Date]"!==a.call(d)||!Number.isFinite(s.expires.valueOf()))throw TypeError(`option expires is invalid: ${s.expires}`);u+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(u+="; HttpOnly"),s.secure&&(u+="; Secure"),s.partitioned&&(u+="; Partitioned"),s.priority)switch("string"==typeof s.priority?s.priority.toLowerCase():void 0){case"low":u+="; Priority=Low";break;case"medium":u+="; Priority=Medium";break;case"high":u+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${s.priority}`)}if(s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":u+="; SameSite=Strict";break;case"lax":u+="; SameSite=Lax";break;case"none":u+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${s.sameSite}`)}return u};let r=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,n=/^[\u0021-\u003A\u003C-\u007E]*$/,o=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,i=/^[\u0020-\u003A\u003D-\u007E]*$/,a=Object.prototype.toString,s=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function l(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function c(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function u(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}}}]);
//# sourceMappingURL=44.js.map