{"version": 3, "file": "app/api/external/v1/docs/route.js", "mappings": "gJEAA,sQFGO,IAAMA,EAAU,OAGhB,eAAeC,EAAIC,CAAoB,EAG5C,IAAMC,EAAc,CAClBC,QAAS,QACTC,KAAM,CACJC,MAAO,sBACPC,YAAa,qKACbC,QAAS,QACTC,QAAS,CACPC,KAAM,iBACNC,MAAO,wBACPC,IAAK,uBACP,EACAC,QAAS,CACPH,KAAM,qBACNE,IAAK,+BACP,CACF,EACAE,QAAS,CACP,CACEF,IAAK,GAAGG,QAAQ,gBAAgB,CAAC,YACjCR,YAAa,mBACf,EACD,CACDS,SAAU,CACR,CACEC,WAAY,EAAE,EAEjB,CACDC,WAAY,CACVC,gBAAiB,CACfF,WAAY,CACVG,KAAM,OACNC,OAAQ,SACRC,aAAc,UACdf,YAAa,qDACf,CACF,EACAgB,QAAS,CACPC,MAAO,CACLJ,KAAM,SACNK,WAAY,CACVC,MAAO,CACLN,KAAM,SACNK,WAAY,CACVE,QAAS,CAAEP,KAAM,QAAS,EAC1BA,KAAM,CAAEA,KAAM,QAAS,EACvBQ,KAAM,CAAER,KAAM,QAAS,EACvBS,QAAS,CAAET,KAAM,QAASU,MAAO,CAAEV,KAAM,QAAS,CAAE,CACtD,CACF,CACF,CACF,EACAW,OAAQ,CACNX,KAAM,SACNK,WAAY,CACVO,GAAI,CAAEZ,KAAM,SAAUa,OAAQ,MAAO,EACrCC,OAAQ,CAAEd,KAAM,SAAUe,KAAM,CAAC,SAAS,EAC1CzB,KAAM,CAAEU,KAAM,QAAS,EACvBb,YAAa,CAAEa,KAAM,QAAS,EAC9BgB,iBAAkB,CAAEhB,KAAM,SAAUe,KAAM,CAAC,iBAAkB,eAAgB,aAAa,EAC1FE,SAAU,CAAEjB,KAAM,QAAS,EAC3BkB,WAAY,CAAElB,KAAM,SAAUa,OAAQ,WAAY,EAClDM,WAAY,CAAEnB,KAAM,SAAUa,OAAQ,WAAY,CACpD,CACF,EACAO,YAAa,CACXpB,KAAM,SACNK,WAAY,CACVO,GAAI,CAAEZ,KAAM,SAAUa,OAAQ,MAAO,EACrCC,OAAQ,CAAEd,KAAM,SAAUe,KAAM,CAAC,eAAgB,EACjDM,SAAU,CAAErB,KAAM,QAAS,EAC3BsB,MAAO,CAAEtB,KAAM,QAAS,EACxBuB,OAAQ,CAAEvB,KAAM,SAAUe,KAAM,CAAC,SAAU,WAAW,EACtDS,8BAA+B,CAAExB,KAAM,SAAU,EACjDyB,YAAa,CAAEzB,KAAM,SAAU0B,QAAS,EAAGC,QAAS,CAAE,EACtDT,WAAY,CAAElB,KAAM,SAAUa,OAAQ,WAAY,EAClDM,WAAY,CAAEnB,KAAM,SAAUa,OAAQ,WAAY,CACpD,CACF,EACAe,MAAO,CACL5B,KAAM,SACNK,WAAY,CACVO,GAAI,CAAEZ,KAAM,QAAS,EACrBc,OAAQ,CAAEd,KAAM,SAAUe,KAAM,CAAC,QAAS,EAC1Cc,QAAS,CAAE7B,KAAM,SAAU,EAC3B8B,SAAU,CAAE9B,KAAM,QAAS,EAC3B+B,iBAAkB,CAChB/B,KAAM,SACNK,WAAY,CACV2B,aAAc,CAAEhC,KAAM,QAAS,EAC/Bb,YAAa,CAAEa,KAAM,QAAS,EAC9BiC,OAAQ,CAAEjC,KAAM,QAAS,EACzBkC,eAAgB,CAAElC,KAAM,SAAU,EAClCmC,SAAU,CAAEnC,KAAM,QAAS,EAC3BqB,SAAU,CAAErB,KAAM,QAAS,CAC7B,CACF,CACF,CACF,CACF,CACF,EACAoC,MAAO,CACL,oBAAqB,CACnBC,KAAM,CACJC,QAAS,yBACTnD,YAAa,8DACboD,KAAM,CAAC,OAAO,CACdC,YAAa,CACXC,UAAU,EACVC,QAAS,CACP,mBAAoB,CAClBC,OAAQ,CACN3C,KAAM,SACNyC,SAAU,CAAC,WAAW,CACtBpC,WAAY,CACVuC,MAAO,CAAE5C,KAAM,SAAU6C,QAAS,eAAgB,EAClDC,SAAU,CACR9C,KAAM,QACNU,MAAO,CACLV,KAAM,SACNK,WAAY,CACV0C,KAAM,CAAE/C,KAAM,SAAUe,KAAM,CAAC,OAAQ,YAAa,SAAS,EAC7D2B,QAAS,CAAE1C,KAAM,QAAS,CAC5B,CACF,CACF,EACAyB,YAAa,CAAEzB,KAAM,SAAU0B,QAAS,EAAGC,QAAS,CAAE,EACtDqB,WAAY,CAAEhD,KAAM,SAAU,EAC9BiD,OAAQ,CAAEjD,KAAM,SAAU,EAC1B+C,KAAM,CAAE/C,KAAM,SAAUb,YAAa,8CAA+C,CACtF,CACF,CACF,CACF,CACF,EACA+D,UAAW,CACT,IAAO,CACL/D,YAAa,wBACbuD,QAAS,CACP,mBAAoB,CAClBC,OAAQ,CACN3C,KAAM,SACNK,WAAY,CACVO,GAAI,CAAEZ,KAAM,QAAS,EACrBc,OAAQ,CAAEd,KAAM,QAAS,EACzB6B,QAAS,CAAE7B,KAAM,SAAU,EAC3B4C,MAAO,CAAE5C,KAAM,QAAS,EACxBmD,QAAS,CAAEnD,KAAM,OAAQ,EACzBoD,MAAO,CAAEpD,KAAM,QAAS,CAC1B,CACF,CACF,CACF,CACF,CACF,CACF,CACF,EACA,WAAY,CACVqD,IAAK,CACHf,QAAS,sBACTnD,YAAa,qCACboD,KAAM,CAAC,2BAA2B,CAClCW,UAAW,CACT,IAAO,CACL/D,YAAa,yBACbuD,QAAS,CACP,mBAAoB,CAClBC,OAAQ,CACN3C,KAAM,SACNK,WAAY,CACVS,OAAQ,CAAEd,KAAM,SAAUe,KAAM,CAAC,OAAO,EACxCuC,KAAM,CACJtD,KAAM,QACNU,MAAO,CAAE6C,KAAM,6BAA8B,CAC/C,CACF,CACF,CACF,CACF,CACF,CACF,CACF,EACAlB,KAAM,CACJC,QAAS,uBACTnD,YAAa,oCACboD,KAAM,CAAC,2BAA2B,CAClCC,YAAa,CACXC,SAAU,GACVC,QAAS,CACP,mBAAoB,CAClBC,OAAQ,CACN3C,KAAM,SACNyC,SAAU,CAAC,OAAO,CAClBpC,WAAY,CACVf,KAAM,CAAEU,KAAM,SAAUwD,UAAW,GAAI,EACvCrE,YAAa,CAAEa,KAAM,SAAUwD,UAAW,GAAI,EAC9CxC,iBAAkB,CAAEhB,KAAM,SAAUe,KAAM,CAAC,iBAAkB,eAAgB,aAAa,EAC1FE,SAAU,CAAEjB,KAAM,QAAS,CAC7B,CACF,CACF,CACF,CACF,EACAkD,UAAW,CACT,IAAO,CACL/D,YAAa,wBACbuD,QAAS,CACP,mBAAoB,CAClBC,OAAQ,CAAEY,KAAM,6BAA8B,CAChD,CACF,CACF,CACF,CACF,CACF,EACA,sBAAuB,CACrBF,IAAK,CACHf,QAAS,oBACTnD,YAAa,qCACboD,KAAM,CAAC,2BAA2B,CAClCkB,WAAY,CACV,CACEnE,KAAM,WACNoE,GAAI,OACJjB,UAAU,EACVE,OAAQ,CAAE3C,KAAM,SAAUa,OAAQ,MAAO,CAC3C,EACD,CACDqC,UAAW,CACT,IAAO,CACL/D,YAAa,wBACbuD,QAAS,CACP,mBAAoB,CAClBC,OAAQ,CAAEY,KAAM,6BAA8B,CAChD,CACF,CACF,CACF,CACF,EACAI,IAAK,CACHrB,QAAS,uBACTnD,YAAa,mCACboD,KAAM,CAAC,2BAA2B,CAClCkB,WAAY,CACV,CACEnE,KAAM,WACNoE,GAAI,OACJjB,UAAU,EACVE,OAAQ,CAAE3C,KAAM,SAAUa,OAAQ,MAAO,CAC3C,EACD,CACD2B,YAAa,CACXC,UAAU,EACVC,QAAS,CACP,mBAAoB,CAClBC,OAAQ,CACN3C,KAAM,SACNK,WAAY,CACVf,KAAM,CAAEU,KAAM,SAAUwD,UAAW,GAAI,EACvCrE,YAAa,CAAEa,KAAM,SAAUwD,UAAW,GAAI,EAC9CxC,iBAAkB,CAAEhB,KAAM,SAAUe,KAAM,CAAC,iBAAkB,eAAgB,aAAa,EAC1FE,SAAU,CAAEjB,KAAM,QAAS,CAC7B,CACF,CACF,CACF,CACF,EACAkD,UAAW,CACT,IAAO,CACL/D,YAAa,wBACbuD,QAAS,CACP,mBAAoB,CAClBC,OAAQ,CAAEY,KAAM,6BAA8B,CAChD,CACF,CACF,CACF,CACF,EACAK,OAAQ,CACNtB,QAAS,uBACTnD,YAAa,qDACboD,KAAM,CAAC,2BAA2B,CAClCkB,WAAY,CACV,CACEnE,KAAM,WACNoE,GAAI,OACJjB,UAAU,EACVE,OAAQ,CAAE3C,KAAM,SAAUa,OAAQ,MAAO,CAC3C,EACD,CACDqC,UAAW,CACT,IAAO,CACL/D,YAAa,wBACbuD,QAAS,CACP,mBAAoB,CAClBC,OAAQ,CACN3C,KAAM,SACNK,WAAY,CACVO,GAAI,CAAEZ,KAAM,QAAS,EACrBc,OAAQ,CAAEd,KAAM,QAAS,EACzB6D,QAAS,CAAE7D,KAAM,SAAU,CAC7B,CACF,CACF,CACF,CACF,CACF,CACF,CACF,EACA,UAAW,CACTqD,IAAK,CACHf,QAAS,cACTnD,YAAa,kDACboD,KAAM,CAAC,qBAAqB,CAC5BW,UAAW,CACT,IAAO,CACL/D,YAAa,2BACbuD,QAAS,CACP,mBAAoB,CAClBC,OAAQ,CACN3C,KAAM,SACNK,WAAY,CACVS,OAAQ,CAAEd,KAAM,SAAUe,KAAM,CAAC,OAAO,EACxCuC,KAAM,CACJtD,KAAM,QACNU,MAAO,CAAE6C,KAAM,4BAA6B,CAC9C,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CACF,EACA,SAAU,CACRF,IAAK,CACHf,QAAS,sBACTnD,YAAa,mDACboD,KAAM,CAAC,YAAY,CACnBkB,WAAY,CACV,CACEnE,KAAM,aACNoE,GAAI,QACJf,OAAQ,CAAE3C,KAAM,SAAUa,OAAQ,WAAY,EAC9C1B,YAAa,6BACf,EACA,CACEG,KAAM,WACNoE,GAAI,QACJf,OAAQ,CAAE3C,KAAM,SAAUa,OAAQ,WAAY,EAC9C1B,YAAa,2BACf,EACA,CACEG,KAAM,cACNoE,GAAI,QACJf,OAAQ,CAAE3C,KAAM,SAAUe,KAAM,CAAC,OAAQ,MAAO,OAAQ,QAAQ,EAChE5B,YAAa,kCACf,EACD,CACD+D,UAAW,CACT,IAAO,CACL/D,YAAa,uBACbuD,QAAS,CACP,mBAAoB,CAClBC,OAAQ,CACN3C,KAAM,SACNK,WAAY,CACVS,OAAQ,CAAEd,KAAM,SAAUe,KAAM,CAAC,eAAe,EAChD+C,OAAQ,CAAE9D,KAAM,QAAS,EACzB+D,OAAQ,CAAE/D,KAAM,QAAS,EACzBgE,YAAa,CAAEhE,KAAM,OAAQ,EAC7BiE,WAAY,CAAEjE,KAAM,QAAS,CAC/B,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CACF,EACAuC,KAAM,CACJ,CACEjD,KAAM,OACNH,YAAa,6DACf,EACA,CACEG,KAAM,2BACNH,YAAa,mCACf,EACA,CACEG,KAAM,0BACNH,YAAa,4CACf,EACA,CACEG,KAAM,qBACNH,YAAa,kDACf,EACA,CACEG,KAAM,YACNH,YAAa,gCACf,EACA,CACEG,KAAM,UACNH,YAAa,0CACf,EACD,EAGH,OAAO+E,EAAAA,EAAYA,CAACC,IAAI,CAACpF,EAAa,CACpCqF,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,eAChC,+BAAgC,yCAChC,eAAgB,kBAClB,CACF,EACF,CAGO,eAAeC,IACpB,OAAO,IAAIH,EAAAA,EAAYA,CAAC,KAAM,CAC5B3C,OAAQ,IACR6C,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,eAChC,+BAAgC,wCAClC,CACF,EACF,CC/aA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,mCACA,iCACA,iBACA,2CACA,CAAK,CACL,yFACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,4EACA,EAFA,4BAEA,2BACA,OACI,QAA8B,EAClC,mCACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA0B,aAAe,kDAAyD,wOAAuQ,ySAAoU,mBAAmB,QAAQ,uDAA2D,gGAAwG,EAAE,oGAA4G,EAAE,kGAA0G,EAAE,+FAAuG,EAAE,uEAA+E,EAAE,kFAA0F,EAAE,0FAAkG,EAAE,uFAA+F,iBAAsB,gBAAkB,uBAAyB,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,gEAAoE,6BAAoC,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,o/BAAmsC,qBAAyB,ykDAAkmD,idAAge,OAAS,SAAS,qCAAyC,iCAAmC,WAAa,0CAAkD,MAAQ,YAAc,iBAAmB,sBAAwB,uBAiBruM,CAAC,CAAC,EAAC,sBCvBH,wDCAA", "sources": ["webpack://_N_E/./src/app/api/external/v1/docs/route.ts", "webpack://_N_E/./src/app/api/external/v1/docs/route.ts?ad19", "webpack://_N_E/?1438", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/", "webpack://_N_E/?932c"], "sourcesContent": ["import { type NextRequest, NextResponse } from 'next/server';\n\n// Use Edge Runtime for better performance\nexport const runtime = 'edge';\n\n// GET /api/external/v1/docs - API Documentation (OpenAPI/Swagger compatible)\nexport async function GET(request: NextRequest) {\n  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://roukey.online';\n  \n  const openApiSpec = {\n    openapi: '3.0.3',\n    info: {\n      title: 'RouKey External API',\n      description: 'Comprehensive API for managing RouKey configurations, provider keys, and intelligent routing. Build powerful AI applications with maximum control and flexibility.',\n      version: '1.0.0',\n      contact: {\n        name: 'Rou<PERSON>ey Support',\n        email: '<EMAIL>',\n        url: 'https://roukey.online'\n      },\n      license: {\n        name: 'Commercial License',\n        url: 'https://roukey.online/license'\n      }\n    },\n    servers: [\n      {\n        url: `${baseUrl}/api/external/v1`,\n        description: 'Production server'\n      }\n    ],\n    security: [\n      {\n        ApiKeyAuth: []\n      }\n    ],\n    components: {\n      securitySchemes: {\n        ApiKeyAuth: {\n          type: 'http',\n          scheme: 'bearer',\n          bearerFormat: 'API Key',\n          description: 'Use your RouKey-generated API key as a Bearer token'\n        }\n      },\n      schemas: {\n        Error: {\n          type: 'object',\n          properties: {\n            error: {\n              type: 'object',\n              properties: {\n                message: { type: 'string' },\n                type: { type: 'string' },\n                code: { type: 'string' },\n                details: { type: 'array', items: { type: 'object' } }\n              }\n            }\n          }\n        },\n        Config: {\n          type: 'object',\n          properties: {\n            id: { type: 'string', format: 'uuid' },\n            object: { type: 'string', enum: ['config'] },\n            name: { type: 'string' },\n            description: { type: 'string' },\n            routing_strategy: { type: 'string', enum: ['load_balancing', 'role_routing', 'agent_mode'] },\n            settings: { type: 'object' },\n            created_at: { type: 'string', format: 'date-time' },\n            updated_at: { type: 'string', format: 'date-time' }\n          }\n        },\n        ProviderKey: {\n          type: 'object',\n          properties: {\n            id: { type: 'string', format: 'uuid' },\n            object: { type: 'string', enum: ['provider_key'] },\n            provider: { type: 'string' },\n            label: { type: 'string' },\n            status: { type: 'string', enum: ['active', 'inactive'] },\n            is_default_general_chat_model: { type: 'boolean' },\n            temperature: { type: 'number', minimum: 0, maximum: 2 },\n            created_at: { type: 'string', format: 'date-time' },\n            updated_at: { type: 'string', format: 'date-time' }\n          }\n        },\n        Model: {\n          type: 'object',\n          properties: {\n            id: { type: 'string' },\n            object: { type: 'string', enum: ['model'] },\n            created: { type: 'integer' },\n            owned_by: { type: 'string' },\n            rokey_extensions: {\n              type: 'object',\n              properties: {\n                display_name: { type: 'string' },\n                description: { type: 'string' },\n                family: { type: 'string' },\n                context_window: { type: 'integer' },\n                modality: { type: 'string' },\n                provider: { type: 'string' }\n              }\n            }\n          }\n        }\n      }\n    },\n    paths: {\n      '/chat/completions': {\n        post: {\n          summary: 'Create chat completion',\n          description: 'OpenAI-compatible chat completions with intelligent routing',\n          tags: ['Chat'],\n          requestBody: {\n            required: true,\n            content: {\n              'application/json': {\n                schema: {\n                  type: 'object',\n                  required: ['messages'],\n                  properties: {\n                    model: { type: 'string', default: 'gpt-3.5-turbo' },\n                    messages: {\n                      type: 'array',\n                      items: {\n                        type: 'object',\n                        properties: {\n                          role: { type: 'string', enum: ['user', 'assistant', 'system'] },\n                          content: { type: 'string' }\n                        }\n                      }\n                    },\n                    temperature: { type: 'number', minimum: 0, maximum: 2 },\n                    max_tokens: { type: 'integer' },\n                    stream: { type: 'boolean' },\n                    role: { type: 'string', description: 'RouKey-specific role for intelligent routing' }\n                  }\n                }\n              }\n            }\n          },\n          responses: {\n            '200': {\n              description: 'Successful completion',\n              content: {\n                'application/json': {\n                  schema: {\n                    type: 'object',\n                    properties: {\n                      id: { type: 'string' },\n                      object: { type: 'string' },\n                      created: { type: 'integer' },\n                      model: { type: 'string' },\n                      choices: { type: 'array' },\n                      usage: { type: 'object' }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      },\n      '/configs': {\n        get: {\n          summary: 'List configurations',\n          description: 'Get all your RouKey configurations',\n          tags: ['Configuration Management'],\n          responses: {\n            '200': {\n              description: 'List of configurations',\n              content: {\n                'application/json': {\n                  schema: {\n                    type: 'object',\n                    properties: {\n                      object: { type: 'string', enum: ['list'] },\n                      data: {\n                        type: 'array',\n                        items: { $ref: '#/components/schemas/Config' }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        post: {\n          summary: 'Create configuration',\n          description: 'Create a new RouKey configuration',\n          tags: ['Configuration Management'],\n          requestBody: {\n            required: true,\n            content: {\n              'application/json': {\n                schema: {\n                  type: 'object',\n                  required: ['name'],\n                  properties: {\n                    name: { type: 'string', maxLength: 100 },\n                    description: { type: 'string', maxLength: 500 },\n                    routing_strategy: { type: 'string', enum: ['load_balancing', 'role_routing', 'agent_mode'] },\n                    settings: { type: 'object' }\n                  }\n                }\n              }\n            }\n          },\n          responses: {\n            '201': {\n              description: 'Configuration created',\n              content: {\n                'application/json': {\n                  schema: { $ref: '#/components/schemas/Config' }\n                }\n              }\n            }\n          }\n        }\n      },\n      '/configs/{configId}': {\n        get: {\n          summary: 'Get configuration',\n          description: 'Get a specific configuration by ID',\n          tags: ['Configuration Management'],\n          parameters: [\n            {\n              name: 'configId',\n              in: 'path',\n              required: true,\n              schema: { type: 'string', format: 'uuid' }\n            }\n          ],\n          responses: {\n            '200': {\n              description: 'Configuration details',\n              content: {\n                'application/json': {\n                  schema: { $ref: '#/components/schemas/Config' }\n                }\n              }\n            }\n          }\n        },\n        put: {\n          summary: 'Update configuration',\n          description: 'Update an existing configuration',\n          tags: ['Configuration Management'],\n          parameters: [\n            {\n              name: 'configId',\n              in: 'path',\n              required: true,\n              schema: { type: 'string', format: 'uuid' }\n            }\n          ],\n          requestBody: {\n            required: true,\n            content: {\n              'application/json': {\n                schema: {\n                  type: 'object',\n                  properties: {\n                    name: { type: 'string', maxLength: 100 },\n                    description: { type: 'string', maxLength: 500 },\n                    routing_strategy: { type: 'string', enum: ['load_balancing', 'role_routing', 'agent_mode'] },\n                    settings: { type: 'object' }\n                  }\n                }\n              }\n            }\n          },\n          responses: {\n            '200': {\n              description: 'Configuration updated',\n              content: {\n                'application/json': {\n                  schema: { $ref: '#/components/schemas/Config' }\n                }\n              }\n            }\n          }\n        },\n        delete: {\n          summary: 'Delete configuration',\n          description: 'Delete a configuration and all its associated keys',\n          tags: ['Configuration Management'],\n          parameters: [\n            {\n              name: 'configId',\n              in: 'path',\n              required: true,\n              schema: { type: 'string', format: 'uuid' }\n            }\n          ],\n          responses: {\n            '200': {\n              description: 'Configuration deleted',\n              content: {\n                'application/json': {\n                  schema: {\n                    type: 'object',\n                    properties: {\n                      id: { type: 'string' },\n                      object: { type: 'string' },\n                      deleted: { type: 'boolean' }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      },\n      '/models': {\n        get: {\n          summary: 'List models',\n          description: 'Get all available AI models (OpenAI-compatible)',\n          tags: ['Models & Providers'],\n          responses: {\n            '200': {\n              description: 'List of available models',\n              content: {\n                'application/json': {\n                  schema: {\n                    type: 'object',\n                    properties: {\n                      object: { type: 'string', enum: ['list'] },\n                      data: {\n                        type: 'array',\n                        items: { $ref: '#/components/schemas/Model' }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      },\n      '/usage': {\n        get: {\n          summary: 'Get usage analytics',\n          description: 'Retrieve detailed usage statistics and analytics',\n          tags: ['Analytics'],\n          parameters: [\n            {\n              name: 'start_date',\n              in: 'query',\n              schema: { type: 'string', format: 'date-time' },\n              description: 'Start date for usage period'\n            },\n            {\n              name: 'end_date',\n              in: 'query',\n              schema: { type: 'string', format: 'date-time' },\n              description: 'End date for usage period'\n            },\n            {\n              name: 'granularity',\n              in: 'query',\n              schema: { type: 'string', enum: ['hour', 'day', 'week', 'month'] },\n              description: 'Time granularity for aggregation'\n            }\n          ],\n          responses: {\n            '200': {\n              description: 'Usage analytics data',\n              content: {\n                'application/json': {\n                  schema: {\n                    type: 'object',\n                    properties: {\n                      object: { type: 'string', enum: ['usage_report'] },\n                      period: { type: 'object' },\n                      totals: { type: 'object' },\n                      time_series: { type: 'array' },\n                      breakdowns: { type: 'object' }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    },\n    tags: [\n      {\n        name: 'Chat',\n        description: 'OpenAI-compatible chat completions with intelligent routing'\n      },\n      {\n        name: 'Configuration Management',\n        description: 'Manage your RouKey configurations'\n      },\n      {\n        name: 'Provider Key Management',\n        description: 'Manage API keys for different AI providers'\n      },\n      {\n        name: 'Models & Providers',\n        description: 'Information about available models and providers'\n      },\n      {\n        name: 'Analytics',\n        description: 'Usage statistics and analytics'\n      },\n      {\n        name: 'Routing',\n        description: 'Configure intelligent routing strategies'\n      }\n    ]\n  };\n\n  return NextResponse.json(openApiSpec, {\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n      'Content-Type': 'application/json',\n    }\n  });\n}\n\n// OPTIONS handler for CORS\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n    },\n  });\n}\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\docs\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/external/v1/docs/route\",\n        pathname: \"/api/external/v1/docs\",\n        filename: \"route\",\n        bundlePath: \"app/api/external/v1/docs/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\docs\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fexternal%2Fv1%2Fdocs%2Froute&page=%2Fapi%2Fexternal%2Fv1%2Fdocs%2Froute&pagePath=private-next-app-dir%2Fapi%2Fexternal%2Fv1%2Fdocs%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&appPaths=%2Fapi%2Fexternal%2Fv1%2Fdocs%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/external/v1/docs/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":true},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.mjs\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\",\"image/avif\"],\"dangerouslyAllowSVG\":true,\"contentSecurityPolicy\":\"default-src 'self'; script-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"raw.githubusercontent.com\",\"port\":\"\",\"pathname\":\"/lobehub/lobe-icons/**\"},{\"protocol\":\"https\",\"hostname\":\"registry.npmmirror.com\",\"port\":\"\",\"pathname\":\"/@lobehub/icons-static-png/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@latest/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@v11/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"images.unsplash.com\",\"port\":\"\",\"pathname\":\"/**\"},{\"protocol\":\"https\",\"hostname\":\"cloud.gmelius.com\",\"port\":\"\",\"pathname\":\"/public/logos/**\"},{\"protocol\":\"https\",\"hostname\":\"upload.wikimedia.org\",\"port\":\"\",\"pathname\":\"/wikipedia/commons/**\"},{\"protocol\":\"https\",\"hostname\":\"kstatic.googleusercontent.com\",\"port\":\"\",\"pathname\":\"/files/**\"}],\"unoptimized\":false},\"devIndicators\":{\"position\":\"bottom-left\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"C:\\\\RoKey App\\\\rokey-app\",\"experimental\":{\"nodeMiddleware\":false,\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"dynamicOnHover\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":3,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":true,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":true,\"largePageDataBytes\":128000,\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"viewTransition\":false,\"routerBFCache\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"useCache\":false,\"optimizePackageImports\":[\"@heroicons/react\",\"@headlessui/react\",\"react-markdown\",\"react-syntax-highlighter\",\"@supabase/supabase-js\",\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"htmlLimitedBots\":\"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti\",\"bundlePagesRouterDependencies\":false,\"configFile\":\"C:\\\\RoKey App\\\\rokey-app\\\\next.config.mjs\",\"configFileName\":\"next.config.mjs\",\"serverExternalPackages\":[\"pdf-parse\",\"mammoth\"],\"turbopack\":{\"rules\":{\"*.svg\":{\"loaders\":[\"@svgr/webpack\"],\"as\":\"*.js\"}},\"root\":\"C:\\\\RoKey App\\\\rokey-app\"},\"compiler\":{\"removeConsole\":true,\"reactRemoveProperties\":true},\"api\":{\"bodyParser\":{\"sizeLimit\":\"50mb\"},\"responseLimit\":\"50mb\"},\"_originalRedirects\":[]}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/external/v1/docs/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/external/v1/docs/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "module.exports = require(\"node:buffer\");", "module.exports = require(\"node:async_hooks\");"], "names": ["runtime", "GET", "request", "openApiSpec", "openapi", "info", "title", "description", "version", "contact", "name", "email", "url", "license", "servers", "baseUrl", "security", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components", "securitySchemes", "type", "scheme", "bearerFormat", "schemas", "Error", "properties", "error", "message", "code", "details", "items", "Config", "id", "format", "object", "enum", "routing_strategy", "settings", "created_at", "updated_at", "Provide<PERSON><PERSON><PERSON>", "provider", "label", "status", "is_default_general_chat_model", "temperature", "minimum", "maximum", "Model", "created", "owned_by", "rokey_extensions", "display_name", "family", "context_window", "modality", "paths", "post", "summary", "tags", "requestBody", "required", "content", "schema", "model", "default", "messages", "role", "max_tokens", "stream", "responses", "choices", "usage", "get", "data", "$ref", "max<PERSON><PERSON><PERSON>", "parameters", "in", "put", "delete", "deleted", "period", "totals", "time_series", "breakdowns", "NextResponse", "json", "headers", "OPTIONS"], "sourceRoot": "", "ignoreList": []}