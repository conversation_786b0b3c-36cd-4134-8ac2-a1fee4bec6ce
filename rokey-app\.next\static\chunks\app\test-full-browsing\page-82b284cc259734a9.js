(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6715],{13311:(e,s,t)=>{Promise.resolve().then(t.bind(t,24881))},24881:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(95155),i=t(12115);function n(){let[e,s]=(0,i.useState)(null),[t,n]=(0,i.useState)(!1),[o,l]=(0,i.useState)(null),d=async()=>{n(!0),l(null),s(null);try{let e={id:"test_workflow_".concat(Date.now()),nodes:[{id:"user-request-1",type:"userRequest",position:{x:100,y:100},data:{label:"User Request",config:{},isConfigured:!0}},{id:"memory-1",type:"memory",position:{x:100,y:200},data:{label:"Memory Brain",config:{memoryName:"browsing_memory",maxSize:10240,encryption:!0},isConfigured:!0}},{id:"planner-1",type:"planner",position:{x:300,y:200},data:{label:"AI Planner",config:{modelId:"gemini-pro",providerId:"google",maxSubtasks:5},isConfigured:!0}},{id:"browsing-1",type:"browsing",position:{x:500,y:200},data:{label:"Intelligent Browsing",config:{maxSites:5,timeout:30,enableScreenshots:!0,enableFormFilling:!0,searchEngines:["google"],maxDepth:2},isConfigured:!0}},{id:"provider-1",type:"provider",position:{x:700,y:200},data:{label:"AI Provider",config:{providerId:"google",modelId:"gemini-pro",apiKey:"test-key"},isConfigured:!0}}],edges:[{id:"e1",source:"user-request-1",target:"browsing-1",type:"smoothstep"},{id:"e2",source:"memory-1",target:"browsing-1",type:"smoothstep"},{id:"e3",source:"planner-1",target:"browsing-1",sourceHandle:"output",targetHandle:"planner",type:"smoothstep"},{id:"e4",source:"browsing-1",target:"provider-1",type:"smoothstep"}]},t="Find the latest iPhone 15 Pro prices from multiple retailers and compare features",r=await fetch("/api/manual-build/execute-workflow",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({workflowId:e.id,nodes:e.nodes,edges:e.edges,userInput:t})});if(!r.ok){let e=await r.json();throw Error(e.details||"Workflow execution failed")}let i=await r.json();s({workflow:e,execution:i,userInput:t,timestamp:new Date().toISOString()})}catch(e){l(e instanceof Error?e.message:"Unknown error")}finally{n(!1)}};return(0,r.jsx)("div",{className:"min-h-screen bg-[#040716] text-white p-8",children:(0,r.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"\uD83C\uDF10 Full Browsing System Test"}),(0,r.jsxs)("div",{className:"mb-8 p-6 bg-gray-800/50 rounded-lg border border-gray-700",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Test Scenario"}),(0,r.jsx)("p",{className:"text-gray-300 mb-4",children:"This test creates a complete browsing workflow with all nodes connected:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside text-gray-300 space-y-2",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"User Request Node:"})," Provides the browsing task"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Memory Node:"})," Tracks progress and stores context"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Planner Node:"})," Creates intelligent browsing plans"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Browsing Node:"})," Executes multi-step browsing with smart completion"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"AI Provider Node:"})," Processes and synthesizes results"]})]})]}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)("button",{onClick:d,disabled:t,className:"bg-[#ff6b35] hover:bg-[#e55a2b] disabled:bg-gray-600 text-white px-8 py-4 rounded-lg font-medium transition-colors duration-200 text-lg",children:t?"\uD83D\uDD04 Running Complex Browsing Test...":"\uD83D\uDE80 Test Full Browsing System"})}),o&&(0,r.jsxs)("div",{className:"mb-6 p-4 bg-red-900/50 border border-red-500 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-red-400 mb-2",children:"❌ Test Failed"}),(0,r.jsx)("p",{className:"text-red-300",children:o})]}),e&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-green-900/30 border border-green-500 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-green-400 mb-4",children:"✅ Test Completed Successfully"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-white mb-2",children:"Workflow Configuration"}),(0,r.jsxs)("div",{className:"text-sm text-gray-300 bg-gray-800 p-3 rounded",children:[(0,r.jsxs)("div",{children:["Nodes: ",e.workflow.nodes.length]}),(0,r.jsxs)("div",{children:["Edges: ",e.workflow.edges.length]}),(0,r.jsxs)("div",{children:["Task: ",e.userInput]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-white mb-2",children:"Execution Status"}),(0,r.jsxs)("div",{className:"text-sm text-gray-300 bg-gray-800 p-3 rounded",children:[(0,r.jsxs)("div",{children:["Status: ",e.execution.success?"✅ Success":"❌ Failed"]}),(0,r.jsxs)("div",{children:["Executed At: ",new Date(e.execution.executedAt).toLocaleString()]}),(0,r.jsxs)("div",{children:["Workflow ID: ",e.execution.workflowId]})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-gray-800 p-6 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"\uD83D\uDCCA Detailed Results"}),(0,r.jsx)("pre",{className:"text-sm overflow-auto max-h-96 bg-gray-900 p-4 rounded",children:JSON.stringify(e.execution.result,null,2)})]}),(0,r.jsxs)("div",{className:"bg-blue-900/30 border border-blue-500 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-blue-400 mb-4",children:"\uD83E\uDDE0 How It Works"}),(0,r.jsxs)("div",{className:"text-sm text-gray-300 space-y-2",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"1. User Request:"})," Provides the browsing task to the system"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"2. Memory Connection:"})," Browsing node connects to memory for persistent tracking"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"3. AI Planning:"})," Planner creates detailed multi-step browsing strategy"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"4. Intelligent Browsing:"})," Executes searches, analyzes results, selects best sites"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"5. Smart Completion:"})," Detects when sufficient information is gathered"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"6. AI Processing:"})," Provider node synthesizes and presents final results"]})]})]})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6642,7706,7544,2138,4518,9248,2324,7358],()=>s(13311)),_N_E=e.O()}]);