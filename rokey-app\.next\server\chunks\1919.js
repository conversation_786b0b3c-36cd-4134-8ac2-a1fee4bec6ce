"use strict";exports.id=1919,exports.ids=[1919],exports.modules={9776:(e,r,n)=>{n.d(r,{Ay:()=>a,B0:()=>s});var t=n(60687);function a({size:e="md",className:r=""}){return(0,t.jsx)("div",{className:`animate-spin rounded-full border-2 border-gray-600 border-t-indigo-500 ${{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"}[e]} ${r}`})}function s({className:e=""}){return(0,t.jsx)("div",{className:`glass rounded-2xl p-6 animate-pulse ${e}`,children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gray-700 rounded-xl"}),(0,t.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-700 rounded w-3/4"}),(0,t.jsx)("div",{className:"h-3 bg-gray-700 rounded w-1/2"})]})]})})}},48935:(e,r,n)=>{n.d(r,{A:()=>a});var t=n(43210);let a=t.forwardRef(function({title:e,titleId:r,...n},a){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},n),e?t.createElement("title",{id:r},e):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"}))})},51919:(e,r,n)=>{n.d(r,{Jg:()=>R,yA:()=>P,sU:()=>b});var t=n(60687),a=n(43210),s=n(11016);let o={publishableKey:process.env.STRIPE_LIVE_PUBLISHABLE_KEY,secretKey:process.env.STRIPE_LIVE_SECRET_KEY,webhookSecret:process.env.STRIPE_LIVE_WEBHOOK_SECRET},i={FREE:process.env.STRIPE_LIVE_FREE_PRICE_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRICE_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID},l={FREE:process.env.STRIPE_LIVE_FREE_PRODUCT_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRODUCT_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID};process.env.STRIPE_LIVE_FREE_PRICE_ID,process.env.STRIPE_LIVE_STARTER_PRICE_ID,process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID,o.publishableKey&&o.publishableKey.substring(0,20),o.secretKey&&o.secretKey.substring(0,20),o.webhookSecret&&o.webhookSecret.substring(0,15);let c={free:{name:"Free",price:"$0",priceId:i.FREE,productId:l.FREE,features:["Unlimited API requests","1 Custom Configuration","3 API Keys per config","All 300+ AI models","Strict fallback routing only","Basic analytics only","No custom roles, basic router only","Limited logs","Community support"],limits:{configurations:1,apiKeysPerConfig:3,apiRequests:999999,canUseAdvancedRouting:!1,canUseCustomRoles:!1,maxCustomRoles:0,canUsePromptEngineering:!1,canUseKnowledgeBase:!1,knowledgeBaseDocuments:0,canUseSemanticCaching:!1}},starter:{name:"Starter",price:"$19",priceId:i.STARTER,productId:l.STARTER,features:["Unlimited API requests","15 Custom Configurations","5 API Keys per config","All 300+ AI models","Intelligent routing strategies","Up to 3 custom roles","Intelligent role routing","Prompt engineering (no file upload)","Enhanced logs and analytics","Community support"],limits:{configurations:15,apiKeysPerConfig:5,apiRequests:999999,canUseAdvancedRouting:!0,canUseCustomRoles:!0,maxCustomRoles:3,canUsePromptEngineering:!0,canUseKnowledgeBase:!1,knowledgeBaseDocuments:0,canUseSemanticCaching:!1}},professional:{name:"Professional",price:"$49",priceId:i.PROFESSIONAL,productId:l.PROFESSIONAL,features:["Unlimited API requests","Unlimited Custom Configurations","Unlimited API Keys per config","All 300+ AI models","All advanced routing strategies","Unlimited custom roles","Prompt engineering + Knowledge base (5 documents)","Semantic caching","Advanced analytics and logging","Priority email support"],limits:{configurations:999999,apiKeysPerConfig:999999,apiRequests:999999,canUseAdvancedRouting:!0,canUseCustomRoles:!0,maxCustomRoles:999999,canUsePromptEngineering:!0,canUseKnowledgeBase:!0,knowledgeBaseDocuments:5,canUseSemanticCaching:!0}}};var d=n(52535);let g=a.forwardRef(function({title:e,titleId:r,...n},t){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),e?a.createElement("title",{id:r},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"}))});var u=n(70149),m=n(48935),p=n(16189);let x={custom_roles:"Custom Roles",knowledge_base:"Knowledge Base",advanced_routing:"Advanced Routing",prompt_engineering:"Prompt Engineering",semantic_caching:"Semantic Caching",configurations:"API Configurations"},f=e=>{for(let r of["starter","professional"]){let n=c[r];switch(e){case"custom_roles":if(n.limits.canUseCustomRoles)return r;break;case"knowledge_base":if(n.limits.canUseKnowledgeBase)return r;break;case"advanced_routing":if(n.limits.canUseAdvancedRouting)return r;break;case"prompt_engineering":if(n.limits.canUsePromptEngineering)return r;break;case"semantic_caching":if(n.limits.canUseSemanticCaching)return r;break;case"configurations":if(n.limits.configurations>c.free.limits.configurations)return r}}return"starter"};function h({feature:e,currentTier:r,customMessage:n,size:a="md",variant:o="card",theme:i="light"}){let{createCheckoutSession:l}=(0,s.R)(),h=(0,p.useRouter)(),E=f(e),b=c[E],R=x[e],v=async()=>{try{"starter"===E?await l("starter"):"professional"===E?await l("professional"):h.push("/pricing")}catch(e){h.push("/pricing")}};return(0,t.jsx)(d.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:`${{card:"dark"===i?"bg-gradient-to-br from-orange-900/20 to-orange-800/20 border border-orange-500/30 rounded-xl shadow-sm":"bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200 rounded-xl shadow-sm",banner:"dark"===i?"bg-orange-900/20 border-l-4 border-orange-500 rounded-r-lg":"bg-orange-100 border-l-4 border-orange-500 rounded-r-lg",inline:"dark"===i?"bg-orange-900/20 border border-orange-500/30 rounded-lg":"bg-orange-50 border border-orange-200 rounded-lg"}[o]} ${{sm:"p-4 text-sm",md:"p-6 text-base",lg:"p-8 text-lg"}[a]}`,children:(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center",children:(0,t.jsx)(g,{className:"w-5 h-5 text-white"})})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("h3",{className:`text-lg font-semibold mb-2 ${"dark"===i?"text-white":"text-gray-900"}`,children:[R," - Premium Feature"]}),(0,t.jsx)("p",{className:`mb-4 ${"dark"===i?"text-gray-300":"text-gray-700"}`,children:n||`${R} is available starting with the ${b.name} plan.
               Upgrade to unlock this powerful feature and enhance your RouKey experience.`}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("button",{onClick:v,className:"inline-flex items-center px-4 py-2 bg-orange-600 text-white text-sm font-medium rounded-lg hover:bg-orange-700 transition-colors duration-200",children:[(0,t.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Upgrade to ",b.name]}),(0,t.jsxs)("button",{onClick:()=>h.push("/pricing"),className:`inline-flex items-center px-4 py-2 text-sm font-medium transition-colors duration-200 ${"dark"===i?"text-orange-400 hover:text-orange-300":"text-orange-600 hover:text-orange-700"}`,children:[(0,t.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"View All Plans"]})]})]})]})})}var E=n(9776);function b({feature:e,children:r,fallback:n,showUpgradePrompt:a=!0,customMessage:o,currentCount:i=0,theme:l="light"}){let{subscriptionStatus:d,loading:g}=(0,s.R)();if(g)return(0,t.jsx)(E.Ay,{});let u=d?.tier||"free";if("configurations"===e){if(i<c[u].limits.configurations)return(0,t.jsx)(t.Fragment,{children:r})}else if(function(e,r){let n=c[e].limits;switch(r){case"custom_roles":return n.canUseCustomRoles;case"knowledge_base":return n.canUseKnowledgeBase;case"advanced_routing":return n.canUseAdvancedRouting;case"prompt_engineering":return n.canUsePromptEngineering;case"semantic_caching":return n.canUseSemanticCaching;case"configurations":return n.configurations>0;default:return!1}}(u,e))return(0,t.jsx)(t.Fragment,{children:r});return n?(0,t.jsx)(t.Fragment,{children:n}):a?(0,t.jsx)(h,{feature:e,currentTier:u,customMessage:o,theme:l}):null}function R({current:e,limit:r,label:n,tier:a,showUpgradeHint:s=!0,className:o="",theme:i="light"}){let l=(0,p.useRouter)(),c=r>=999999,g=c?0:e/r*100,u=g>=80,m=e>=r&&!c;return(0,t.jsxs)("div",{className:`flex items-center justify-between text-sm ${o}`,children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("span",{className:"dark"===i?"text-gray-300":"text-gray-600",children:[n,":"]}),!c&&(0,t.jsx)("div",{className:`w-16 rounded-full h-1.5 ${"dark"===i?"bg-gray-700":"bg-gray-200"}`,children:(0,t.jsx)(d.P.div,{className:`h-1.5 rounded-full ${c?"bg-green-500":m?"bg-red-500":u?"bg-yellow-500":"bg-green-500"}`,initial:{width:0},animate:{width:`${Math.min(g,100)}%`},transition:{duration:.5,ease:"easeOut"}})})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{className:`text-xs font-medium ${"dark"===i?c?"text-green-400":m?"text-red-400":u?"text-yellow-400":"text-green-400":c?"text-green-600":m?"text-red-600":u?"text-yellow-600":"text-green-600"}`,children:c?"Unlimited":`${e}/${r}`}),(m||u)&&s&&(0,t.jsx)("button",{className:`text-xs underline ml-2 ${"dark"===i?"text-orange-400 hover:text-orange-300":"text-orange-600 hover:text-orange-700"}`,onClick:()=>{l.push("/billing")},children:"Upgrade"})]})]})}n(58089),n(81521),n(59168);let v=a.forwardRef(function({title:e,titleId:r,...n},t){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),e?a.createElement("title",{id:r},e):null,a.createElement("path",{fillRule:"evenodd",d:"M12.516 2.17a.75.75 0 0 0-1.032 0 11.209 11.209 0 0 1-7.877 ********** 0 0 0-.722.515A12.74 12.74 0 0 0 2.25 9.75c0 5.942 4.064 10.933 9.563 12.348a.749.749 0 0 0 .374 0c5.499-1.415 9.563-6.406 9.563-12.348 0-1.39-.223-2.73-.635-3.985a.75.75 0 0 0-.722-.516l-.143.001c-2.996 0-5.717-1.17-7.734-3.08Zm3.094 8.016a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z",clipRule:"evenodd"}))}),w=a.forwardRef(function({title:e,titleId:r,...n},t){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),e?a.createElement("title",{id:r},e):null,a.createElement("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.006 5.404.434c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.434 2.082-5.005Z",clipRule:"evenodd"}))}),I=a.forwardRef(function({title:e,titleId:r,...n},t){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),e?a.createElement("title",{id:r},e):null,a.createElement("path",{fillRule:"evenodd",d:"M9 4.5a.75.75 0 0 1 .721.544l.813 2.846a3.75 3.75 0 0 0 2.576 2.576l2.846.813a.75.75 0 0 1 0 1.442l-2.846.813a3.75 3.75 0 0 0-2.576 2.576l-.813 2.846a.75.75 0 0 1-1.442 0l-.813-2.846a3.75 3.75 0 0 0-2.576-2.576l-2.846-.813a.75.75 0 0 1 0-1.442l2.846-.813A3.75 3.75 0 0 0 7.466 7.89l.813-2.846A.75.75 0 0 1 9 4.5ZM18 1.5a.75.75 0 0 1 .728.568l.258 1.036c.236.94.97 1.674 1.91 1.91l1.036.258a.75.75 0 0 1 0 1.456l-1.036.258c-.94.236-1.674.97-1.91 1.91l-.258 1.036a.75.75 0 0 1-1.456 0l-.258-1.036a2.625 2.625 0 0 0-1.91-1.91l-1.036-.258a.75.75 0 0 1 0-1.456l1.036-.258a2.625 2.625 0 0 0 1.91-1.91l.258-1.036A.75.75 0 0 1 18 1.5ZM16.5 15a.75.75 0 0 1 .712.513l.394 1.183c.15.447.5.799.948.948l1.183.395a.75.75 0 0 1 0 1.422l-1.183.395c-.447.15-.799.5-.948.948l-.395 1.183a.75.75 0 0 1-1.422 0l-.395-1.183a1.5 1.5 0 0 0-.948-.948l-1.183-.395a.75.75 0 0 1 0-1.422l1.183-.395c.447-.15.799-.5.948-.948l.395-1.183A.75.75 0 0 1 16.5 15Z",clipRule:"evenodd"}))}),_={free:{name:"Free",light:{color:"bg-gray-100 text-gray-800 border-gray-300",iconColor:"text-gray-600"},dark:{color:"bg-gray-800/50 text-gray-200 border-gray-600/50",iconColor:"text-gray-400"},icon:v},starter:{name:"Starter",light:{color:"bg-blue-100 text-blue-800 border-blue-300",iconColor:"text-blue-600"},dark:{color:"bg-blue-900/30 text-blue-200 border-blue-500/50",iconColor:"text-blue-400"},icon:w},professional:{name:"Professional",light:{color:"bg-orange-100 text-orange-800 border-orange-300",iconColor:"text-orange-600"},dark:{color:"bg-orange-900/30 text-orange-200 border-orange-500/50",iconColor:"text-orange-400"},icon:I},enterprise:{name:"Enterprise",light:{color:"bg-purple-100 text-purple-800 border-purple-300",iconColor:"text-purple-600"},dark:{color:"bg-purple-900/30 text-purple-200 border-purple-500/50",iconColor:"text-purple-400"},icon:a.forwardRef(function({title:e,titleId:r,...n},t){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),e?a.createElement("title",{id:r},e):null,a.createElement("path",{fillRule:"evenodd",d:"M5.166 2.621v.858c-1.035.148-2.059.33-3.071.543a.75.75 0 0 0-.584.859 6.753 6.753 0 0 0 6.138 5.6 6.73 6.73 0 0 0 2.743 1.346A6.707 6.707 0 0 1 9.279 15H8.54c-1.036 0-1.875.84-1.875 1.875V19.5h-.75a2.25 2.25 0 0 0-2.25 2.25c0 .414.336.75.75.75h15a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-2.25-2.25h-.75v-2.625c0-1.036-.84-1.875-1.875-1.875h-.739a6.706 6.706 0 0 1-1.112-3.173 6.73 6.73 0 0 0 2.743-1.347 6.753 6.753 0 0 0 6.139-********* 0 0 0-.585-.858 47.077 47.077 0 0 0-3.07-.543V2.62a.75.75 0 0 0-.658-.744 49.22 49.22 0 0 0-6.093-.377c-2.063 0-4.096.128-6.093.377a.75.75 0 0 0-.657.744Zm0 2.629c0 1.196.312 2.32.857 3.294A5.266 5.266 0 0 1 3.16 5.337a45.6 45.6 0 0 1 2.006-.343v.256Zm13.5 0v-.256c.674.1 1.343.214 2.006.343a5.265 5.265 0 0 1-2.863 3.207 6.72 6.72 0 0 0 .857-3.294Z",clipRule:"evenodd"}))})}},y={sm:{container:"px-2 py-1 text-xs",icon:"w-3 h-3"},md:{container:"px-3 py-1 text-sm",icon:"w-4 h-4"},lg:{container:"px-4 py-2 text-base",icon:"w-5 h-5"}};function P({tier:e,size:r="md",showIcon:n=!0,className:a="",theme:s="light"}){let o=_[e],i=y[r],l=o[s],c=o.icon;return(0,t.jsxs)("span",{className:`
      inline-flex items-center space-x-1 font-medium border rounded-full
      ${l.color}
      ${i.container}
      ${a}
    `,children:[n&&(0,t.jsx)(c,{className:`${i.icon} ${l.iconColor}`}),(0,t.jsx)("span",{children:o.name})]})}}};