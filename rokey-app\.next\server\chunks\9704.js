"use strict";exports.id=9704,exports.ids=[9704],exports.modules={99704:(e,t,o)=>{function n(e,t,o,n){let r=e.getReader(),a=new TextDecoder;return new TextEncoder,new ReadableStream({async start(e){let o=!1,l="";Date.now();try{for(;;){let{done:c,value:T}=await r.read();if(c){if(n&&!o){let e=i(l,t);n(e)}e.close();break}let u=a.decode(T,{stream:!0});if(!o&&u.includes("delta"))try{for(let e of u.split("\n"))if(e.startsWith("data: ")&&!e.includes("[DONE]")){let t=e.substring(6);try{let e=JSON.parse(t);if(e.choices?.[0]?.delta?.content){Date.now(),o=!0,l+=e.choices[0].delta.content;break}}catch(e){}}}catch(e){o||(Date.now(),o=!0)}if(n&&u.includes("usage"))try{let e=s(u,t);e&&n(e)}catch(e){}if(u.includes("delta"))try{for(let e of u.split("\n"))if(e.startsWith("data: ")&&!e.includes("[DONE]")){let t=e.substring(6);try{let e=JSON.parse(t);e.choices?.[0]?.delta?.content&&(l+=e.choices[0].delta.content)}catch(e){}}}catch(e){}e.enqueue(T)}}catch(t){t instanceof Error&&(t.message.includes("aborted")||t.message.includes("ECONNRESET"))?e.close():e.error(t)}}})}function r(e,t,o){void 0!==o.timeToFirstToken&&(o.timeToFirstToken<500||o.timeToFirstToken<1e3||o.timeToFirstToken),o.totalStreamTime,o.totalTokens,o.averageTokenLatency}function a(e,t){return{provider:e||"unknown",model:t||"unknown"}}function s(e,t){try{for(let o of e.split("\n"))if(o.startsWith("data: ")&&!o.includes("[DONE]")){let e=o.substring(6);try{let o=JSON.parse(e);if(o.usage){let e,n=o.usage,r=0,a=0,s=0;if("openrouter"===t.toLowerCase()?(r=n.prompt_tokens||0,a=n.completion_tokens||0,s=n.total_tokens||r+a,e=n.cost?1e-6*n.cost:void 0):(r=n.prompt_tokens||n.input_tokens||0,a=n.completion_tokens||n.output_tokens||0,s=n.total_tokens||r+a),s>0)return{promptTokens:r,completionTokens:a,totalTokens:s,cost:e}}}catch(e){}}}catch(e){}return null}function i(e,t,o){let n=.25;switch(t.toLowerCase()){case"openrouter":case"openai":case"xai":n=.25;break;case"google":n=.22;break;case"anthropic":n=.26}let r=Math.ceil(e.length*n),a=o?Math.ceil(o.length*n):Math.ceil(.3*r);return{promptTokens:a,completionTokens:r,totalTokens:a+r}}function l(e){return Math.ceil(e.length/4)}o.r(t),o.d(t,{PERFORMANCE_THRESHOLDS:()=>c,createFirstTokenTrackingStream:()=>n,estimateTokenCount:()=>l,estimateUsageFromContent:()=>i,evaluatePerformance:()=>T,extractUsageFromStreamChunk:()=>s,getProviderModelFromContext:()=>a,logStreamingPerformance:()=>r});let c={EXCELLENT_FIRST_TOKEN:500,GOOD_FIRST_TOKEN:1e3,SLOW_FIRST_TOKEN:2e3,EXCELLENT_TOTAL:3e3,GOOD_TOTAL:5e3,SLOW_TOTAL:1e4,TARGET_TOKEN_LATENCY:50};function T(e){let t=e.timeToFirstToken?e.timeToFirstToken<c.EXCELLENT_FIRST_TOKEN?"excellent":e.timeToFirstToken<c.GOOD_FIRST_TOKEN?"good":e.timeToFirstToken<c.SLOW_FIRST_TOKEN?"slow":"very_slow":"very_slow",o=e.totalStreamTime?e.totalStreamTime<c.EXCELLENT_TOTAL?"excellent":e.totalStreamTime<c.GOOD_TOTAL?"good":e.totalStreamTime<c.SLOW_TOTAL?"slow":"very_slow":"very_slow",n=e.averageTokenLatency?e.averageTokenLatency<c.TARGET_TOKEN_LATENCY?"excellent":e.averageTokenLatency<2*c.TARGET_TOKEN_LATENCY?"good":e.averageTokenLatency<4*c.TARGET_TOKEN_LATENCY?"slow":"very_slow":"very_slow",r=["excellent","good","slow","very_slow"],a=[t,o,n].reduce((e,t)=>r.indexOf(t)>r.indexOf(e)?t:e,"excellent");return{firstTokenGrade:t,totalTimeGrade:o,tokenLatencyGrade:n,overallGrade:a}}}};