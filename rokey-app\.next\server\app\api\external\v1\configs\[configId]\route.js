(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[435],{5356:e=>{"use strict";e.exports=require("node:buffer")},5521:e=>{"use strict";e.exports=require("node:async_hooks")},9189:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>E,default:()=>P});var o,a={};r.r(a),r.d(a,{DELETE:()=>x,GET:()=>v,OPTIONS:()=>C,PUT:()=>b,runtime:()=>h});var i={};r.r(i),r.d(i,{patchFetch:()=>R,routeModule:()=>w,serverHooks:()=>I,workAsyncStorage:()=>k,workUnitAsyncStorage:()=>A});var s=r(8429),n=r(9874),c=r(8294),l=r(6567),p=r(4144),d=r(5421),u=r(974),m=r(4429),f=r(9975),g=r(1109);let h="edge",_=new m.S,y=g.z.object({name:g.z.string().min(1).max(100).optional(),description:g.z.string().max(500).optional(),routing_strategy:g.z.enum(["load_balancing","role_routing","agent_mode"]).optional(),settings:g.z.object({temperature:g.z.number().min(0).max(2).optional(),max_tokens:g.z.number().int().positive().optional(),top_p:g.z.number().min(0).max(1).optional(),frequency_penalty:g.z.number().min(-2).max(2).optional(),presence_penalty:g.z.number().min(-2).max(2).optional()}).optional()});async function v(e,{params:t}){try{let r=await _.authenticateRequest(e);if(!r.success)return u.Rp.json({error:{message:r.error,type:"authentication_error",code:"invalid_api_key"}},{status:r.statusCode||401});let{userApiKey:o,userConfig:a,ipAddress:i}=r,{configId:s}=await t,n=(0,f.Qb)(e),{data:c,error:l}=await n.from("custom_api_configs").select(`
        id,
        name,
        description,
        routing_strategy,
        settings,
        created_at,
        updated_at,
        api_keys(
          id,
          provider,
          label,
          status,
          is_default_general_chat_model,
          temperature,
          predefined_models(name, display_name)
        )
      `).eq("id",s).eq("user_id",a.user_id).single();if(l||!c)return u.Rp.json({error:{message:"Configuration not found",type:"not_found_error",code:"config_not_found"}},{status:404});return _.logApiUsage(o,e,{statusCode:200,modelUsed:"config_management",providerUsed:"rokey_api"},i).catch(console.error),u.Rp.json({id:c.id,object:"config",name:c.name,description:c.description,routing_strategy:c.routing_strategy,settings:c.settings,created_at:c.created_at,updated_at:c.updated_at,api_keys:c.api_keys||[]},{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}catch(e){return u.Rp.json({error:{message:"Internal server error",type:"server_error",code:"internal_error"}},{status:500})}}async function b(e,{params:t}){try{let r=await _.authenticateRequest(e);if(!r.success)return u.Rp.json({error:{message:r.error,type:"authentication_error",code:"invalid_api_key"}},{status:r.statusCode||401});let{userApiKey:o,userConfig:a,ipAddress:i}=r,{configId:s}=await t,n=await e.json(),c=y.safeParse(n);if(!c.success)return u.Rp.json({error:{message:"Invalid request data",type:"validation_error",code:"invalid_parameters",details:c.error.errors}},{status:400});let l=c.data,p=(0,f.Qb)(e),{data:d,error:m}=await p.from("custom_api_configs").update({...l,updated_at:new Date().toISOString()}).eq("id",s).eq("user_id",a.user_id).select().single();if(m||!d)return u.Rp.json({error:{message:"Configuration not found or failed to update",type:"not_found_error",code:"config_not_found"}},{status:404});return _.logApiUsage(o,e,{statusCode:200,modelUsed:"config_management",providerUsed:"rokey_api"},i).catch(console.error),u.Rp.json({id:d.id,object:"config",name:d.name,description:d.description,routing_strategy:d.routing_strategy,settings:d.settings,created_at:d.created_at,updated_at:d.updated_at},{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}catch(e){return u.Rp.json({error:{message:"Internal server error",type:"server_error",code:"internal_error"}},{status:500})}}async function x(e,{params:t}){try{let r=await _.authenticateRequest(e);if(!r.success)return u.Rp.json({error:{message:r.error,type:"authentication_error",code:"invalid_api_key"}},{status:r.statusCode||401});let{userApiKey:o,userConfig:a,ipAddress:i}=r,{configId:s}=await t,n=(0,f.Qb)(e),{data:c,error:l}=await n.from("custom_api_configs").select("id, name").eq("id",s).eq("user_id",a.user_id).single();if(l||!c)return u.Rp.json({error:{message:"Configuration not found",type:"not_found_error",code:"config_not_found"}},{status:404});let{error:p}=await n.from("custom_api_configs").delete().eq("id",s).eq("user_id",a.user_id);if(p)return u.Rp.json({error:{message:"Failed to delete configuration",type:"server_error",code:"database_error"}},{status:500});return _.logApiUsage(o,e,{statusCode:200,modelUsed:"config_management",providerUsed:"rokey_api"},i).catch(console.error),u.Rp.json({id:s,object:"config",deleted:!0},{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}catch(e){return u.Rp.json({error:{message:"Internal server error",type:"server_error",code:"internal_error"}},{status:500})}}async function C(){return new u.Rp(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}let w=new l.AppRouteRouteModule({definition:{kind:p.A.APP_ROUTE,page:"/api/external/v1/configs/[configId]/route",pathname:"/api/external/v1/configs/[configId]",filename:"route",bundlePath:"app/api/external/v1/configs/[configId]/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\external\\v1\\configs\\[configId]\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:k,workUnitAsyncStorage:A,serverHooks:I}=w;function R(){return(0,d.V5)({workAsyncStorage:k,workUnitAsyncStorage:A})}let S=null==(o=self.__RSC_MANIFEST)?void 0:o["/api/external/v1/configs/[configId]/route"],T=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);S&&T&&(0,n.fQ)({page:"/api/external/v1/configs/[configId]/route",clientReferenceManifest:S,serverActionsManifest:T,serverModuleMap:(0,s.e)({serverActionsManifest:T})});let E=i,P=c.s.wrap(w,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!0},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp","image/avif"],dangerouslyAllowSVG:!0,contentSecurityPolicy:"default-src 'self'; script-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"raw.githubusercontent.com",port:"",pathname:"/lobehub/lobe-icons/**"},{protocol:"https",hostname:"registry.npmmirror.com",port:"",pathname:"/@lobehub/icons-static-png/**"},{protocol:"https",hostname:"cdn.jsdelivr.net",port:"",pathname:"/npm/simple-icons@latest/icons/**"},{protocol:"https",hostname:"cdn.jsdelivr.net",port:"",pathname:"/npm/simple-icons@v11/icons/**"},{protocol:"https",hostname:"images.unsplash.com",port:"",pathname:"/**"},{protocol:"https",hostname:"cloud.gmelius.com",port:"",pathname:"/public/logos/**"},{protocol:"https",hostname:"upload.wikimedia.org",port:"",pathname:"/wikipedia/commons/**"},{protocol:"https",hostname:"kstatic.googleusercontent.com",port:"",pathname:"/files/**"}],unoptimized:!1},devIndicators:{position:"bottom-left"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"C:\\RoKey App\\rokey-app",experimental:{nodeMiddleware:!1,cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,dynamicOnHover:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!0,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!0,largePageDataBytes:128e3,typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,viewTransition:!1,routerBFCache:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,useCache:!1,optimizePackageImports:["@heroicons/react","@headlessui/react","react-markdown","react-syntax-highlighter","@supabase/supabase-js","lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},htmlLimitedBots:"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti",bundlePagesRouterDependencies:!1,configFile:"C:\\RoKey App\\rokey-app\\next.config.mjs",configFileName:"next.config.mjs",serverExternalPackages:["pdf-parse","mammoth"],turbopack:{rules:{"*.svg":{loaders:["@svgr/webpack"],as:"*.js"}},root:"C:\\RoKey App\\rokey-app"},compiler:{removeConsole:!0,reactRemoveProperties:!0},_originalRedirects:[]}})},9975:(e,t,r)=>{"use strict";r.d(t,{Qb:()=>a});var o=r(3339);function a(e){return(0,o.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}r(2710)}},e=>{var t=t=>e(e.s=t);e.O(0,[580,918,109,44,833],()=>t(9189));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/external/v1/configs/[configId]/route"]=r}]);
//# sourceMappingURL=route.js.map