"use strict";exports.id=146,exports.ids=[146],exports.modules={10146:(e,t,a)=>{async function r(e){try{let{sendResendWelcomeEmail:t}=await a.e(7195).then(a.bind(a,37195));return await t(e)}catch(e){return!1}}async function n(e){if(process.env.RESEND_API_KEY&&await r(e))return!0;try{let t={to_email:e.userEmail,to_name:e.userName,user_tier:e.userTier,company_name:"<PERSON><PERSON><PERSON><PERSON>",dashboard_url:"https://roukey.online/dashboard",docs_url:"https://roukey.online/docs",support_email:"<EMAIL>",current_year:new Date().getFullYear(),welcome_date:new Date().toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})},a=await fetch("https://api.emailjs.com/api/v1.0/email/send",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer lm7-ATth2Cql60KIN"},body:JSON.stringify({service_id:"service_2xtn7iv",template_id:"template_welcome_email",user_id:"lm7-ATth2Cql60KIN",template_params:t,accessToken:"lm7-ATth2Cql60KIN"})});if(a.ok)return!0;return await a.text(),!0}catch(e){return!0}}a.d(t,{sendWelcomeEmail:()=>n})}};