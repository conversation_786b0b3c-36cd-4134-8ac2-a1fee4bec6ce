{"version": 3, "file": "app/api/external/v1/usage/route.js", "mappings": "gJEAA,oSFMO,IAAMA,EAAU,OAEjBC,EAAiB,IAAIC,EAAAA,CAAoBA,CAGzCC,EAAmBC,EAAAA,CAAAA,CAAAA,IAHLH,EAGa,CAAC,CAChCI,WAAYD,EAAAA,CAAAA,CAAAA,MAAQ,GAAGE,QAAQ,GAAGC,QAAQ,GAC1CC,SAAUJ,EAAAA,CAAAA,CAAAA,MAAQ,GAAGE,QAAQ,GAAGC,QAAQ,GACxCE,YAAaL,EAAAA,CAAAA,CAAAA,IAAM,CAAC,CAAC,OAAQ,MAAO,OAAQ,QAAQ,EAAEM,OAAO,CAAC,OAC9DC,UAAWP,EAAAA,CAAAA,CAAAA,MAAQ,GAAGQ,IAAI,GAAGL,QAAQ,GACrCM,SAAUT,EAAAA,CAAAA,CAAAA,MAAQ,GAAGG,QAAQ,GAC7BO,MAAOV,EAAAA,CAAAA,CAAAA,MAAQ,GAAGG,QAAQ,EAC5B,GAGO,eAAeQ,EAAIC,CAAoB,EAC5C,GAAI,CAEF,IAAMC,EAAa,MAAMhB,EAAeiB,kBAADjB,CAAoB,CAACe,GAE5D,GAAI,CAACC,EAAWE,OAAO,CACrB,CADuB,MAChBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAASN,EAAWK,KAAK,CACzBE,KAAM,uBACNC,KAAM,iBACR,CACF,EACA,CAAEC,OAAQT,EAAWU,UAAU,EAAI,GAAI,GAI3C,GAAM,YAAEC,CAAU,YAAEC,CAAU,WAAEC,CAAS,CAAE,CAAGb,EAGxC,cAAEc,CAAY,CAAE,CAAG,IAAIC,IAAIhB,EAAQiB,GAAG,EACtCC,EAAc,CAClB7B,WAAY0B,EAAaI,GAAG,CAAC,oBAAiBC,EAC9C5B,SAAUuB,EAAaI,GAAG,CAAC,kBAAeC,EAC1C3B,YAAasB,EAAaI,GAAG,CAAC,gBAAkB,MAChDxB,UAAWoB,EAAaI,GAAG,CAAC,mBAAgBC,EAC5CvB,SAAUkB,EAAaI,GAAG,CAAC,aAAeC,OAC1CtB,MAAOiB,EAAaI,GAAG,CAAC,eAAYC,CACtC,EAEMC,EAAmBlC,EAAiBmC,SAAS,CAACJ,GACpD,GAAI,CAACG,EAAiBlB,OAAO,CAC3B,CAD6B,MACtBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,2BACTC,KAAM,mBACNC,KAAM,qBACNc,QAASF,EAAiBf,KAAK,CAACkB,MAAM,CAE1C,EACA,CAAEd,OAAQ,GAAI,GAIlB,GAAM,YAAErB,CAAU,UAAEG,CAAQ,aAAEC,CAAW,WAAEE,CAAS,UAAEE,CAAQ,OAAEC,CAAK,CAAE,CAAGuB,EAAiBI,IAAI,CAGzFC,EAAUlC,EAAW,IAAImC,KAAKnC,GAAY,IAAImC,KAC9CC,MAA6BD,KAAjBtC,GAA6CsC,KAAKE,GAAG,EAAxC,CAA6C,IAAlBF,CAAuB,GAK7EG,EALkF,CAErEC,EAAAA,EAF0E,EAE1EA,CAAqCA,CAAC/B,CAFyC,EAM7FgC,IAAI,CAAC,2BACLC,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;MAoBT,CAAC,EACAC,GAAG,CAAC,aAAcN,EAAUO,WAAW,IACvCC,GAAG,CAAC,aAAcV,EAAQS,WAAW,IACrCE,KAAK,CAAC,aAAc,CAAEC,UAAW,EAAM,GAG1CR,EAAQA,EAAMS,EAAE,CAAC,qDAAsD1B,EAAY2B,OAAO,EAGtF7C,IACFmC,EAAQA,EAAMS,EAAE,CADH,+CACoD5C,EAAAA,EAE/DE,IACFiC,EAAQA,EAAMS,EADF,CACK,gBAAiB1C,EAAAA,EAEhCC,IACFgC,EAAQA,CADC,CACKS,EAAE,CAAC,aAAczC,EAAAA,EAGjC,GAAM,CAAE2B,KAAMgB,CAAS,OAAEnC,CAAK,CAAE,CAAG,MAAMwB,EAEzC,GAAIxB,EAEF,KAFS,EAEFF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,6BACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,GAKlB,IAAMgC,EAAOD,GAAa,EAAE,CAGtBE,EAASD,EAAKE,MAAM,CAAC,CAACC,EAAKC,IAAS,iBACxBD,EAAIE,cAAc,CAAG,EACrCC,aAAcH,EAAIG,YAAY,EAAIF,CAAAA,CAAIE,YAAY,GAAI,EACtDC,WAAYJ,EAAII,UAAU,EAAIH,CAAAA,CAAII,QAAQ,GAAI,EAC9CC,oBAAqBN,EAAIM,mBAAmB,GAAIL,CAAoB,QAAhBM,WAAW,EAC/DC,EAD0E,IAAI,UAC7DR,EAAIQ,eAAe,GAAIP,CAAoB,MAApBA,EAAIM,WAAW,EACzD,EADoE,CAElEL,GAFsE,YAEtD,EAChBC,aAAc,EACdC,WAAY,EACZE,oBAAqB,EACrBE,gBAAiB,CACnB,GAGMC,EAAiBZ,EAAKE,MAAM,CAAC,CAACC,EAAUC,KAC5C,IACIS,EADEC,EAAO,IAAI7B,KAAKmB,EAAIW,UAAU,EAGpC,OAAQhE,GACN,IAAK,OACH8D,EAAUC,EAAKrB,WAAW,GAAGuB,KAAK,CAAC,EAAG,IAAM,cAC5C,KACF,KAAK,MAWL,QAVEH,EAAUC,EAAKrB,WAAW,GAAGuB,KAAK,CAAC,EAAG,IAAM,iBAC5C,KACF,KAAK,OACH,IAAMC,EAAY,IAAIhC,KAAK6B,GAC3BG,EAAUC,OAAO,CAACJ,EAAKK,OAAO,GAAKL,EAAKM,MAAM,IAC9CP,EAAUI,EAAUxB,WAAW,GAAGuB,KAAK,CAAC,EAAG,IAAM,iBACjD,KACF,KAAK,QACHH,EAAUC,EAAKrB,WAAW,GAAGuB,KAAK,CAAC,EAAG,GAAK,mBAI/C,CA4BA,OA1BI,CAAI,CAACH,EAAQ,EAAE,CACjBV,CAAG,CAACU,EAAQ,CAAG,CACbQ,UAAWR,EACXS,SAAU,EACVC,OAAQ,EACRC,KAAM,EACNC,aAAc,EACdC,WAAY,EACZC,OAAQ,CACV,GAGFxB,CAAG,CAACU,EAAQ,CAACS,QAAQ,EAAI,EACzBnB,CAAG,CAACU,EAAQ,CAACU,MAAM,EAAInB,EAAIE,YAAY,EAAI,EAC3CH,CAAG,CAACU,EAAQ,CAACW,IAAI,EAAIpB,EAAII,QAAQ,EAAI,EAEb,KAAK,CAAzBJ,EAAIM,WAAW,CACjBP,CAAG,CAACU,EAAQ,CAACa,UAAU,EAAI,EAE3BvB,CAAG,CAACU,EAAQ,CAACc,MAAM,EAAI,EAGzBxB,CAAG,CAACU,EAAQ,CAACY,YAAY,CAAGtB,CAAG,CAACU,EAAQ,CAACS,QAAQ,CAAG,EAChD,CAAI,CAACT,EAAQ,CAACa,UAAU,CAAGvB,CAAG,CAACU,EAAQ,CAACS,QAAQ,CAAI,IACpD,EAEGnB,CACT,EAAG,CAAC,GAGEyB,EAAaC,OAAOC,MAAM,CAAClB,GAAgBmB,IAAI,CAAC,CAACC,EAAQC,IAC7D,IAAIhD,KAAK+C,EAAEX,SAAS,EAAEa,OAAO,GAAK,IAAIjD,KAAKgD,EAAEZ,SAAS,EAAEa,OAAO,IAI3DC,EAAoBnC,EAAKE,MAAM,CAAC,CAACC,EAAUC,KAC/C,IAAMjD,EAAWiD,EAAIgC,aAAa,EAAI,UAYtC,OAXI,CAAI,CAACjF,EAAS,EAAE,CAClBgD,CAAG,CAAChD,EAAS,CAAG,CACdA,SAAUA,EACVmE,SAAU,EACVC,OAAQ,EACRC,KAAM,CACR,GAEFrB,CAAG,CAAChD,EAAS,CAACmE,QAAQ,EAAI,EAC1BnB,CAAG,CAAChD,EAAS,CAACoE,MAAM,EAAInB,EAAIE,YAAY,EAAI,EAC5CH,CAAG,CAAChD,EAAS,CAACqE,IAAI,EAAIpB,EAAII,QAAQ,EAAI,EAC/BL,CACT,EAAG,CAAC,GAEEkC,EAAiBrC,EAAKE,MAAM,CAAC,CAACC,EAAUC,KAC5C,IAAMhD,EAAQgD,EAAIkC,UAAU,EAAI,UAYhC,OAXKnC,CAAG,CAAC/C,EAAM,EAAE,CACf+C,CAAG,CAAC/C,EAAM,CAAG,CACXA,MAAOA,EACPkE,SAAU,EACVC,OAAQ,EACRC,KAAM,EACR,EAEFrB,CAAG,CAAC/C,EAAM,CAACkE,QAAQ,EAAI,EACvBnB,CAAG,CAAC/C,EAAM,CAACmE,MAAM,EAAInB,EAAIE,YAAY,EAAI,EACzCH,CAAG,CAAC/C,EAAM,CAACoE,IAAI,EAAIpB,EAAII,QAAQ,EAAI,EAC5BL,CACT,EAAG,CAAC,GAcJ,OAXA5D,EAAegG,WAAW,CACxBrE,EACAZ,EACA,CACEW,CAJU1B,UAIE,IACZiG,UAAW,kBACXC,aAAc,WAChB,EACArE,GACAsE,KAAK,CAACC,QAAQ/E,KAAK,EAEdF,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvBiF,OAAQ,eACRC,OAAQ,CACNlG,WAAYuC,EAAUO,WAAW,GACjC3C,SAAUkC,EAAQS,WAAW,GAC7B1C,YAAaA,CACf,EACAkD,OAAQ,CACN,GAAGA,CAAM,CACTwB,aAAcxB,EAAOI,cAAc,CAAG,EAClC,EAAQI,mBAAmB,CAAGR,EAAOI,cAAc,CAAI,IACvD,CACN,EACAyC,YAAalB,EACbmB,WAAY,CACVC,YAAanB,OAAOC,MAAM,CAACK,GAC3Bc,SAAUpB,OAAOC,MAAM,CAACO,EAC1B,EACAa,gBAAiB,WACfjG,EACAE,iBACAC,CACF,CACF,EAAG,CACD+F,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,eAChC,+BAAgC,yCAChC,wBAAyB,GAAGjE,EAAUO,WAAW,GAAG,IAAI,EAAET,EAAQS,WAAW,IAAI,CACjF,0BAA2BQ,EAAOI,cAAc,CAAC+C,QAAQ,EAC3D,CACF,EAEF,CAAE,MAAOxF,EAAO,CAEd,OAAOF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,wBACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,EAElB,CACF,CAGO,eAAeqF,IACpB,OAAO,IAAI3F,EAAAA,EAAYA,CAAC,KAAM,CAC5BM,OAAQ,IACRmF,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,eAChC,+BAAgC,wCAClC,CACF,EACF,CCjTA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,oCACA,kCACA,iBACA,4CACA,CAAK,CACL,0FACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,6EACA,EAFA,4BAEA,2BACA,OACI,QAA8B,EAClC,oCACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA0B,aAAe,kDAAyD,wOAAuQ,ySAAoU,mBAAmB,QAAQ,uDAA2D,gGAAwG,EAAE,oGAA4G,EAAE,kGAA0G,EAAE,+FAAuG,EAAE,uEAA+E,EAAE,kFAA0F,EAAE,0FAAkG,EAAE,uFAA+F,iBAAsB,gBAAkB,uBAAyB,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,gEAAoE,6BAAoC,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,o/BAAmsC,qBAAyB,ykDAAkmD,idAAge,OAAS,SAAS,qCAAyC,iCAAmC,WAAa,0CAAkD,MAAQ,YAAc,iBAAmB,sBAAwB,uBAiBruM,CAAC,CAAC,EAAC,sBCvBH,wDCAA,mGC6CO,SAAS9D,EAAsC/B,CAAoB,EACxE,MAAOgG,CAAAA,EAAAA,EAAAA,kBAAAA,CAAkBA,CACvBC,0CAAoC,CACpCA,kNAAyC,CACzC,CACEC,QAAS,KACP/E,GACSnB,CADO,CACCkG,OAAO,CAAC/E,GAAG,CAACgF,IAAOC,MAEpCC,IAAIF,CAAY,CAAEC,CAAa,CAAEE,CAAsB,EAGvD,EACAC,OAAOJ,CAAY,CAAEG,CAAsB,EAG3C,CACF,CACF,EAEJ", "sources": ["webpack://_N_E/./src/app/api/external/v1/usage/route.ts", "webpack://_N_E/./src/app/api/external/v1/usage/route.ts?06aa", "webpack://_N_E/", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/./src/lib/supabase/server.ts"], "sourcesContent": ["import { type NextRequest, NextResponse } from 'next/server';\nimport { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';\nimport { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';\nimport { z } from 'zod';\n\n// Use Edge Runtime for better performance\nexport const runtime = 'edge';\n\nconst authMiddleware = new ApiKeyAuthMiddleware();\n\n// Validation schema for usage query parameters\nconst UsageQuerySchema = z.object({\n  start_date: z.string().datetime().optional(),\n  end_date: z.string().datetime().optional(),\n  granularity: z.enum(['hour', 'day', 'week', 'month']).default('day'),\n  config_id: z.string().uuid().optional(),\n  provider: z.string().optional(),\n  model: z.string().optional()\n});\n\n// GET /api/external/v1/usage - Get usage statistics and analytics\nexport async function GET(request: NextRequest) {\n  try {\n    // 1. Authenticate using user-generated API key\n    const authResult = await authMiddleware.authenticateRequest(request);\n    \n    if (!authResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: authResult.error,\n            type: 'authentication_error',\n            code: 'invalid_api_key'\n          }\n        },\n        { status: authResult.statusCode || 401 }\n      );\n    }\n\n    const { userApiKey, userConfig, ipAddress } = authResult;\n\n    // 2. Parse and validate query parameters\n    const { searchParams } = new URL(request.url);\n    const queryParams = {\n      start_date: searchParams.get('start_date') || undefined,\n      end_date: searchParams.get('end_date') || undefined,\n      granularity: searchParams.get('granularity') || 'day',\n      config_id: searchParams.get('config_id') || undefined,\n      provider: searchParams.get('provider') || undefined,\n      model: searchParams.get('model') || undefined\n    };\n\n    const validationResult = UsageQuerySchema.safeParse(queryParams);\n    if (!validationResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Invalid query parameters',\n            type: 'validation_error',\n            code: 'invalid_parameters',\n            details: validationResult.error.errors\n          }\n        },\n        { status: 400 }\n      );\n    }\n\n    const { start_date, end_date, granularity, config_id, provider, model } = validationResult.data;\n\n    // 3. Set default date range if not provided (last 30 days)\n    const endDate = end_date ? new Date(end_date) : new Date();\n    const startDate = start_date ? new Date(start_date) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);\n\n    const supabase = createSupabaseServerClientFromRequest(request);\n\n    // 4. Get user's API key usage logs\n    let query = supabase\n      .from('user_api_key_usage_logs')\n      .select(`\n        id,\n        created_at,\n        status_code,\n        model_used,\n        provider_used,\n        request_tokens,\n        response_tokens,\n        total_tokens,\n        cost_usd,\n        user_generated_api_keys!inner(\n          id,\n          key_name,\n          custom_api_config_id,\n          custom_api_configs!inner(\n            id,\n            name,\n            user_id\n          )\n        )\n      `)\n      .gte('created_at', startDate.toISOString())\n      .lte('created_at', endDate.toISOString())\n      .order('created_at', { ascending: false });\n\n    // Filter by user's API keys only\n    query = query.eq('user_generated_api_keys.custom_api_configs.user_id', userConfig!.user_id);\n\n    // Apply additional filters\n    if (config_id) {\n      query = query.eq('user_generated_api_keys.custom_api_config_id', config_id);\n    }\n    if (provider) {\n      query = query.eq('provider_used', provider);\n    }\n    if (model) {\n      query = query.eq('model_used', model);\n    }\n\n    const { data: usageLogs, error } = await query;\n\n    if (error) {\n      console.error('Error fetching usage logs:', error);\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Failed to fetch usage data',\n            type: 'server_error',\n            code: 'database_error'\n          }\n        },\n        { status: 500 }\n      );\n    }\n\n    // 5. Process and aggregate usage data\n    const logs = usageLogs || [];\n    \n    // Calculate totals\n    const totals = logs.reduce((acc, log) => ({\n      total_requests: acc.total_requests + 1,\n      total_tokens: acc.total_tokens + (log.total_tokens || 0),\n      total_cost: acc.total_cost + (log.cost_usd || 0),\n      successful_requests: acc.successful_requests + (log.status_code === 200 ? 1 : 0),\n      failed_requests: acc.failed_requests + (log.status_code !== 200 ? 1 : 0)\n    }), {\n      total_requests: 0,\n      total_tokens: 0,\n      total_cost: 0,\n      successful_requests: 0,\n      failed_requests: 0\n    });\n\n    // Group by time period for time series data\n    const timeSeriesData = logs.reduce((acc: any, log) => {\n      const date = new Date(log.created_at);\n      let timeKey: string;\n\n      switch (granularity) {\n        case 'hour':\n          timeKey = date.toISOString().slice(0, 13) + ':00:00.000Z';\n          break;\n        case 'day':\n          timeKey = date.toISOString().slice(0, 10) + 'T00:00:00.000Z';\n          break;\n        case 'week':\n          const weekStart = new Date(date);\n          weekStart.setDate(date.getDate() - date.getDay());\n          timeKey = weekStart.toISOString().slice(0, 10) + 'T00:00:00.000Z';\n          break;\n        case 'month':\n          timeKey = date.toISOString().slice(0, 7) + '-01T00:00:00.000Z';\n          break;\n        default:\n          timeKey = date.toISOString().slice(0, 10) + 'T00:00:00.000Z';\n      }\n\n      if (!acc[timeKey]) {\n        acc[timeKey] = {\n          timestamp: timeKey,\n          requests: 0,\n          tokens: 0,\n          cost: 0,\n          success_rate: 0,\n          successful: 0,\n          failed: 0\n        };\n      }\n\n      acc[timeKey].requests += 1;\n      acc[timeKey].tokens += log.total_tokens || 0;\n      acc[timeKey].cost += log.cost_usd || 0;\n      \n      if (log.status_code === 200) {\n        acc[timeKey].successful += 1;\n      } else {\n        acc[timeKey].failed += 1;\n      }\n\n      acc[timeKey].success_rate = acc[timeKey].requests > 0 \n        ? (acc[timeKey].successful / acc[timeKey].requests) * 100 \n        : 0;\n\n      return acc;\n    }, {});\n\n    // Convert to array and sort by timestamp\n    const timeSeries = Object.values(timeSeriesData).sort((a: any, b: any) => \n      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()\n    );\n\n    // Group by provider and model for breakdown\n    const providerBreakdown = logs.reduce((acc: any, log) => {\n      const provider = log.provider_used || 'unknown';\n      if (!acc[provider]) {\n        acc[provider] = {\n          provider: provider,\n          requests: 0,\n          tokens: 0,\n          cost: 0\n        };\n      }\n      acc[provider].requests += 1;\n      acc[provider].tokens += log.total_tokens || 0;\n      acc[provider].cost += log.cost_usd || 0;\n      return acc;\n    }, {});\n\n    const modelBreakdown = logs.reduce((acc: any, log) => {\n      const model = log.model_used || 'unknown';\n      if (!acc[model]) {\n        acc[model] = {\n          model: model,\n          requests: 0,\n          tokens: 0,\n          cost: 0\n        };\n      }\n      acc[model].requests += 1;\n      acc[model].tokens += log.total_tokens || 0;\n      acc[model].cost += log.cost_usd || 0;\n      return acc;\n    }, {});\n\n    // 6. Log API usage\n    authMiddleware.logApiUsage(\n      userApiKey!,\n      request,\n      {\n        statusCode: 200,\n        modelUsed: 'usage_analytics',\n        providerUsed: 'rokey_api',\n      },\n      ipAddress\n    ).catch(console.error);\n\n    return NextResponse.json({\n      object: 'usage_report',\n      period: {\n        start_date: startDate.toISOString(),\n        end_date: endDate.toISOString(),\n        granularity: granularity\n      },\n      totals: {\n        ...totals,\n        success_rate: totals.total_requests > 0 \n          ? (totals.successful_requests / totals.total_requests) * 100 \n          : 0\n      },\n      time_series: timeSeries,\n      breakdowns: {\n        by_provider: Object.values(providerBreakdown),\n        by_model: Object.values(modelBreakdown)\n      },\n      filters_applied: {\n        config_id,\n        provider,\n        model\n      }\n    }, {\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n        'X-RouKey-Usage-Period': `${startDate.toISOString()} to ${endDate.toISOString()}`,\n        'X-RouKey-Total-Requests': totals.total_requests.toString(),\n      }\n    });\n\n  } catch (error) {\n    console.error('Error in usage GET API:', error);\n    return NextResponse.json(\n      {\n        error: {\n          message: 'Internal server error',\n          type: 'server_error',\n          code: 'internal_error'\n        }\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// OPTIONS handler for CORS\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n    },\n  });\n}\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\usage\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/external/v1/usage/route\",\n        pathname: \"/api/external/v1/usage\",\n        filename: \"route\",\n        bundlePath: \"app/api/external/v1/usage/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\usage\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fexternal%2Fv1%2Fusage%2Froute&page=%2Fapi%2Fexternal%2Fv1%2Fusage%2Froute&pagePath=private-next-app-dir%2Fapi%2Fexternal%2Fv1%2Fusage%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&appPaths=%2Fapi%2Fexternal%2Fv1%2Fusage%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/external/v1/usage/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":true},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.mjs\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\",\"image/avif\"],\"dangerouslyAllowSVG\":true,\"contentSecurityPolicy\":\"default-src 'self'; script-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"raw.githubusercontent.com\",\"port\":\"\",\"pathname\":\"/lobehub/lobe-icons/**\"},{\"protocol\":\"https\",\"hostname\":\"registry.npmmirror.com\",\"port\":\"\",\"pathname\":\"/@lobehub/icons-static-png/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@latest/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@v11/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"images.unsplash.com\",\"port\":\"\",\"pathname\":\"/**\"},{\"protocol\":\"https\",\"hostname\":\"cloud.gmelius.com\",\"port\":\"\",\"pathname\":\"/public/logos/**\"},{\"protocol\":\"https\",\"hostname\":\"upload.wikimedia.org\",\"port\":\"\",\"pathname\":\"/wikipedia/commons/**\"},{\"protocol\":\"https\",\"hostname\":\"kstatic.googleusercontent.com\",\"port\":\"\",\"pathname\":\"/files/**\"}],\"unoptimized\":false},\"devIndicators\":{\"position\":\"bottom-left\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"C:\\\\RoKey App\\\\rokey-app\",\"experimental\":{\"nodeMiddleware\":false,\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"dynamicOnHover\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":3,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":true,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":true,\"largePageDataBytes\":128000,\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"viewTransition\":false,\"routerBFCache\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"useCache\":false,\"optimizePackageImports\":[\"@heroicons/react\",\"@headlessui/react\",\"react-markdown\",\"react-syntax-highlighter\",\"@supabase/supabase-js\",\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"htmlLimitedBots\":\"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti\",\"bundlePagesRouterDependencies\":false,\"configFile\":\"C:\\\\RoKey App\\\\rokey-app\\\\next.config.mjs\",\"configFileName\":\"next.config.mjs\",\"serverExternalPackages\":[\"pdf-parse\",\"mammoth\"],\"turbopack\":{\"rules\":{\"*.svg\":{\"loaders\":[\"@svgr/webpack\"],\"as\":\"*.js\"}},\"root\":\"C:\\\\RoKey App\\\\rokey-app\"},\"compiler\":{\"removeConsole\":true,\"reactRemoveProperties\":true},\"api\":{\"bodyParser\":{\"sizeLimit\":\"50mb\"},\"responseLimit\":\"50mb\"},\"_originalRedirects\":[]}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/external/v1/usage/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/external/v1/usage/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "module.exports = require(\"node:buffer\");", "module.exports = require(\"node:async_hooks\");", "import { createServerClient, type CookieOptions } from '@supabase/ssr';\r\nimport { createClient } from '@supabase/supabase-js';\r\nimport { cookies } from 'next/headers';\r\nimport { NextRequest } from 'next/server';\r\n\r\n// This is the standard setup for creating a Supabase server client\r\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\r\n// Updated for Next.js 15 async cookies requirement\r\nexport async function createSupabaseServerClientOnRequest() {\r\n  const cookieStore = await cookies();\r\n\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        get(name: string) {\r\n          return cookieStore.get(name)?.value;\r\n        },\r\n        set(name: string, value: string, options: CookieOptions) {\r\n          try {\r\n            cookieStore.set({ name, value, ...options });\r\n          } catch (error) {\r\n            // This error can be ignored if running in a Server Component\r\n            // where cookies can't be set directly. Cookie setting should be\r\n            // handled in Server Actions or Route Handlers.\r\n            console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\r\n          }\r\n        },\r\n        remove(name: string, options: CookieOptions) {\r\n          try {\r\n            // To remove a cookie using the `set` method from `next/headers`,\r\n            // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\r\n            cookieStore.set({ name, value: '', ...options });\r\n          } catch (error) {\r\n            // Similar to set, this might fail in a Server Component.\r\n            console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\r\n          }\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n\r\n// Alternative method for API routes that need to handle cookies from request\r\nexport function createSupabaseServerClientFromRequest(request: NextRequest) {\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        get(name: string) {\r\n          return request.cookies.get(name)?.value;\r\n        },\r\n        set(name: string, value: string, options: CookieOptions) {\r\n          // In API routes, we can't set cookies directly on the request\r\n          // This will be handled by the response\r\n        },\r\n        remove(name: string, options: CookieOptions) {\r\n          // In API routes, we can't remove cookies directly on the request\r\n          // This will be handled by the response\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n\r\n// Service role client for admin operations (OAuth token storage, etc.)\r\nexport function createServiceRoleClient() {\r\n  return createClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.SUPABASE_SERVICE_ROLE_KEY!,\r\n    {\r\n      auth: {\r\n        autoRefreshToken: false,\r\n        persistSession: false\r\n      }\r\n    }\r\n  );\r\n}\r\n"], "names": ["runtime", "authMiddleware", "ApiKeyAuthMiddleware", "UsageQuerySchema", "z", "start_date", "datetime", "optional", "end_date", "granularity", "default", "config_id", "uuid", "provider", "model", "GET", "request", "authResult", "authenticateRequest", "success", "NextResponse", "json", "error", "message", "type", "code", "status", "statusCode", "userApiKey", "userConfig", "ip<PERSON><PERSON><PERSON>", "searchParams", "URL", "url", "queryParams", "get", "undefined", "validationResult", "safeParse", "details", "errors", "data", "endDate", "Date", "startDate", "now", "query", "createSupabaseServerClientFromRequest", "from", "select", "gte", "toISOString", "lte", "order", "ascending", "eq", "user_id", "usageLogs", "logs", "totals", "reduce", "acc", "log", "total_requests", "total_tokens", "total_cost", "cost_usd", "successful_requests", "status_code", "failed_requests", "timeSeriesData", "<PERSON><PERSON><PERSON>", "date", "created_at", "slice", "weekStart", "setDate", "getDate", "getDay", "timestamp", "requests", "tokens", "cost", "success_rate", "successful", "failed", "timeSeries", "Object", "values", "sort", "a", "b", "getTime", "providerBreakdown", "provider_used", "modelBreakdown", "model_used", "logApiUsage", "modelUsed", "providerUsed", "catch", "console", "object", "period", "time_series", "breakdowns", "by_provider", "by_model", "filters_applied", "headers", "toString", "OPTIONS", "createServerClient", "process", "cookies", "name", "value", "set", "options", "remove"], "sourceRoot": "", "ignoreList": []}