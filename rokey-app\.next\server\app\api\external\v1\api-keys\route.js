(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[309],{5356:e=>{"use strict";e.exports=require("node:buffer")},5521:e=>{"use strict";e.exports=require("node:async_hooks")},9004:(e,t,a)=>{"use strict";a.r(t),a.d(t,{ComponentMod:()=>P,default:()=>T});var r,i={};a.r(i),a.d(i,{GET:()=>x,OPTIONS:()=>w,POST:()=>v,runtime:()=>h});var s={};a.r(s),a.d(s,{patchFetch:()=>R,routeModule:()=>b,serverHooks:()=>A,workAsyncStorage:()=>C,workUnitAsyncStorage:()=>S});var o=a(8429),n=a(9874),c=a(8294),l=a(6567),p=a(4144),d=a(5421),u=a(974),m=a(4429),f=a(9975),_=a(4261),g=a(1109);let h="edge",y=new m.S,k=g.z.object({config_id:g.z.string().uuid(),key_name:g.z.string().min(1).max(100),expires_at:g.z.string().datetime().optional(),permissions:g.z.object({chat:g.z.boolean().optional().default(!0),streaming:g.z.boolean().optional().default(!0),all_models:g.z.boolean().optional().default(!0)}).optional().default({chat:!0,streaming:!0,all_models:!0}),allowed_ips:g.z.array(g.z.string().ip()).optional().default([]),allowed_domains:g.z.array(g.z.string()).optional().default([])});async function x(e){try{let t=await y.authenticateRequest(e);if(!t.success)return u.Rp.json({error:{message:t.error,type:"authentication_error",code:"invalid_api_key"}},{status:t.statusCode||401});let{userApiKey:a,userConfig:r,ipAddress:i}=t,{searchParams:s}=new URL(e.url),o=s.get("config_id"),n=s.get("status"),c=(0,f.Qb)(e).from("user_generated_api_keys").select(`
        id,
        key_name,
        key_prefix,
        permissions,
        allowed_ips,
        allowed_domains,
        total_requests,
        last_used_at,
        status,
        expires_at,
        created_at,
        updated_at,
        custom_api_configs!inner(
          id,
          name,
          user_id
        )
      `).eq("custom_api_configs.user_id",r.user_id).order("created_at",{ascending:!1});o&&(c=c.eq("custom_api_config_id",o)),n&&(c=c.eq("status",n));let{data:l,error:p}=await c;if(p)return u.Rp.json({error:{message:"Failed to fetch API keys",type:"server_error",code:"database_error"}},{status:500});let d=(l||[]).map(e=>({id:e.id,object:"api_key",key_name:e.key_name,key_prefix:e.key_prefix,masked_key:`${e.key_prefix}_${"*".repeat(28)}xxxx`,permissions:e.permissions,allowed_ips:e.allowed_ips,allowed_domains:e.allowed_domains,total_requests:e.total_requests,last_used_at:e.last_used_at,status:e.status,expires_at:e.expires_at,created_at:e.created_at,updated_at:e.updated_at,config:{id:e.custom_api_configs.id,name:e.custom_api_configs.name}}));return y.logApiUsage(a,e,{statusCode:200,modelUsed:"api_key_management",providerUsed:"rokey_api"},i).catch(console.error),u.Rp.json({object:"list",data:d,has_more:!1,total_count:d.length},{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}catch(e){return u.Rp.json({error:{message:"Internal server error",type:"server_error",code:"internal_error"}},{status:500})}}async function v(e){try{let t=await y.authenticateRequest(e);if(!t.success)return u.Rp.json({error:{message:t.error,type:"authentication_error",code:"invalid_api_key"}},{status:t.statusCode||401});let{userApiKey:a,userConfig:r,ipAddress:i}=t,s=await e.json(),o=k.safeParse(s);if(!o.success)return u.Rp.json({error:{message:"Invalid request data",type:"validation_error",code:"invalid_parameters",details:o.error.errors}},{status:400});let n=o.data,c=(0,f.Qb)(e),{data:l,error:p}=await c.from("custom_api_configs").select("id, name").eq("id",n.config_id).eq("user_id",r.user_id).single();if(p||!l)return u.Rp.json({error:{message:"Configuration not found",type:"not_found_error",code:"config_not_found"}},{status:404});let{fullKey:d,prefix:m,secretPart:g,hash:h}=await _.F.generateApiKey(),x=await _.F.encryptSuffix(g),{data:v,error:w}=await c.from("user_generated_api_keys").insert({user_id:r.user_id,custom_api_config_id:n.config_id,key_name:n.key_name,key_prefix:m,key_hash:h,encrypted_key_suffix:x,permissions:n.permissions,allowed_ips:n.allowed_ips,allowed_domains:n.allowed_domains,expires_at:n.expires_at||null}).select().single();if(w)return u.Rp.json({error:{message:"Failed to create API key",type:"server_error",code:"database_error"}},{status:500});return y.logApiUsage(a,e,{statusCode:201,modelUsed:"api_key_management",providerUsed:"rokey_api"},i).catch(console.error),u.Rp.json({id:v.id,object:"api_key",key_name:v.key_name,api_key:d,key_prefix:v.key_prefix,permissions:v.permissions,allowed_ips:v.allowed_ips,allowed_domains:v.allowed_domains,status:v.status,expires_at:v.expires_at,created_at:v.created_at,config:{id:l.id,name:l.name},warning:"Save this API key now - this is the only time you will see it in full!"},{status:201,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}catch(e){return u.Rp.json({error:{message:"Internal server error",type:"server_error",code:"internal_error"}},{status:500})}}async function w(){return new u.Rp(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}g.z.object({key_name:g.z.string().min(1).max(100).optional(),status:g.z.enum(["active","inactive"]).optional(),expires_at:g.z.string().datetime().nullable().optional(),permissions:g.z.object({chat:g.z.boolean().optional(),streaming:g.z.boolean().optional(),all_models:g.z.boolean().optional()}).optional(),allowed_ips:g.z.array(g.z.string().ip()).optional(),allowed_domains:g.z.array(g.z.string()).optional()});let b=new l.AppRouteRouteModule({definition:{kind:p.A.APP_ROUTE,page:"/api/external/v1/api-keys/route",pathname:"/api/external/v1/api-keys",filename:"route",bundlePath:"app/api/external/v1/api-keys/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\external\\v1\\api-keys\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:C,workUnitAsyncStorage:S,serverHooks:A}=b;function R(){return(0,d.V5)({workAsyncStorage:C,workUnitAsyncStorage:S})}let z=null==(r=self.__RSC_MANIFEST)?void 0:r["/api/external/v1/api-keys/route"],I=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);z&&I&&(0,n.fQ)({page:"/api/external/v1/api-keys/route",clientReferenceManifest:z,serverActionsManifest:I,serverModuleMap:(0,o.e)({serverActionsManifest:I})});let P=s,T=c.s.wrap(b,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!0},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp","image/avif"],dangerouslyAllowSVG:!0,contentSecurityPolicy:"default-src 'self'; script-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"raw.githubusercontent.com",port:"",pathname:"/lobehub/lobe-icons/**"},{protocol:"https",hostname:"registry.npmmirror.com",port:"",pathname:"/@lobehub/icons-static-png/**"},{protocol:"https",hostname:"cdn.jsdelivr.net",port:"",pathname:"/npm/simple-icons@latest/icons/**"},{protocol:"https",hostname:"cdn.jsdelivr.net",port:"",pathname:"/npm/simple-icons@v11/icons/**"},{protocol:"https",hostname:"images.unsplash.com",port:"",pathname:"/**"},{protocol:"https",hostname:"cloud.gmelius.com",port:"",pathname:"/public/logos/**"},{protocol:"https",hostname:"upload.wikimedia.org",port:"",pathname:"/wikipedia/commons/**"},{protocol:"https",hostname:"kstatic.googleusercontent.com",port:"",pathname:"/files/**"}],unoptimized:!1},devIndicators:{position:"bottom-left"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"C:\\RoKey App\\rokey-app",experimental:{nodeMiddleware:!1,cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,dynamicOnHover:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!0,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!0,largePageDataBytes:128e3,typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,viewTransition:!1,routerBFCache:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,useCache:!1,optimizePackageImports:["@heroicons/react","@headlessui/react","react-markdown","react-syntax-highlighter","@supabase/supabase-js","lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},htmlLimitedBots:"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti",bundlePagesRouterDependencies:!1,configFile:"C:\\RoKey App\\rokey-app\\next.config.mjs",configFileName:"next.config.mjs",serverExternalPackages:["pdf-parse","mammoth"],turbopack:{rules:{"*.svg":{loaders:["@svgr/webpack"],as:"*.js"}},root:"C:\\RoKey App\\rokey-app"},compiler:{removeConsole:!0,reactRemoveProperties:!0},_originalRedirects:[]}})},9975:(e,t,a)=>{"use strict";a.d(t,{Qb:()=>i});var r=a(3339);function i(e){return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,a){},remove(e,t){}}})}a(2710)}},e=>{var t=t=>e(e.s=t);e.O(0,[580,918,109,44,833],()=>t(9004));var a=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/external/v1/api-keys/route"]=a}]);
//# sourceMappingURL=route.js.map