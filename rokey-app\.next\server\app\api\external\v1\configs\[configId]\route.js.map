{"version": 3, "file": "app/api/external/v1/configs/[configId]/route.js", "mappings": "gJEAA,2TFMO,IAAMA,EAAU,OAEjBC,EAAiB,IAAIC,EAAAA,CAAoBA,CASzCC,EAAqBC,EAAAA,CAAAA,CAAAA,IATPH,EASe,CAAC,CAClCI,KAAMD,EAAAA,CAAAA,CAAAA,MAAQ,GAAGE,GAAG,CAAC,GAAGC,GAAG,CAAC,KAAKC,QAAQ,GACzCC,YAAaL,EAAAA,CAAAA,CAAAA,MAAQ,GAAGG,GAAG,CAAC,KAAKC,QAAQ,GACzCE,iBAAkBN,EAAAA,CAAAA,CAAAA,IAAM,CAAC,CAAC,iBAAkB,eAAgB,aAAa,EAAEI,QAAQ,GACnFG,SAAUP,EAAAA,CAAAA,CAAAA,MAAQ,CAAC,CACjBQ,YAAaR,EAAAA,CAAAA,CAAAA,MAAQ,GAAGE,GAAG,CAAC,GAAGC,GAAG,CAAC,GAAGC,QAAQ,GAC9CK,WAAYT,EAAAA,CAAAA,CAAAA,MAAQ,GAAGU,GAAG,GAAGC,QAAQ,GAAGP,QAAQ,GAChDQ,MAAOZ,EAAAA,CAAAA,CAAAA,MAAQ,GAAGE,GAAG,CAAC,GAAGC,GAAG,CAAC,GAAGC,QAAQ,GACxCS,kBAAmBb,EAAAA,CAAAA,CAAAA,MAAQ,GAAGE,GAAG,CAAC,CAAC,GAAGC,GAAG,CAAC,GAAGC,QAAQ,GACrDU,iBAAkBd,EAAAA,CAAAA,CAAAA,MAAQ,GAAGE,GAAG,CAAC,CAAC,GAAGC,GAAG,CAAC,GAAGC,QAAQ,EACtD,GAAGA,QAAQ,EACb,GAGO,eAAeW,EAAIC,CAAoB,CAAE,QAAEC,CAAM,CAAe,EACrE,GAAI,CAEF,IAAMC,EAAa,MAAMrB,EAAesB,kBAADtB,CAAoB,CAACmB,GAE5D,GAAI,CAACE,EAAWE,OAAO,CACrB,CADuB,MAChBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAASN,EAAWK,KAAK,CACzBE,KAAM,uBACNC,KAAM,iBACR,CACF,EACA,CAAEC,OAAQT,EAAWU,UAAU,EAAI,GAAI,GAI3C,GAAM,YAAEC,CAAU,YAAEC,CAAU,WAAEC,CAAS,CAAE,CAAGb,EACxC,UAAEc,CAAQ,CAAE,CAAG,MAAMf,EAGrBgB,EAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAqCA,CAAClB,GAEjD,CAAEmB,KAAMC,CAAM,OAAEb,CAAK,CAAE,CAAG,MAAMU,EACnCI,IAAI,CAAC,sBACLC,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;MAiBT,CAAC,EACAC,EAAE,CAAC,KAAMP,GACTO,EAAE,CAAC,UAAWT,EAAYU,OAAO,EACjCC,MAAM,GAET,GAAIlB,GAAS,CAACa,EACZ,MADoB,CACbf,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,0BACTC,KAAM,kBACNC,KAAM,kBACR,CACF,EACA,CAAEC,OAAQ,GAAI,GAgBlB,OAXA9B,EAAe6C,WAAW,CACxBb,EACAb,EACA,CACEY,CAJU/B,UAIE,IACZ8C,UAAW,oBACXC,aAAc,WAChB,EACAb,GACAc,KAAK,CAACC,QAAQvB,KAAK,EAEdF,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvByB,GAAIX,EAAOW,EAAE,CACbC,OAAQ,SACR/C,KAAMmC,EAAOnC,IAAI,CACjBI,YAAa+B,EAAO/B,WAAW,CAC/BC,iBAAkB8B,EAAO9B,gBAAgB,CACzCC,SAAU6B,EAAO7B,QAAQ,CACzB0C,WAAYb,EAAOa,UAAU,CAC7BC,WAAYd,EAAOc,UAAU,CAC7BC,SAAUf,EAAOe,QAAQ,EAAI,EAC/B,EAAG,CACDC,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,4BAChC,+BAAgC,wCAClC,CACF,EAEF,CAAE,MAAO7B,EAAO,CAEd,OAAOF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,wBACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,EAElB,CACF,CAGO,eAAe0B,EAAIrC,CAAoB,CAAE,QAAEC,CAAM,CAAe,EACrE,GAAI,CAEF,IAAMC,EAAa,MAAMrB,EAAesB,kBAADtB,CAAoB,CAACmB,GAE5D,GAAI,CAACE,EAAWE,OAAO,CACrB,CADuB,MAChBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAASN,EAAWK,KAAK,CACzBE,KAAM,uBACNC,KAAM,iBACR,CACF,EACA,CAAEC,OAAQT,EAAWU,UAAU,EAAI,GAAI,GAI3C,GAAM,YAAEC,CAAU,YAAEC,CAAU,WAAEC,CAAS,CAAE,CAAGb,EACxC,CAAEc,UAAQ,CAAE,CAAG,MAAMf,EAGrBqC,EAAO,MAAMtC,EAAQM,IAAI,GACzBiC,EAAmBxD,EAAmByD,SAAS,CAACF,GAEtD,GAAI,CAACC,EAAiBnC,OAAO,CAC3B,CAD6B,MACtBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,uBACTC,KAAM,mBACNC,KAAM,qBACN+B,QAASF,EAAiBhC,KAAK,CAACmC,MAAM,CAE1C,EACA,CAAE/B,OAAQ,GAAI,GAIlB,IAAMgC,EAAaJ,EAAiBpB,IAAI,CAGlCF,EAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAqCA,CAAClB,GAEjD,CAAEmB,KAAMyB,CAAa,OAAErC,CAAK,CAAE,CAAG,MAAMU,EAC1CI,IAAI,CAAC,sBACLwB,MAAM,CAAC,CACN,GAAGF,CAAU,CACbT,WAAY,IAAIY,OAAOC,WAAW,EACpC,GACCxB,EAAE,CAAC,KAAMP,GACTO,EAAE,CAAC,UAAWT,EAAYU,OAAO,EACjCF,MAAM,GACNG,MAAM,GAET,GAAIlB,GAAS,CAACqC,EACZ,OAAOvC,EAAAA,EAAYA,CAACC,CADO,GACH,CACtB,CACEC,MAAO,CACLC,QAAS,8CACTC,KAAM,kBACNC,KAAM,kBACR,CACF,EACA,CAAEC,OAAQ,GAAI,GAgBlB,OAXA9B,EAAe6C,WAAW,CACxBb,EACAb,EACA,CACEY,CAJU/B,UAIE,IACZ8C,UAAW,oBACXC,aAAc,WAChB,EACAb,GACAc,KAAK,CAACC,QAAQvB,KAAK,EAEdF,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvByB,GAAIa,EAAcb,EAAE,CACpBC,OAAQ,SACR/C,KAAM2D,EAAc3D,IAAI,CACxBI,YAAauD,EAAcvD,WAAW,CACtCC,iBAAkBsD,EAActD,gBAAgB,CAChDC,SAAUqD,EAAcrD,QAAQ,CAChC0C,WAAYW,EAAcX,UAAU,CACpCC,WAAYU,EAAcV,UAAU,EACnC,CACDE,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,4BAChC,+BAAgC,wCAClC,CACF,EAEF,CAAE,MAAO7B,EAAO,CAEd,OAAOF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,wBACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,EAElB,CACF,CAGO,eAAeqC,EAAOhD,CAAoB,CAAE,QAAEC,CAAM,CAAe,EACxE,GAAI,CAEF,IAAMC,EAAa,MAAMrB,EAAesB,kBAADtB,CAAoB,CAACmB,GAE5D,GAAI,CAACE,EAAWE,OAAO,CACrB,CADuB,MAChBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAASN,EAAWK,KAAK,CACzBE,KAAM,uBACNC,KAAM,iBACR,CACF,EACA,CAAEC,OAAQT,EAAWU,UAAU,EAAI,GAAI,GAI3C,GAAM,CAAEC,YAAU,YAAEC,CAAU,WAAEC,CAAS,CAAE,CAAGb,EACxC,UAAEc,CAAQ,CAAE,CAAG,MAAMf,EAGrBgB,EAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAqCA,CAAClB,GAEjD,CAAEmB,KAAMC,CAAM,CAAEb,MAAO0C,CAAU,CAAE,CAAG,MAAMhC,EAC/CI,IAAI,CAAC,sBACLC,MAAM,CAAC,YACPC,EAAE,CAAC,KAAMP,GACTO,EAAE,CAAC,UAAWT,EAAYU,OAAO,EACjCC,MAAM,GAET,GAAIwB,GAAc,CAAC7B,EACjB,MADyB,CAClBf,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,0BACTC,KAAM,kBACNC,KAAM,kBACR,CACF,EACA,CAAEC,OAAQ,GAAI,GAKlB,GAAM,CAAEJ,MAAO2C,CAAW,CAAE,CAAG,MAAMjC,EAClCI,IAAI,CAAC,sBACL8B,MAAM,GACN5B,EAAE,CAAC,KAAMP,GACTO,EAAE,CAAC,UAAWT,EAAYU,OAAO,EAEpC,GAAI0B,EAEF,OAAO7C,EAAAA,EAFQ,CAEKC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,iCACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,GAgBlB,OAXA9B,EAAe6C,WAAW,CACxBb,EACAb,EACA,CACEY,CAJU/B,UAIE,IACZ8C,UAAW,oBACXC,aAAc,WAChB,EACAb,GACAc,KAAK,CAACC,QAAQvB,KAAK,EAEdF,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvByB,GAAIf,EACJgB,OAAQ,SACRoB,SAAS,CACX,EAAG,CACDhB,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,4BAChC,+BAAgC,wCAClC,CACF,EAEF,CAAE,MAAO7B,EAAO,CAEd,OAAOF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,wBACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,EAElB,CACF,CAGO,eAAe0C,IACpB,OAAO,IAAIhD,EAAAA,EAAYA,CAAC,KAAM,CAC5BM,OAAQ,IACRyB,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,4BAChC,+BAAgC,wCAClC,CACF,EACF,CCtWA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,iDACA,+CACA,iBACA,yDACA,CAAK,CACL,wGACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,0FACA,EAFA,4BAEA,2BACA,OACI,QAA8B,EAClC,iDACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA0B,aAAe,kDAAyD,wOAAuQ,ySAAoU,mBAAmB,QAAQ,uDAA2D,gGAAwG,EAAE,oGAA4G,EAAE,kGAA0G,EAAE,+FAAuG,EAAE,uEAA+E,EAAE,kFAA0F,EAAE,0FAAkG,EAAE,uFAA+F,iBAAsB,gBAAkB,uBAAyB,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,gEAAoE,6BAAoC,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,o/BAAmsC,qBAAyB,ykDAAkmD,idAAge,OAAS,SAAS,qCAAyC,iCAAmC,WAAa,0CAAkD,MAAQ,YAAc,iBAAmB,sBAAwB,uBAiBruM,CAAC,CAAC,EAAC,sBCvBH,wDCAA,mGC6CO,SAASlB,EAAsClB,CAAoB,EACxE,MAAOsD,CAAAA,EAAAA,EAAAA,kBAAAA,CAAkBA,CACvBC,0CAAoC,CACpCA,kNAAyC,CACzC,CACEC,QAAS,CACPC,IAAIxE,GACKe,CADO,CACCwD,OAAO,CAACC,GAAG,CAACxE,IAAOyE,MAEpCC,IAAI1E,CAAY,CAAEyE,CAAa,CAAEE,CAAsB,EAGvD,EACAC,OAAO5E,CAAY,CAAE2E,CAAsB,EAG3C,CACF,CACF,EAEJ", "sources": ["webpack://_N_E/./src/app/api/external/v1/configs/[configId]/route.ts", "webpack://_N_E/./src/app/api/external/v1/configs/[configId]/route.ts?0339", "webpack://_N_E/?b0bd", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/./src/lib/supabase/server.ts"], "sourcesContent": ["import { type NextRequest, NextResponse } from 'next/server';\nimport { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';\nimport { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';\nimport { z } from 'zod';\n\n// Use Edge Runtime for better performance\nexport const runtime = 'edge';\n\nconst authMiddleware = new ApiKeyAuthMiddleware();\n\ninterface RouteParams {\n  params: Promise<{\n    configId: string;\n  }>;\n}\n\n// Validation schema\nconst UpdateConfigSchema = z.object({\n  name: z.string().min(1).max(100).optional(),\n  description: z.string().max(500).optional(),\n  routing_strategy: z.enum(['load_balancing', 'role_routing', 'agent_mode']).optional(),\n  settings: z.object({\n    temperature: z.number().min(0).max(2).optional(),\n    max_tokens: z.number().int().positive().optional(),\n    top_p: z.number().min(0).max(1).optional(),\n    frequency_penalty: z.number().min(-2).max(2).optional(),\n    presence_penalty: z.number().min(-2).max(2).optional(),\n  }).optional()\n});\n\n// GET /api/external/v1/configs/{configId} - Get specific configuration\nexport async function GET(request: NextRequest, { params }: RouteParams) {\n  try {\n    // 1. Authenticate using user-generated API key\n    const authResult = await authMiddleware.authenticateRequest(request);\n    \n    if (!authResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: authResult.error,\n            type: 'authentication_error',\n            code: 'invalid_api_key'\n          }\n        },\n        { status: authResult.statusCode || 401 }\n      );\n    }\n\n    const { userApiKey, userConfig, ipAddress } = authResult;\n    const { configId } = await params;\n\n    // 2. Get configuration\n    const supabase = createSupabaseServerClientFromRequest(request);\n    \n    const { data: config, error } = await supabase\n      .from('custom_api_configs')\n      .select(`\n        id,\n        name,\n        description,\n        routing_strategy,\n        settings,\n        created_at,\n        updated_at,\n        api_keys(\n          id,\n          provider,\n          label,\n          status,\n          is_default_general_chat_model,\n          temperature,\n          predefined_models(name, display_name)\n        )\n      `)\n      .eq('id', configId)\n      .eq('user_id', userConfig!.user_id)\n      .single();\n\n    if (error || !config) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Configuration not found',\n            type: 'not_found_error',\n            code: 'config_not_found'\n          }\n        },\n        { status: 404 }\n      );\n    }\n\n    // 3. Log API usage\n    authMiddleware.logApiUsage(\n      userApiKey!,\n      request,\n      {\n        statusCode: 200,\n        modelUsed: 'config_management',\n        providerUsed: 'rokey_api',\n      },\n      ipAddress\n    ).catch(console.error);\n\n    return NextResponse.json({\n      id: config.id,\n      object: 'config',\n      name: config.name,\n      description: config.description,\n      routing_strategy: config.routing_strategy,\n      settings: config.settings,\n      created_at: config.created_at,\n      updated_at: config.updated_at,\n      api_keys: config.api_keys || []\n    }, {\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n      }\n    });\n\n  } catch (error) {\n    console.error('Error in config GET API:', error);\n    return NextResponse.json(\n      {\n        error: {\n          message: 'Internal server error',\n          type: 'server_error',\n          code: 'internal_error'\n        }\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// PUT /api/external/v1/configs/{configId} - Update configuration\nexport async function PUT(request: NextRequest, { params }: RouteParams) {\n  try {\n    // 1. Authenticate using user-generated API key\n    const authResult = await authMiddleware.authenticateRequest(request);\n    \n    if (!authResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: authResult.error,\n            type: 'authentication_error',\n            code: 'invalid_api_key'\n          }\n        },\n        { status: authResult.statusCode || 401 }\n      );\n    }\n\n    const { userApiKey, userConfig, ipAddress } = authResult;\n    const { configId } = await params;\n\n    // 2. Validate request body\n    const body = await request.json();\n    const validationResult = UpdateConfigSchema.safeParse(body);\n\n    if (!validationResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Invalid request data',\n            type: 'validation_error',\n            code: 'invalid_parameters',\n            details: validationResult.error.errors\n          }\n        },\n        { status: 400 }\n      );\n    }\n\n    const updateData = validationResult.data;\n\n    // 3. Update configuration\n    const supabase = createSupabaseServerClientFromRequest(request);\n    \n    const { data: updatedConfig, error } = await supabase\n      .from('custom_api_configs')\n      .update({\n        ...updateData,\n        updated_at: new Date().toISOString()\n      })\n      .eq('id', configId)\n      .eq('user_id', userConfig!.user_id)\n      .select()\n      .single();\n\n    if (error || !updatedConfig) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Configuration not found or failed to update',\n            type: 'not_found_error',\n            code: 'config_not_found'\n          }\n        },\n        { status: 404 }\n      );\n    }\n\n    // 4. Log API usage\n    authMiddleware.logApiUsage(\n      userApiKey!,\n      request,\n      {\n        statusCode: 200,\n        modelUsed: 'config_management',\n        providerUsed: 'rokey_api',\n      },\n      ipAddress\n    ).catch(console.error);\n\n    return NextResponse.json({\n      id: updatedConfig.id,\n      object: 'config',\n      name: updatedConfig.name,\n      description: updatedConfig.description,\n      routing_strategy: updatedConfig.routing_strategy,\n      settings: updatedConfig.settings,\n      created_at: updatedConfig.created_at,\n      updated_at: updatedConfig.updated_at\n    }, {\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n      }\n    });\n\n  } catch (error) {\n    console.error('Error in config PUT API:', error);\n    return NextResponse.json(\n      {\n        error: {\n          message: 'Internal server error',\n          type: 'server_error',\n          code: 'internal_error'\n        }\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// DELETE /api/external/v1/configs/{configId} - Delete configuration\nexport async function DELETE(request: NextRequest, { params }: RouteParams) {\n  try {\n    // 1. Authenticate using user-generated API key\n    const authResult = await authMiddleware.authenticateRequest(request);\n    \n    if (!authResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: authResult.error,\n            type: 'authentication_error',\n            code: 'invalid_api_key'\n          }\n        },\n        { status: authResult.statusCode || 401 }\n      );\n    }\n\n    const { userApiKey, userConfig, ipAddress } = authResult;\n    const { configId } = await params;\n\n    // 2. Check if config exists and belongs to user\n    const supabase = createSupabaseServerClientFromRequest(request);\n    \n    const { data: config, error: fetchError } = await supabase\n      .from('custom_api_configs')\n      .select('id, name')\n      .eq('id', configId)\n      .eq('user_id', userConfig!.user_id)\n      .single();\n\n    if (fetchError || !config) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Configuration not found',\n            type: 'not_found_error',\n            code: 'config_not_found'\n          }\n        },\n        { status: 404 }\n      );\n    }\n\n    // 3. Delete configuration (this will cascade delete related API keys)\n    const { error: deleteError } = await supabase\n      .from('custom_api_configs')\n      .delete()\n      .eq('id', configId)\n      .eq('user_id', userConfig!.user_id);\n\n    if (deleteError) {\n      console.error('Error deleting config:', deleteError);\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Failed to delete configuration',\n            type: 'server_error',\n            code: 'database_error'\n          }\n        },\n        { status: 500 }\n      );\n    }\n\n    // 4. Log API usage\n    authMiddleware.logApiUsage(\n      userApiKey!,\n      request,\n      {\n        statusCode: 200,\n        modelUsed: 'config_management',\n        providerUsed: 'rokey_api',\n      },\n      ipAddress\n    ).catch(console.error);\n\n    return NextResponse.json({\n      id: configId,\n      object: 'config',\n      deleted: true\n    }, {\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n      }\n    });\n\n  } catch (error) {\n    console.error('Error in config DELETE API:', error);\n    return NextResponse.json(\n      {\n        error: {\n          message: 'Internal server error',\n          type: 'server_error',\n          code: 'internal_error'\n        }\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// OPTIONS handler for CORS\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n    },\n  });\n}\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\configs\\\\[configId]\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/external/v1/configs/[configId]/route\",\n        pathname: \"/api/external/v1/configs/[configId]\",\n        filename: \"route\",\n        bundlePath: \"app/api/external/v1/configs/[configId]/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\configs\\\\[configId]\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fexternal%2Fv1%2Fconfigs%2F%5BconfigId%5D%2Froute&page=%2Fapi%2Fexternal%2Fv1%2Fconfigs%2F%5BconfigId%5D%2Froute&pagePath=private-next-app-dir%2Fapi%2Fexternal%2Fv1%2Fconfigs%2F%5BconfigId%5D%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&appPaths=%2Fapi%2Fexternal%2Fv1%2Fconfigs%2F%5BconfigId%5D%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/external/v1/configs/[configId]/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":true},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.mjs\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\",\"image/avif\"],\"dangerouslyAllowSVG\":true,\"contentSecurityPolicy\":\"default-src 'self'; script-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"raw.githubusercontent.com\",\"port\":\"\",\"pathname\":\"/lobehub/lobe-icons/**\"},{\"protocol\":\"https\",\"hostname\":\"registry.npmmirror.com\",\"port\":\"\",\"pathname\":\"/@lobehub/icons-static-png/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@latest/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@v11/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"images.unsplash.com\",\"port\":\"\",\"pathname\":\"/**\"},{\"protocol\":\"https\",\"hostname\":\"cloud.gmelius.com\",\"port\":\"\",\"pathname\":\"/public/logos/**\"},{\"protocol\":\"https\",\"hostname\":\"upload.wikimedia.org\",\"port\":\"\",\"pathname\":\"/wikipedia/commons/**\"},{\"protocol\":\"https\",\"hostname\":\"kstatic.googleusercontent.com\",\"port\":\"\",\"pathname\":\"/files/**\"}],\"unoptimized\":false},\"devIndicators\":{\"position\":\"bottom-left\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"C:\\\\RoKey App\\\\rokey-app\",\"experimental\":{\"nodeMiddleware\":false,\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"dynamicOnHover\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":3,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":true,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":true,\"largePageDataBytes\":128000,\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"viewTransition\":false,\"routerBFCache\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"useCache\":false,\"optimizePackageImports\":[\"@heroicons/react\",\"@headlessui/react\",\"react-markdown\",\"react-syntax-highlighter\",\"@supabase/supabase-js\",\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"htmlLimitedBots\":\"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti\",\"bundlePagesRouterDependencies\":false,\"configFile\":\"C:\\\\RoKey App\\\\rokey-app\\\\next.config.mjs\",\"configFileName\":\"next.config.mjs\",\"serverExternalPackages\":[\"pdf-parse\",\"mammoth\"],\"turbopack\":{\"rules\":{\"*.svg\":{\"loaders\":[\"@svgr/webpack\"],\"as\":\"*.js\"}},\"root\":\"C:\\\\RoKey App\\\\rokey-app\"},\"compiler\":{\"removeConsole\":true,\"reactRemoveProperties\":true},\"api\":{\"bodyParser\":{\"sizeLimit\":\"50mb\"},\"responseLimit\":\"50mb\"},\"_originalRedirects\":[]}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/external/v1/configs/[configId]/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/external/v1/configs/[configId]/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "module.exports = require(\"node:buffer\");", "module.exports = require(\"node:async_hooks\");", "import { createServerClient, type CookieOptions } from '@supabase/ssr';\r\nimport { createClient } from '@supabase/supabase-js';\r\nimport { cookies } from 'next/headers';\r\nimport { NextRequest } from 'next/server';\r\n\r\n// This is the standard setup for creating a Supabase server client\r\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\r\n// Updated for Next.js 15 async cookies requirement\r\nexport async function createSupabaseServerClientOnRequest() {\r\n  const cookieStore = await cookies();\r\n\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        get(name: string) {\r\n          return cookieStore.get(name)?.value;\r\n        },\r\n        set(name: string, value: string, options: CookieOptions) {\r\n          try {\r\n            cookieStore.set({ name, value, ...options });\r\n          } catch (error) {\r\n            // This error can be ignored if running in a Server Component\r\n            // where cookies can't be set directly. Cookie setting should be\r\n            // handled in Server Actions or Route Handlers.\r\n            console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\r\n          }\r\n        },\r\n        remove(name: string, options: CookieOptions) {\r\n          try {\r\n            // To remove a cookie using the `set` method from `next/headers`,\r\n            // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\r\n            cookieStore.set({ name, value: '', ...options });\r\n          } catch (error) {\r\n            // Similar to set, this might fail in a Server Component.\r\n            console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\r\n          }\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n\r\n// Alternative method for API routes that need to handle cookies from request\r\nexport function createSupabaseServerClientFromRequest(request: NextRequest) {\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        get(name: string) {\r\n          return request.cookies.get(name)?.value;\r\n        },\r\n        set(name: string, value: string, options: CookieOptions) {\r\n          // In API routes, we can't set cookies directly on the request\r\n          // This will be handled by the response\r\n        },\r\n        remove(name: string, options: CookieOptions) {\r\n          // In API routes, we can't remove cookies directly on the request\r\n          // This will be handled by the response\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n\r\n// Service role client for admin operations (OAuth token storage, etc.)\r\nexport function createServiceRoleClient() {\r\n  return createClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.SUPABASE_SERVICE_ROLE_KEY!,\r\n    {\r\n      auth: {\r\n        autoRefreshToken: false,\r\n        persistSession: false\r\n      }\r\n    }\r\n  );\r\n}\r\n"], "names": ["runtime", "authMiddleware", "ApiKeyAuthMiddleware", "UpdateConfigSchema", "z", "name", "min", "max", "optional", "description", "routing_strategy", "settings", "temperature", "max_tokens", "int", "positive", "top_p", "frequency_penalty", "presence_penalty", "GET", "request", "params", "authResult", "authenticateRequest", "success", "NextResponse", "json", "error", "message", "type", "code", "status", "statusCode", "userApiKey", "userConfig", "ip<PERSON><PERSON><PERSON>", "configId", "supabase", "createSupabaseServerClientFromRequest", "data", "config", "from", "select", "eq", "user_id", "single", "logApiUsage", "modelUsed", "providerUsed", "catch", "console", "id", "object", "created_at", "updated_at", "api_keys", "headers", "PUT", "body", "validationResult", "safeParse", "details", "errors", "updateData", "updatedConfig", "update", "Date", "toISOString", "DELETE", "fetchError", "deleteError", "delete", "deleted", "OPTIONS", "createServerClient", "process", "cookies", "get", "value", "set", "options", "remove"], "sourceRoot": "", "ignoreList": []}