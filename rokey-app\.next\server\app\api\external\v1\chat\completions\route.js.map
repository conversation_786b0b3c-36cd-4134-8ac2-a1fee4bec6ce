{"version": 3, "file": "app/api/external/v1/chat/completions/route.js", "mappings": "+IEAA,2RFKO,IAAMA,EAAU,OAMjBC,EAAgCC,EAAAA,CAAAA,CAAAA,MAAQ,CAAC,CAC7CC,MAAOD,EAAAA,CAAAA,CAAAA,MAAQ,GAAGE,QAAQ,GAAGC,OAAO,CAAC,iBACrCC,SAAUJ,EAAAA,CAAAA,CAAAA,KAAO,CACfA,EAAAA,CAAAA,CAAAA,MAAQ,CAAC,CACPK,KAAML,EAAAA,CAAAA,CAAAA,IAAM,CAAC,CAAC,OAAQ,YAAa,SAAS,EAC5CM,QAASN,EAAAA,CAAAA,CAAAA,KAAO,CAAC,CACfA,EAAAA,CAAAA,CAAAA,MAAQ,GACRA,EAAAA,CAAAA,CAAAA,KAAO,CAACA,EAAAA,CAAAA,CAAAA,GAAK,IAAI,CAErB,IACAO,GAAG,CAAC,EAAG,CAAEC,QAAS,SAHgC,wBAGE,GACtDC,OAAQT,EAAAA,CAAAA,CAAAA,OAAS,GAAGE,QAAQ,GAAGC,OAAO,EAAC,GACvCO,YAAaV,EAAAA,CAAAA,CAAAA,MAAQ,GAAGO,GAAG,CAAC,GAAGI,GAAG,CAAC,GAAGT,QAAQ,GAC9CU,WAAYZ,EAAAA,CAAAA,CAAAA,MAAQ,GAAGa,GAAG,GAAGC,QAAQ,GAAGZ,QAAQ,GAChDa,MAAOf,EAAAA,CAAAA,CAAAA,MAAQ,GAAGO,GAAG,CAAC,GAAGI,GAAG,CAAC,GAAGT,QAAQ,GACxCc,kBAAmBhB,EAAAA,CAAAA,CAAAA,MAAQ,GAAGO,GAAG,CAAC,CAAC,GAAGI,GAAG,CAAC,GAAGT,QAAQ,GACrDe,iBAAkBjB,EAAAA,CAAAA,CAAAA,MAAQ,GAAGO,GAAG,CAAC,CAAC,GAAGI,GAAG,CAAC,GAAGT,QAAQ,GACpDgB,KAAMlB,EAAAA,CAAAA,CAAAA,KAAO,CAAC,CAACA,EAAAA,CAAAA,CAAAA,MAAQ,GAAIA,EAAAA,CAAAA,CAAAA,KAAO,CAACA,EAAAA,CAAAA,CAAAA,MAAQ,IAAI,EAAEE,QAAQ,GACzDiB,EAAGnB,EAAAA,CAAAA,CAAAA,MAAQ,GAAGa,GAAG,GAAGC,QAAQ,GAAGZ,QAAQ,GAAGC,OAAO,CAAC,GAElDE,KAAML,EAAAA,CAAAA,CAAAA,MAAQ,GAAGE,QAAQ,EAC3B,GAAGkB,QAAQ,CAACpB,EAAAA,CAAAA,CAAAA,GAAK,IAEXqB,CAFgB,CAEC,IAAIC,EAAAA,CAAoBA,CAIxC,UAJaD,KAIEE,EAAKC,CAAoB,EAC7C,GAAI,CAEF,IATuD,EASjDC,EAAa,MAAMJ,EAAeK,kBAADL,CAAoB,CAACG,GAE5D,GAAI,CAACC,EAAWE,OAAO,CAErB,CAFuB,MAEhBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLtB,QAASiB,EAAWK,KAAK,CACzBC,KAAM,uBACNC,KAAM,iBACR,CACF,EACA,CAAEC,OAAQR,EAAWS,UAAU,EAAI,GAAI,GAI3C,GAAM,YAAEC,CAAU,YAAEC,CAAU,WAAEC,CAAS,CAAE,CAAGZ,EAG9C,GAAI,CAACJ,EAAeiB,aAAa,CAACH,EAAa,EAA5Bd,MACjB,CADsD,MAC/CO,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLtB,QAAS,wCACTuB,KAAM,mBACNC,KAAM,0BACR,CACF,EACA,CAAEC,OAAQ,GAAI,GAKlB,IAAMM,EAAU,MAAMf,EAAQK,IAAI,GAC5BW,EAAmBzC,EAA8B0C,SAAS,CAACF,GAEjE,GAAI,CAACC,EAAiBb,OAAO,CAC3B,CAD6B,MACtBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLtB,QAAS,uBACTuB,KAAM,wBACNC,KAAM,kBACNU,QAASF,EAAiBV,KAAK,CAACa,OAAO,GAAGC,WAAW,CAEzD,EACA,CAAEX,OAAQ,GAAI,GAIlB,IAAMY,EAAcL,EAAiBM,IAAI,CAGzC,GAAID,EAAYpC,MAAM,EAAI,CAACY,EAAeiB,aAAa,CAACH,EAAa,EAA5Bd,WACvC,CADiF,MAC1EO,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLtB,QAAS,6CACTuB,KAAM,mBACNC,KAAM,uBACR,CACF,EACA,CAAEC,OAAQ,GAAI,GAMlB,IAAIc,EAAkB,GAEtB,GAAIF,EAAYxC,IAAI,EAAIwC,EAAYzC,QAAQ,EAAE4C,KAAK,GACjDC,EAAI3C,OAAO,EAA2B,UAAvB,OAAO2C,EAAI3C,OAAO,EAAiB2C,EAAI3C,OAAO,CAAC4C,MAAM,CAAG,KAEvE,CADC,EACG,CAEF,IAAMC,EAAyB,MAAMC,MAAM,GAAGC,uBAAgC,IAAI,CAAuB,CAAC,yBAAoC,CAC5IC,OAAQ,CADkI,MAE1IC,QAAS,CACP,eAAgB,mBAChB,cAAiB,CAAC,OAAO,EAAEF,QAAQG,GAAG,CAACC,sBAAsB,EAAE,EAEjEC,KAAMC,KAAKC,SAAS,CAAC,CACnBxD,SAAUyC,EAAYzC,QAAQ,CAC9BC,KAAMwC,EAAYxC,IAAI,CACtBwD,UAAWzB,EAAY0B,EAAE,EAE7B,GAEIX,EAAuBY,EAAE,EAAE,CAE7BhB,EAAkBiB,CADW,MAAMb,EAAuBtB,IAAI,IACvBoC,WAAAA,CAM3C,CAAE,MAAOnC,EAAO,CAEhB,CAIF,IAAIoC,EAA2B,KAM3BnB,IACFmB,EAAsB,CACpBnC,KAAM,KAFW,iBAGjBoC,OAAQ,4FACRC,iBAAkB,GAAGf,uBAAgC,IAAI,CAAuB,CAAC,qBACjFgB,QAD8G,CAAC,cACvF,EACxBC,kBAAkB,EAClBC,iBAAkB,sEAClBC,SAAU,CACR,kDACA,mEACA,4CACA,uCACD,GAOL,IAAMC,EAAkB,CACtBC,qBAAsBtC,EAAY0B,EAAE,CACpC1D,SAAUyC,EAAYzC,QAAQ,CAC9BK,OAAQoC,EAAYpC,MAAM,CAC1BC,YAAamC,EAAYnC,WAAW,CACpCE,WAAYiC,EAAYjC,UAAU,CAClCP,KAAMwC,EAAYxC,IAAI,CACtBJ,MAAO4C,EAAY5C,KAAK,CAExBc,MAAO8B,EAAY9B,KAAK,CACxBC,kBAAmB6B,EAAY7B,iBAAiB,CAChDC,iBAAkB4B,EAAY5B,gBAAgB,CAC9CC,KAAM2B,EAAY3B,IAAI,CACtBC,EAAG0B,EAAY1B,CAAC,CAEhBwD,kBAAmBvC,EAAYwC,OAAO,EAIlCC,EAAiB,IAAIC,IAAI,2BAA4BtD,EAAQuD,GAAG,EAGhEC,EAAa,IAAIC,gBACjBC,EAAYC,WAAW,IAAMH,EAAWI,KAAK,GAAI,MAGvD,EAH+D,CAG3D,CACFC,EAAmB,MAAMjC,MAAMyB,EAAeS,QAAQ,GAAI,CACxDhC,OAAQ,OACRC,QAAS,CACP,eAAgB,mBAChB,cAAiB,CAAC,OAAO,EAAEF,QAAQG,GAAG,CAACC,sBAAsB,EAAE,CAC/D,kBAAmBpB,GAAa,GAChC,oBAAqBF,EAAY2B,EAAE,CACnC,qBAAsB,MACxB,EACAJ,KAAMC,KAAKC,SAAS,CAACa,GACrBc,OAAQP,EAAWO,MAAM,EAE7B,CAAE,MAAOzD,EAAY,CAEnB,GADA0D,aAAaN,GACM,cAAc,CAA7BpD,EAAM2D,IAAI,CAEZ,OAAO7D,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLtB,QAAS,+GACTuB,KAAM,gBACNC,KAAM,kBACN0D,WAAY,oEACd,CACF,EACA,CAAEzD,OAAQ,GAAI,EAGlB,OAAMH,CACR,CACA0D,KAFe,QAEFN,GAGb,IAAIS,EAAsB,EAAE,CACxBC,EAAkB/C,EANiB,KAMA,CAGvC,GAAI,CAACA,EAAYpC,MAAM,EAAI4E,EAAiBtB,EAAE,CAC5C,CAD8C,EAC1C,CACF,IAAM8B,EAAgBR,EAAiBS,KAAK,GACtCC,EAAe,MAAMF,EAAchE,IAAI,GAGzCkE,EAAaC,cAAc,EAAEC,YAAY,CAC3CN,EAAYI,EAAaC,cAAc,CAACC,UAAAA,EAEtCF,EAAa9F,KAAK,EAAE,CACtB2F,EAAkBG,EAAa9F,KAAAA,CAEnC,CAAE,MAAO6B,EAAO,CAEhB,CAkBF,GAdAT,EAAe6E,WAAW,CACxB/D,EACAX,EACA,CACEU,CAJUb,UAIEgE,EAAiBpD,MAAM,CACnCkE,UAAWP,EACXQ,aAAcT,EAAUzC,MAAM,CAAG,EAAIyC,EAAUU,IAAI,CAAC,MAAQC,MAC9D,EACAjE,GACAkE,KAAK,CAAC,IAER,GAGI1D,EAAYpC,MAAM,CAAE,CAEtB,IAAM+F,EAAwC,CAC5C,eAAgB,oBAChB,gBAAiB,WACjB,WAAc,aACd,8BAA+B,IAC/B,+BAAgC,gBAChC,+BAAgC,yCAChC,kBAAmBpE,EAAYqD,IAAI,EAarC,OATIvB,IACFsC,CAAa,CAAC,eADS,iBACuB,CAAGtC,EAAoBnC,IAAI,CACzEyE,CAAa,CAAC,+BAA+B,CAAG,QAO3C,IAAIC,SAASpB,EAAiB3B,IAAI,CAAE,CACzCzB,OAAQoD,EAAiBpD,MAAM,CAC/BsB,QAASiD,CACX,EACF,CAAO,CAEL,IAAMT,EAAe,MAAMV,EAAiBxD,IAAI,GAGhD,IAAIwD,EAAiBtB,EAAE,CAsCrB,OAAOnC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLtB,QAASuF,EAAajE,KAAK,EAAI,wBAC/BC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQoD,EAAiBpD,MAAM,EA9CZ,EAEvB,IAAMyE,EAAmB,CACvB,GAAGX,CAAY,CAEfC,eAAgB,CACdC,WAAYF,EAAaC,cAAc,EAAEC,YAAc,EAAE,CACzDU,iBAAkBvE,EAAYuE,gBAAgB,CAC9CC,YAAaxE,EAAYqD,IAAI,CAC7BoB,aAAc1E,EAAY2E,QAAQ,CAElC,GAAI5C,GAAuB,CAAE6C,qBAAsB7C,CAAoB,CAAC,CAExE,GAAI6B,EAAaC,cAAc,EAAI,CAAC,CACtC,CACF,EAGMgB,EAA0C,CAC9C,8BAA+B,IAC/B,+BAAgC,gBAChC,+BAAgC,yCAChC,sBAAuBrB,EAAUU,IAAI,CAAC,OAAS,OAC/C,kBAAmBjE,EAAYqD,IAAI,EASrC,OALIvB,IACF8C,CAAe,CAAC,eADO,iBACyB,CAAG9C,EAAoBnC,IAAI,CAC3EiF,CAAe,CAAC,gCAAgC,CAAG,QAG9CpF,EAAAA,EAAYA,CAACC,IAAI,CAAC6E,EAAkB,CACzCzE,OAAQoD,EAAiBpD,MAAM,CAC/BsB,QAASyD,CACX,EACF,CAaF,CAEF,CAAE,IAfS,EAeFlF,EAAO,CAEd,OAAOF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLtB,QAAS,wBACTuB,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,EAElB,CACF,CAGO,eAAegF,IACpB,OAAO,IAAIR,SAAS,KAAM,CACxBxE,OAAQ,IACRsB,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,gBAChC,+BAAgC,yCAChC,yBAA0B,OAC5B,CACF,EACF,CC9WA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,+CACA,6CACA,iBACA,uDACA,CAAK,CACL,sGACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,wFACA,EAFA,4BAEA,2BACA,OACI,QAA8B,EAClC,+CACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA0B,aAAe,kDAAyD,wOAAuQ,ySAAoU,mBAAmB,QAAQ,uDAA2D,gGAAwG,EAAE,oGAA4G,EAAE,kGAA0G,EAAE,+FAAuG,EAAE,uEAA+E,EAAE,kFAA0F,EAAE,0FAAkG,EAAE,uFAA+F,iBAAsB,gBAAkB,uBAAyB,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,gEAAoE,6BAAoC,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,o/BAAmsC,qBAAyB,ykDAAkmD,idAAge,OAAS,SAAS,qCAAyC,iCAAmC,WAAa,0CAAkD,MAAQ,YAAc,iBAAmB,sBAAwB,uBAiBruM,CAAC,CAAC,EAAC,sBCvBH,uDCAA", "sources": ["webpack://_N_E/./src/app/api/external/v1/chat/completions/route.ts", "webpack://_N_E/./src/app/api/external/v1/chat/completions/route.ts?812a", "webpack://_N_E/?eb19", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/external commonjs \"node:async_hooks\""], "sourcesContent": ["import { type NextRequest, NextResponse } from 'next/server';\nimport { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';\nimport { z } from 'zod';\n\n// Use Edge Runtime for no timeout limits and better global performance\nexport const runtime = 'edge';\n\n// OpenAI-compatible external API endpoint for user-generated API keys\n// This endpoint accepts requests from external applications using user-generated API keys\n// Compatible with OpenAI SDK and other OpenAI-compatible clients\n\nconst OpenAICompatibleRequestSchema = z.object({\n  model: z.string().optional().default('gpt-3.5-turbo'), // OpenAI compatibility\n  messages: z.array(\n    z.object({\n      role: z.enum(['user', 'assistant', 'system']),\n      content: z.union([\n        z.string(),\n        z.array(z.any()) // Support multimodal content\n      ]),\n    })\n  ).min(1, { message: \"Messages array cannot be empty.\" }),\n  stream: z.boolean().optional().default(false),\n  temperature: z.number().min(0).max(2).optional(),\n  max_tokens: z.number().int().positive().optional(),\n  top_p: z.number().min(0).max(1).optional(),\n  frequency_penalty: z.number().min(-2).max(2).optional(),\n  presence_penalty: z.number().min(-2).max(2).optional(),\n  stop: z.union([z.string(), z.array(z.string())]).optional(),\n  n: z.number().int().positive().optional().default(1),\n  // RouKey-specific extensions\n  role: z.string().optional(), // For role-based routing\n}).catchall(z.any()); // Allow additional OpenAI parameters\n\nconst authMiddleware = new ApiKeyAuthMiddleware();\n\n// Only using Gemini classifier for multi-role detection - no keyword-based complexity detection\n\nexport async function POST(request: NextRequest) {\n  try {\n    // 1. Authenticate using user-generated API key\n    const authResult = await authMiddleware.authenticateRequest(request);\n    \n    if (!authResult.success) {\n      // Return OpenAI-compatible error format\n      return NextResponse.json(\n        {\n          error: {\n            message: authResult.error,\n            type: 'authentication_error',\n            code: 'invalid_api_key'\n          }\n        },\n        { status: authResult.statusCode || 401 }\n      );\n    }\n\n    const { userApiKey, userConfig, ipAddress } = authResult;\n\n    // 2. Check permissions\n    if (!authMiddleware.hasPermission(userApiKey!, 'chat')) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'API key does not have chat permission',\n            type: 'permission_error',\n            code: 'insufficient_permissions'\n          }\n        },\n        { status: 403 }\n      );\n    }\n\n    // 3. Parse and validate request body\n    const rawBody = await request.json();\n    const validationResult = OpenAICompatibleRequestSchema.safeParse(rawBody);\n\n    if (!validationResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Invalid request body',\n            type: 'invalid_request_error',\n            code: 'invalid_request',\n            details: validationResult.error.flatten().fieldErrors\n          }\n        },\n        { status: 400 }\n      );\n    }\n\n    const requestData = validationResult.data;\n\n    // 4. Check streaming permission if requested\n    if (requestData.stream && !authMiddleware.hasPermission(userApiKey!, 'streaming')) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'API key does not have streaming permission',\n            type: 'permission_error',\n            code: 'streaming_not_allowed'\n          }\n        },\n        { status: 403 }\n      );\n    }\n\n    // 5. Check if request should use async processing\n    // First, check if this is a multi-role task by calling internal classification\n    let isMultiRoleTask = false;\n\n    if (requestData.role || requestData.messages?.some((msg: any) =>\n      msg.content && typeof msg.content === 'string' && msg.content.length > 100\n    )) {\n      try {\n        // Quick classification check to detect multi-role tasks\n        const classificationResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/internal/classify-multi-role`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${process.env.ROKEY_API_ACCESS_TOKEN}`,\n          },\n          body: JSON.stringify({\n            messages: requestData.messages,\n            role: requestData.role,\n            config_id: userConfig!.id\n          }),\n        });\n\n        if (classificationResponse.ok) {\n          const classificationResult = await classificationResponse.json();\n          isMultiRoleTask = classificationResult.isMultiRole;\n\n          if (isMultiRoleTask) {\n            console.log(`[External API] Multi-role task detected by Gemini classifier - auto-routing to async processing`);\n          }\n        }\n      } catch (error) {\n        console.warn('[External API] Classification check failed, falling back to keyword detection');\n      }\n    }\n\n    // Store async processing recommendations for response headers\n    let asyncRecommendation: any = null;\n\n    // Multi-role tasks can now work with both streaming and non-streaming\n    let streamForced = false;\n    // Note: Removed forced streaming for multi-role tasks to support non-streaming mode\n\n    if (isMultiRoleTask) {\n      asyncRecommendation = {\n        type: 'multi_role_detected',\n        reason: 'Gemini classifier detected this task requires multiple specialized roles working together',\n        async_submit_url: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/external/v1/async/submit`,\n        estimated_time_minutes: 5,\n        streaming_forced: false, // Changed to false to support non-streaming\n        streaming_reason: 'Multi-role tasks now support both streaming and non-streaming modes',\n        benefits: [\n          'Supports both streaming and non-streaming modes',\n          'Real-time progress tracking with role detection (when streaming)',\n          'Better responsiveness and user experience',\n          'Proper handling of role coordination'\n        ]\n      };\n    }\n\n    // Only use Gemini classifier for multi-role detection - no keyword-based fallback\n\n    // Prepare request for internal RouKey API\n    const internalRequest = {\n      custom_api_config_id: userConfig!.id,\n      messages: requestData.messages,\n      stream: requestData.stream,\n      temperature: requestData.temperature,\n      max_tokens: requestData.max_tokens,\n      role: requestData.role, // RouKey-specific role routing\n      model: requestData.model,\n      // Pass through other OpenAI parameters\n      top_p: requestData.top_p,\n      frequency_penalty: requestData.frequency_penalty,\n      presence_penalty: requestData.presence_penalty,\n      stop: requestData.stop,\n      n: requestData.n,\n      // Pass user context for RLS\n      _internal_user_id: userConfig!.user_id,\n    };\n\n    // 6. Call internal RouKey API with timeout\n    const internalApiUrl = new URL('/api/v1/chat/completions', request.url);\n\n    // Create AbortController for timeout\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 55000); // 55s timeout (5s buffer for Vercel)\n\n    let internalResponse: Response;\n    try {\n      internalResponse = await fetch(internalApiUrl.toString(), {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${process.env.ROKEY_API_ACCESS_TOKEN}`,\n          'X-Forwarded-For': ipAddress || '',\n          'X-User-API-Key-ID': userApiKey!.id,\n          'X-External-Request': 'true',\n        },\n        body: JSON.stringify(internalRequest),\n        signal: controller.signal,\n      });\n    } catch (error: any) {\n      clearTimeout(timeoutId);\n      if (error.name === 'AbortError') {\n        // Handle timeout - suggest async processing\n        return NextResponse.json(\n          {\n            error: {\n              message: 'Request timeout. For complex multi-role tasks, consider using async processing or breaking down the request.',\n              type: 'timeout_error',\n              code: 'request_timeout',\n              suggestion: 'Try reducing complexity or use streaming for better responsiveness'\n            }\n          },\n          { status: 408 }\n        );\n      }\n      throw error; // Re-throw other errors\n    }\n    clearTimeout(timeoutId);\n\n    // 7. Extract role information for logging and response enhancement\n    let rolesUsed: string[] = [];\n    let actualModelUsed = requestData.model;\n\n    // For non-streaming, we can peek at the response to extract role info\n    if (!requestData.stream && internalResponse.ok) {\n      try {\n        const responseClone = internalResponse.clone();\n        const responseData = await responseClone.json();\n\n        // Extract roles from response metadata\n        if (responseData.rokey_metadata?.roles_used) {\n          rolesUsed = responseData.rokey_metadata.roles_used;\n        }\n        if (responseData.model) {\n          actualModelUsed = responseData.model;\n        }\n      } catch (error) {\n        console.warn('Could not extract role metadata:', error);\n      }\n    }\n\n    // Log usage asynchronously\n    authMiddleware.logApiUsage(\n      userApiKey!,\n      request,\n      {\n        statusCode: internalResponse.status,\n        modelUsed: actualModelUsed,\n        providerUsed: rolesUsed.length > 0 ? rolesUsed.join(', ') : undefined,\n      },\n      ipAddress\n    ).catch((error: any) => {\n      console.error('Failed to log API usage:', error);\n    });\n\n    // 8. Handle response based on streaming\n    if (requestData.stream) {\n      // For streaming responses, pass through the stream with enhanced headers\n      const streamHeaders: Record<string, string> = {\n        'Content-Type': 'text/event-stream',\n        'Cache-Control': 'no-cache',\n        'Connection': 'keep-alive',\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'POST, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n        'X-RouKey-Config': userConfig!.name,\n      };\n\n      // Add async recommendation headers for streaming if present\n      if (asyncRecommendation) {\n        streamHeaders['X-RouKey-Async-Recommendation'] = asyncRecommendation.type;\n        streamHeaders['X-RouKey-Multi-Role-Detected'] = 'true';\n        if (streamForced) {\n          streamHeaders['X-RouKey-Stream-Forced'] = 'true';\n          streamHeaders['X-RouKey-Stream-Reason'] = 'Multi-role task requires streaming to prevent timeouts';\n        }\n      }\n\n      return new Response(internalResponse.body, {\n        status: internalResponse.status,\n        headers: streamHeaders,\n      });\n    } else {\n      // For non-streaming responses, return JSON\n      const responseData = await internalResponse.json();\n\n      // Ensure OpenAI-compatible response format\n      if (internalResponse.ok) {\n        // Enhance response with comprehensive role information and async recommendations\n        const enhancedResponse = {\n          ...responseData,\n          // Add RouKey-specific metadata while maintaining OpenAI compatibility\n          rokey_metadata: {\n            roles_used: responseData.rokey_metadata?.roles_used || [],\n            routing_strategy: userConfig!.routing_strategy,\n            config_name: userConfig!.name,\n            api_key_name: userApiKey!.key_name,\n            // Include async recommendation if present\n            ...(asyncRecommendation && { async_recommendation: asyncRecommendation }),\n            // Include all other metadata from internal response\n            ...(responseData.rokey_metadata || {})\n          }\n        };\n\n        // Prepare response headers with comprehensive information\n        const responseHeaders: Record<string, string> = {\n          'Access-Control-Allow-Origin': '*',\n          'Access-Control-Allow-Methods': 'POST, OPTIONS',\n          'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n          'X-RouKey-Roles-Used': rolesUsed.join(', ') || 'none',\n          'X-RouKey-Config': userConfig!.name,\n        };\n\n        // Add async recommendation headers if present\n        if (asyncRecommendation) {\n          responseHeaders['X-RouKey-Async-Recommendation'] = asyncRecommendation.type;\n          responseHeaders['X-RouKey-Streaming-Suggestion'] = 'true';\n        }\n\n        return NextResponse.json(enhancedResponse, {\n          status: internalResponse.status,\n          headers: responseHeaders\n        });\n      } else {\n        // Convert internal errors to OpenAI format\n        return NextResponse.json(\n          {\n            error: {\n              message: responseData.error || 'Internal server error',\n              type: 'server_error',\n              code: 'internal_error'\n            }\n          },\n          { status: internalResponse.status }\n        );\n      }\n    }\n\n  } catch (error) {\n    console.error('Error in external chat completions API:', error);\n    return NextResponse.json(\n      {\n        error: {\n          message: 'Internal server error',\n          type: 'server_error',\n          code: 'internal_error'\n        }\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// Handle CORS preflight requests\nexport async function OPTIONS() {\n  return new Response(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'POST, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n      'Access-Control-Max-Age': '86400',\n    },\n  });\n}\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\chat\\\\completions\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/external/v1/chat/completions/route\",\n        pathname: \"/api/external/v1/chat/completions\",\n        filename: \"route\",\n        bundlePath: \"app/api/external/v1/chat/completions/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\chat\\\\completions\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fexternal%2Fv1%2Fchat%2Fcompletions%2Froute&page=%2Fapi%2Fexternal%2Fv1%2Fchat%2Fcompletions%2Froute&pagePath=private-next-app-dir%2Fapi%2Fexternal%2Fv1%2Fchat%2Fcompletions%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&appPaths=%2Fapi%2Fexternal%2Fv1%2Fchat%2Fcompletions%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/external/v1/chat/completions/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":true},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.mjs\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\",\"image/avif\"],\"dangerouslyAllowSVG\":true,\"contentSecurityPolicy\":\"default-src 'self'; script-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"raw.githubusercontent.com\",\"port\":\"\",\"pathname\":\"/lobehub/lobe-icons/**\"},{\"protocol\":\"https\",\"hostname\":\"registry.npmmirror.com\",\"port\":\"\",\"pathname\":\"/@lobehub/icons-static-png/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@latest/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@v11/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"images.unsplash.com\",\"port\":\"\",\"pathname\":\"/**\"},{\"protocol\":\"https\",\"hostname\":\"cloud.gmelius.com\",\"port\":\"\",\"pathname\":\"/public/logos/**\"},{\"protocol\":\"https\",\"hostname\":\"upload.wikimedia.org\",\"port\":\"\",\"pathname\":\"/wikipedia/commons/**\"},{\"protocol\":\"https\",\"hostname\":\"kstatic.googleusercontent.com\",\"port\":\"\",\"pathname\":\"/files/**\"}],\"unoptimized\":false},\"devIndicators\":{\"position\":\"bottom-left\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"C:\\\\RoKey App\\\\rokey-app\",\"experimental\":{\"nodeMiddleware\":false,\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"dynamicOnHover\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":3,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":true,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":true,\"largePageDataBytes\":128000,\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"viewTransition\":false,\"routerBFCache\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"useCache\":false,\"optimizePackageImports\":[\"@heroicons/react\",\"@headlessui/react\",\"react-markdown\",\"react-syntax-highlighter\",\"@supabase/supabase-js\",\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"htmlLimitedBots\":\"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti\",\"bundlePagesRouterDependencies\":false,\"configFile\":\"C:\\\\RoKey App\\\\rokey-app\\\\next.config.mjs\",\"configFileName\":\"next.config.mjs\",\"serverExternalPackages\":[\"pdf-parse\",\"mammoth\"],\"turbopack\":{\"rules\":{\"*.svg\":{\"loaders\":[\"@svgr/webpack\"],\"as\":\"*.js\"}},\"root\":\"C:\\\\RoKey App\\\\rokey-app\"},\"compiler\":{\"removeConsole\":true,\"reactRemoveProperties\":true},\"api\":{\"bodyParser\":{\"sizeLimit\":\"50mb\"},\"responseLimit\":\"50mb\"},\"_originalRedirects\":[]}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/external/v1/chat/completions/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/external/v1/chat/completions/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "module.exports = require(\"node:buffer\");", "module.exports = require(\"node:async_hooks\");"], "names": ["runtime", "OpenAICompatibleRequestSchema", "z", "model", "optional", "default", "messages", "role", "content", "min", "message", "stream", "temperature", "max", "max_tokens", "int", "positive", "top_p", "frequency_penalty", "presence_penalty", "stop", "n", "catchall", "authMiddleware", "ApiKeyAuthMiddleware", "POST", "request", "authResult", "authenticateRequest", "success", "NextResponse", "json", "error", "type", "code", "status", "statusCode", "userApiKey", "userConfig", "ip<PERSON><PERSON><PERSON>", "hasPermission", "rawBody", "validationResult", "safeParse", "details", "flatten", "fieldErrors", "requestData", "data", "isMultiRoleTask", "some", "msg", "length", "classificationResponse", "fetch", "process", "method", "headers", "env", "ROKEY_API_ACCESS_TOKEN", "body", "JSON", "stringify", "config_id", "id", "ok", "classificationResult", "isMultiRole", "asyncRecommendation", "reason", "async_submit_url", "estimated_time_minutes", "streaming_forced", "streaming_reason", "benefits", "internalRequest", "custom_api_config_id", "_internal_user_id", "user_id", "internalApiUrl", "URL", "url", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "internalResponse", "toString", "signal", "clearTimeout", "name", "suggestion", "rolesUsed", "actualModelUsed", "responseClone", "clone", "responseData", "rokey_metadata", "roles_used", "logApiUsage", "modelUsed", "providerUsed", "join", "undefined", "catch", "streamHeaders", "Response", "enhancedResponse", "routing_strategy", "config_name", "api_key_name", "key_name", "async_recommendation", "responseHeaders", "OPTIONS"], "sourceRoot": "", "ignoreList": []}