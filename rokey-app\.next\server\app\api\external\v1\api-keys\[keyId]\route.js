(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[316],{5356:e=>{"use strict";e.exports=require("node:buffer")},5521:e=>{"use strict";e.exports=require("node:async_hooks")},8645:(e,t,a)=>{"use strict";a.r(t),a.d(t,{ComponentMod:()=>P,default:()=>E});var r,s={};a.r(s),a.d(s,{DELETE:()=>x,GET:()=>k,OPTIONS:()=>w,PUT:()=>v,runtime:()=>g});var o={};a.r(o),a.d(o,{patchFetch:()=>S,routeModule:()=>b,serverHooks:()=>I,workAsyncStorage:()=>A,workUnitAsyncStorage:()=>C});var i=a(8429),n=a(9874),c=a(8294),l=a(6567),p=a(4144),d=a(5421),u=a(974),m=a(4429),_=a(9975),f=a(1109);let g="edge",y=new m.S,h=f.z.object({key_name:f.z.string().min(1).max(100).optional(),status:f.z.enum(["active","inactive"]).optional(),expires_at:f.z.string().datetime().nullable().optional(),permissions:f.z.object({chat:f.z.boolean().optional(),streaming:f.z.boolean().optional(),all_models:f.z.boolean().optional()}).optional(),allowed_ips:f.z.array(f.z.string().ip()).optional(),allowed_domains:f.z.array(f.z.string()).optional()});async function k(e,{params:t}){try{let a=await y.authenticateRequest(e);if(!a.success)return u.Rp.json({error:{message:a.error,type:"authentication_error",code:"invalid_api_key"}},{status:a.statusCode||401});let{userApiKey:r,userConfig:s,ipAddress:o}=a,{keyId:i}=await t,n=(0,_.Qb)(e),{data:c,error:l}=await n.from("user_generated_api_keys").select(`
        id,
        key_name,
        key_prefix,
        permissions,
        allowed_ips,
        allowed_domains,
        total_requests,
        last_used_at,
        last_used_ip,
        status,
        expires_at,
        created_at,
        updated_at,
        custom_api_configs!inner(
          id,
          name,
          user_id
        )
      `).eq("id",i).single();if(l||!c||c.custom_api_configs.user_id!==s.user_id)return u.Rp.json({error:{message:"API key not found",type:"not_found_error",code:"api_key_not_found"}},{status:404});return y.logApiUsage(r,e,{statusCode:200,modelUsed:"api_key_management",providerUsed:"rokey_api"},o).catch(console.error),u.Rp.json({id:c.id,object:"api_key",key_name:c.key_name,key_prefix:c.key_prefix,masked_key:`${c.key_prefix}_${"*".repeat(28)}xxxx`,permissions:c.permissions,allowed_ips:c.allowed_ips,allowed_domains:c.allowed_domains,total_requests:c.total_requests,last_used_at:c.last_used_at,last_used_ip:c.last_used_ip,status:c.status,expires_at:c.expires_at,created_at:c.created_at,updated_at:c.updated_at,config:{id:c.custom_api_configs.id,name:c.custom_api_configs.name}},{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}catch(e){return u.Rp.json({error:{message:"Internal server error",type:"server_error",code:"internal_error"}},{status:500})}}async function v(e,{params:t}){try{let a=await y.authenticateRequest(e);if(!a.success)return u.Rp.json({error:{message:a.error,type:"authentication_error",code:"invalid_api_key"}},{status:a.statusCode||401});let{userApiKey:r,userConfig:s,ipAddress:o}=a,{keyId:i}=await t,n=await e.json(),c=h.safeParse(n);if(!c.success)return u.Rp.json({error:{message:"Invalid request data",type:"validation_error",code:"invalid_parameters",details:c.error.errors}},{status:400});let l=c.data,p=(0,_.Qb)(e),{data:d,error:m}=await p.from("user_generated_api_keys").update({...l,updated_at:new Date().toISOString()}).eq("id",i).eq("user_id",s.user_id).select(`
        id,
        key_name,
        key_prefix,
        permissions,
        allowed_ips,
        allowed_domains,
        total_requests,
        last_used_at,
        status,
        expires_at,
        created_at,
        updated_at,
        custom_api_configs!inner(
          id,
          name
        )
      `).single();if(m||!d)return u.Rp.json({error:{message:"API key not found or failed to update",type:"not_found_error",code:"api_key_not_found"}},{status:404});return y.logApiUsage(r,e,{statusCode:200,modelUsed:"api_key_management",providerUsed:"rokey_api"},o).catch(console.error),u.Rp.json({id:d.id,object:"api_key",key_name:d.key_name,key_prefix:d.key_prefix,masked_key:`${d.key_prefix}_${"*".repeat(28)}xxxx`,permissions:d.permissions,allowed_ips:d.allowed_ips,allowed_domains:d.allowed_domains,total_requests:d.total_requests,last_used_at:d.last_used_at,status:d.status,expires_at:d.expires_at,created_at:d.created_at,updated_at:d.updated_at,config:{id:d.custom_api_configs.id,name:d.custom_api_configs.name}},{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}catch(e){return u.Rp.json({error:{message:"Internal server error",type:"server_error",code:"internal_error"}},{status:500})}}async function x(e,{params:t}){try{let a=await y.authenticateRequest(e);if(!a.success)return u.Rp.json({error:{message:a.error,type:"authentication_error",code:"invalid_api_key"}},{status:a.statusCode||401});let{userApiKey:r,userConfig:s,ipAddress:o}=a,{keyId:i}=await t,n=(0,_.Qb)(e),{data:c,error:l}=await n.from("user_generated_api_keys").select(`
        id,
        key_name,
        custom_api_configs!inner(
          user_id
        )
      `).eq("id",i).single();if(l||!c||c.custom_api_configs.user_id!==s.user_id)return u.Rp.json({error:{message:"API key not found",type:"not_found_error",code:"api_key_not_found"}},{status:404});let{error:p}=await n.from("user_generated_api_keys").update({status:"revoked",updated_at:new Date().toISOString()}).eq("id",i).eq("user_id",s.user_id);if(p)return u.Rp.json({error:{message:"Failed to revoke API key",type:"server_error",code:"database_error"}},{status:500});return y.logApiUsage(r,e,{statusCode:200,modelUsed:"api_key_management",providerUsed:"rokey_api"},o).catch(console.error),u.Rp.json({id:i,object:"api_key",revoked:!0,message:"API key has been revoked and is no longer valid"},{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}catch(e){return u.Rp.json({error:{message:"Internal server error",type:"server_error",code:"internal_error"}},{status:500})}}async function w(){return new u.Rp(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}let b=new l.AppRouteRouteModule({definition:{kind:p.A.APP_ROUTE,page:"/api/external/v1/api-keys/[keyId]/route",pathname:"/api/external/v1/api-keys/[keyId]",filename:"route",bundlePath:"app/api/external/v1/api-keys/[keyId]/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\external\\v1\\api-keys\\[keyId]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:A,workUnitAsyncStorage:C,serverHooks:I}=b;function S(){return(0,d.V5)({workAsyncStorage:A,workUnitAsyncStorage:C})}let R=null==(r=self.__RSC_MANIFEST)?void 0:r["/api/external/v1/api-keys/[keyId]/route"],T=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);R&&T&&(0,n.fQ)({page:"/api/external/v1/api-keys/[keyId]/route",clientReferenceManifest:R,serverActionsManifest:T,serverModuleMap:(0,i.e)({serverActionsManifest:T})});let P=o,E=c.s.wrap(b,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!0},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp","image/avif"],dangerouslyAllowSVG:!0,contentSecurityPolicy:"default-src 'self'; script-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"raw.githubusercontent.com",port:"",pathname:"/lobehub/lobe-icons/**"},{protocol:"https",hostname:"registry.npmmirror.com",port:"",pathname:"/@lobehub/icons-static-png/**"},{protocol:"https",hostname:"cdn.jsdelivr.net",port:"",pathname:"/npm/simple-icons@latest/icons/**"},{protocol:"https",hostname:"cdn.jsdelivr.net",port:"",pathname:"/npm/simple-icons@v11/icons/**"},{protocol:"https",hostname:"images.unsplash.com",port:"",pathname:"/**"},{protocol:"https",hostname:"cloud.gmelius.com",port:"",pathname:"/public/logos/**"},{protocol:"https",hostname:"upload.wikimedia.org",port:"",pathname:"/wikipedia/commons/**"},{protocol:"https",hostname:"kstatic.googleusercontent.com",port:"",pathname:"/files/**"}],unoptimized:!1},devIndicators:{position:"bottom-left"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"C:\\RoKey App\\rokey-app",experimental:{nodeMiddleware:!1,cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,dynamicOnHover:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!0,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!0,largePageDataBytes:128e3,typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,viewTransition:!1,routerBFCache:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,useCache:!1,optimizePackageImports:["@heroicons/react","@headlessui/react","react-markdown","react-syntax-highlighter","@supabase/supabase-js","lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},htmlLimitedBots:"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti",bundlePagesRouterDependencies:!1,configFile:"C:\\RoKey App\\rokey-app\\next.config.mjs",configFileName:"next.config.mjs",serverExternalPackages:["pdf-parse","mammoth"],turbopack:{rules:{"*.svg":{loaders:["@svgr/webpack"],as:"*.js"}},root:"C:\\RoKey App\\rokey-app"},compiler:{removeConsole:!0,reactRemoveProperties:!0},_originalRedirects:[]}})},9975:(e,t,a)=>{"use strict";a.d(t,{Qb:()=>s});var r=a(3339);function s(e){return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,a){},remove(e,t){}}})}a(2710)}},e=>{var t=t=>e(e.s=t);e.O(0,[580,918,109,44,833],()=>t(8645));var a=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/external/v1/api-keys/[keyId]/route"]=a}]);
//# sourceMappingURL=route.js.map