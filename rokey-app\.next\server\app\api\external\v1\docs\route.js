(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[169],{5356:e=>{"use strict";e.exports=require("node:buffer")},5521:e=>{"use strict";e.exports=require("node:async_hooks")},6487:()=>{},8099:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ComponentMod:()=>k,default:()=>A});var o,r={};i.r(r),i.d(r,{GET:()=>g,OPTIONS:()=>y,runtime:()=>u});var a={};i.r(a),i.d(a,{patchFetch:()=>x,routeModule:()=>f,serverHooks:()=>v,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>b});var n=i(8429),s=i(9874),c=i(8294),p=i(6567),l=i(4144),m=i(5421),d=i(974);let u="edge";async function g(e){let t={openapi:"3.0.3",info:{title:"RouKey External API",description:"Comprehensive API for managing RouKey configurations, provider keys, and intelligent routing. Build powerful AI applications with maximum control and flexibility.",version:"1.0.0",contact:{name:"RouKey Support",email:"<EMAIL>",url:"https://roukey.online"},license:{name:"Commercial License",url:"https://roukey.online/license"}},servers:[{url:"https://roukey.online/api/external/v1",description:"Production server"}],security:[{ApiKeyAuth:[]}],components:{securitySchemes:{ApiKeyAuth:{type:"http",scheme:"bearer",bearerFormat:"API Key",description:"Use your RouKey-generated API key as a Bearer token"}},schemas:{Error:{type:"object",properties:{error:{type:"object",properties:{message:{type:"string"},type:{type:"string"},code:{type:"string"},details:{type:"array",items:{type:"object"}}}}}},Config:{type:"object",properties:{id:{type:"string",format:"uuid"},object:{type:"string",enum:["config"]},name:{type:"string"},description:{type:"string"},routing_strategy:{type:"string",enum:["load_balancing","role_routing","agent_mode"]},settings:{type:"object"},created_at:{type:"string",format:"date-time"},updated_at:{type:"string",format:"date-time"}}},ProviderKey:{type:"object",properties:{id:{type:"string",format:"uuid"},object:{type:"string",enum:["provider_key"]},provider:{type:"string"},label:{type:"string"},status:{type:"string",enum:["active","inactive"]},is_default_general_chat_model:{type:"boolean"},temperature:{type:"number",minimum:0,maximum:2},created_at:{type:"string",format:"date-time"},updated_at:{type:"string",format:"date-time"}}},Model:{type:"object",properties:{id:{type:"string"},object:{type:"string",enum:["model"]},created:{type:"integer"},owned_by:{type:"string"},rokey_extensions:{type:"object",properties:{display_name:{type:"string"},description:{type:"string"},family:{type:"string"},context_window:{type:"integer"},modality:{type:"string"},provider:{type:"string"}}}}}}},paths:{"/chat/completions":{post:{summary:"Create chat completion",description:"OpenAI-compatible chat completions with intelligent routing",tags:["Chat"],requestBody:{required:!0,content:{"application/json":{schema:{type:"object",required:["messages"],properties:{model:{type:"string",default:"gpt-3.5-turbo"},messages:{type:"array",items:{type:"object",properties:{role:{type:"string",enum:["user","assistant","system"]},content:{type:"string"}}}},temperature:{type:"number",minimum:0,maximum:2},max_tokens:{type:"integer"},stream:{type:"boolean"},role:{type:"string",description:"RouKey-specific role for intelligent routing"}}}}}},responses:{200:{description:"Successful completion",content:{"application/json":{schema:{type:"object",properties:{id:{type:"string"},object:{type:"string"},created:{type:"integer"},model:{type:"string"},choices:{type:"array"},usage:{type:"object"}}}}}}}}},"/configs":{get:{summary:"List configurations",description:"Get all your RouKey configurations",tags:["Configuration Management"],responses:{200:{description:"List of configurations",content:{"application/json":{schema:{type:"object",properties:{object:{type:"string",enum:["list"]},data:{type:"array",items:{$ref:"#/components/schemas/Config"}}}}}}}}},post:{summary:"Create configuration",description:"Create a new RouKey configuration",tags:["Configuration Management"],requestBody:{required:!0,content:{"application/json":{schema:{type:"object",required:["name"],properties:{name:{type:"string",maxLength:100},description:{type:"string",maxLength:500},routing_strategy:{type:"string",enum:["load_balancing","role_routing","agent_mode"]},settings:{type:"object"}}}}}},responses:{201:{description:"Configuration created",content:{"application/json":{schema:{$ref:"#/components/schemas/Config"}}}}}}},"/configs/{configId}":{get:{summary:"Get configuration",description:"Get a specific configuration by ID",tags:["Configuration Management"],parameters:[{name:"configId",in:"path",required:!0,schema:{type:"string",format:"uuid"}}],responses:{200:{description:"Configuration details",content:{"application/json":{schema:{$ref:"#/components/schemas/Config"}}}}}},put:{summary:"Update configuration",description:"Update an existing configuration",tags:["Configuration Management"],parameters:[{name:"configId",in:"path",required:!0,schema:{type:"string",format:"uuid"}}],requestBody:{required:!0,content:{"application/json":{schema:{type:"object",properties:{name:{type:"string",maxLength:100},description:{type:"string",maxLength:500},routing_strategy:{type:"string",enum:["load_balancing","role_routing","agent_mode"]},settings:{type:"object"}}}}}},responses:{200:{description:"Configuration updated",content:{"application/json":{schema:{$ref:"#/components/schemas/Config"}}}}}},delete:{summary:"Delete configuration",description:"Delete a configuration and all its associated keys",tags:["Configuration Management"],parameters:[{name:"configId",in:"path",required:!0,schema:{type:"string",format:"uuid"}}],responses:{200:{description:"Configuration deleted",content:{"application/json":{schema:{type:"object",properties:{id:{type:"string"},object:{type:"string"},deleted:{type:"boolean"}}}}}}}}},"/models":{get:{summary:"List models",description:"Get all available AI models (OpenAI-compatible)",tags:["Models & Providers"],responses:{200:{description:"List of available models",content:{"application/json":{schema:{type:"object",properties:{object:{type:"string",enum:["list"]},data:{type:"array",items:{$ref:"#/components/schemas/Model"}}}}}}}}}},"/usage":{get:{summary:"Get usage analytics",description:"Retrieve detailed usage statistics and analytics",tags:["Analytics"],parameters:[{name:"start_date",in:"query",schema:{type:"string",format:"date-time"},description:"Start date for usage period"},{name:"end_date",in:"query",schema:{type:"string",format:"date-time"},description:"End date for usage period"},{name:"granularity",in:"query",schema:{type:"string",enum:["hour","day","week","month"]},description:"Time granularity for aggregation"}],responses:{200:{description:"Usage analytics data",content:{"application/json":{schema:{type:"object",properties:{object:{type:"string",enum:["usage_report"]},period:{type:"object"},totals:{type:"object"},time_series:{type:"array"},breakdowns:{type:"object"}}}}}}}}}},tags:[{name:"Chat",description:"OpenAI-compatible chat completions with intelligent routing"},{name:"Configuration Management",description:"Manage your RouKey configurations"},{name:"Provider Key Management",description:"Manage API keys for different AI providers"},{name:"Models & Providers",description:"Information about available models and providers"},{name:"Analytics",description:"Usage statistics and analytics"},{name:"Routing",description:"Configure intelligent routing strategies"}]};return d.Rp.json(t,{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key","Content-Type":"application/json"}})}async function y(){return new d.Rp(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}let f=new p.AppRouteRouteModule({definition:{kind:l.A.APP_ROUTE,page:"/api/external/v1/docs/route",pathname:"/api/external/v1/docs",filename:"route",bundlePath:"app/api/external/v1/docs/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\external\\v1\\docs\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:h,workUnitAsyncStorage:b,serverHooks:v}=f;function x(){return(0,m.V5)({workAsyncStorage:h,workUnitAsyncStorage:b})}let C=null==(o=self.__RSC_MANIFEST)?void 0:o["/api/external/v1/docs/route"],j=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);C&&j&&(0,s.fQ)({page:"/api/external/v1/docs/route",clientReferenceManifest:C,serverActionsManifest:j,serverModuleMap:(0,n.e)({serverActionsManifest:j})});let k=a,A=c.s.wrap(f,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!0},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp","image/avif"],dangerouslyAllowSVG:!0,contentSecurityPolicy:"default-src 'self'; script-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"raw.githubusercontent.com",port:"",pathname:"/lobehub/lobe-icons/**"},{protocol:"https",hostname:"registry.npmmirror.com",port:"",pathname:"/@lobehub/icons-static-png/**"},{protocol:"https",hostname:"cdn.jsdelivr.net",port:"",pathname:"/npm/simple-icons@latest/icons/**"},{protocol:"https",hostname:"cdn.jsdelivr.net",port:"",pathname:"/npm/simple-icons@v11/icons/**"},{protocol:"https",hostname:"images.unsplash.com",port:"",pathname:"/**"},{protocol:"https",hostname:"cloud.gmelius.com",port:"",pathname:"/public/logos/**"},{protocol:"https",hostname:"upload.wikimedia.org",port:"",pathname:"/wikipedia/commons/**"},{protocol:"https",hostname:"kstatic.googleusercontent.com",port:"",pathname:"/files/**"}],unoptimized:!1},devIndicators:{position:"bottom-left"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"C:\\RoKey App\\rokey-app",experimental:{nodeMiddleware:!1,cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,dynamicOnHover:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!0,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!0,largePageDataBytes:128e3,typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,viewTransition:!1,routerBFCache:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,useCache:!1,optimizePackageImports:["@heroicons/react","@headlessui/react","react-markdown","react-syntax-highlighter","@supabase/supabase-js","lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},htmlLimitedBots:"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti",bundlePagesRouterDependencies:!1,configFile:"C:\\RoKey App\\rokey-app\\next.config.mjs",configFileName:"next.config.mjs",serverExternalPackages:["pdf-parse","mammoth"],turbopack:{rules:{"*.svg":{loaders:["@svgr/webpack"],as:"*.js"}},root:"C:\\RoKey App\\rokey-app"},compiler:{removeConsole:!0,reactRemoveProperties:!0},_originalRedirects:[]}})},8335:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[580],()=>t(8099));var i=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/external/v1/docs/route"]=i}]);
//# sourceMappingURL=route.js.map