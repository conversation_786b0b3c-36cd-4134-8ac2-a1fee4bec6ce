(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[442],{5356:e=>{"use strict";e.exports=require("node:buffer")},5521:e=>{"use strict";e.exports=require("node:async_hooks")},7081:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ComponentMod:()=>C,default:()=>R});var r,a={};i.r(a),i.d(a,{GET:()=>_,OPTIONS:()=>v,runtime:()=>h});var s={};i.r(s),i.d(s,{patchFetch:()=>w,routeModule:()=>b,serverHooks:()=>x,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>k});var o=i(8429),n=i(9874),c=i(8294),p=i(6567),l=i(4144),u=i(5421),m=i(974),d=i(4429);let h="edge",f=new d.S,g=[{id:"openai",name:"OpenAI",description:"Leading AI research company with GPT models",website:"https://openai.com",api_base_url:"https://api.openai.com/v1",supported_features:["chat","completions","embeddings","images","audio","vision"],authentication_type:"bearer_token",rate_limits:{requests_per_minute:3500,tokens_per_minute:9e4}},{id:"anthropic",name:"Anthropic",description:"AI safety company with Claude models",website:"https://anthropic.com",api_base_url:"https://api.anthropic.com/v1",supported_features:["chat","completions","vision"],authentication_type:"api_key",rate_limits:{requests_per_minute:1e3,tokens_per_minute:4e4}},{id:"google",name:"Google AI",description:"Google's Gemini models and AI services",website:"https://ai.google.dev",api_base_url:"https://generativelanguage.googleapis.com/v1beta",supported_features:["chat","completions","vision","embeddings"],authentication_type:"api_key",rate_limits:{requests_per_minute:1500,tokens_per_minute:32e3}},{id:"cohere",name:"Cohere",description:"Enterprise-focused language models",website:"https://cohere.ai",api_base_url:"https://api.cohere.ai/v1",supported_features:["chat","completions","embeddings","rerank"],authentication_type:"bearer_token",rate_limits:{requests_per_minute:1e3,tokens_per_minute:4e4}},{id:"mistral",name:"Mistral AI",description:"Open and efficient language models",website:"https://mistral.ai",api_base_url:"https://api.mistral.ai/v1",supported_features:["chat","completions","embeddings"],authentication_type:"bearer_token",rate_limits:{requests_per_minute:1e3,tokens_per_minute:3e4}},{id:"perplexity",name:"Perplexity AI",description:"Search-augmented language models",website:"https://perplexity.ai",api_base_url:"https://api.perplexity.ai",supported_features:["chat","completions","search"],authentication_type:"bearer_token",rate_limits:{requests_per_minute:500,tokens_per_minute:2e4}},{id:"groq",name:"Groq",description:"Ultra-fast inference for language models",website:"https://groq.com",api_base_url:"https://api.groq.com/openai/v1",supported_features:["chat","completions"],authentication_type:"bearer_token",rate_limits:{requests_per_minute:30,tokens_per_minute:6e3}},{id:"together",name:"Together AI",description:"Open source models with fast inference",website:"https://together.ai",api_base_url:"https://api.together.xyz/v1",supported_features:["chat","completions","embeddings"],authentication_type:"bearer_token",rate_limits:{requests_per_minute:600,tokens_per_minute:3e4}},{id:"fireworks",name:"Fireworks AI",description:"Fast inference for open source models",website:"https://fireworks.ai",api_base_url:"https://api.fireworks.ai/inference/v1",supported_features:["chat","completions","embeddings"],authentication_type:"bearer_token",rate_limits:{requests_per_minute:600,tokens_per_minute:4e4}},{id:"deepseek",name:"DeepSeek",description:"Advanced reasoning and coding models",website:"https://deepseek.com",api_base_url:"https://api.deepseek.com/v1",supported_features:["chat","completions"],authentication_type:"bearer_token",rate_limits:{requests_per_minute:500,tokens_per_minute:3e4}}];async function _(e){try{let t=await f.authenticateRequest(e);if(!t.success)return m.Rp.json({error:{message:t.error,type:"authentication_error",code:"invalid_api_key"}},{status:t.statusCode||401});let{userApiKey:i,userConfig:r,ipAddress:a}=t,{searchParams:s}=new URL(e.url),o=s.get("feature"),n=s.get("auth_type"),c=g;return o&&(c=c.filter(e=>e.supported_features.includes(o))),n&&(c=c.filter(e=>e.authentication_type===n)),f.logApiUsage(i,e,{statusCode:200,modelUsed:"provider_listing",providerUsed:"rokey_api"},a).catch(console.error),m.Rp.json({object:"list",data:c,has_more:!1,total_count:c.length},{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key","X-RouKey-Total-Providers":g.length.toString(),"X-RouKey-Filtered-Count":c.length.toString()}})}catch(e){return m.Rp.json({error:{message:"Internal server error",type:"server_error",code:"internal_error"}},{status:500})}}async function v(){return new m.Rp(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}let b=new p.AppRouteRouteModule({definition:{kind:l.A.APP_ROUTE,page:"/api/external/v1/providers/route",pathname:"/api/external/v1/providers",filename:"route",bundlePath:"app/api/external/v1/providers/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\external\\v1\\providers\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:y,workUnitAsyncStorage:k,serverHooks:x}=b;function w(){return(0,u.V5)({workAsyncStorage:y,workUnitAsyncStorage:k})}let S=null==(r=self.__RSC_MANIFEST)?void 0:r["/api/external/v1/providers/route"],A=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);S&&A&&(0,n.fQ)({page:"/api/external/v1/providers/route",clientReferenceManifest:S,serverActionsManifest:A,serverModuleMap:(0,o.e)({serverActionsManifest:A})});let C=s,R=c.s.wrap(b,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!0},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp","image/avif"],dangerouslyAllowSVG:!0,contentSecurityPolicy:"default-src 'self'; script-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"raw.githubusercontent.com",port:"",pathname:"/lobehub/lobe-icons/**"},{protocol:"https",hostname:"registry.npmmirror.com",port:"",pathname:"/@lobehub/icons-static-png/**"},{protocol:"https",hostname:"cdn.jsdelivr.net",port:"",pathname:"/npm/simple-icons@latest/icons/**"},{protocol:"https",hostname:"cdn.jsdelivr.net",port:"",pathname:"/npm/simple-icons@v11/icons/**"},{protocol:"https",hostname:"images.unsplash.com",port:"",pathname:"/**"},{protocol:"https",hostname:"cloud.gmelius.com",port:"",pathname:"/public/logos/**"},{protocol:"https",hostname:"upload.wikimedia.org",port:"",pathname:"/wikipedia/commons/**"},{protocol:"https",hostname:"kstatic.googleusercontent.com",port:"",pathname:"/files/**"}],unoptimized:!1},devIndicators:{position:"bottom-left"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"C:\\RoKey App\\rokey-app",experimental:{nodeMiddleware:!1,cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,dynamicOnHover:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!0,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!0,largePageDataBytes:128e3,typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,viewTransition:!1,routerBFCache:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,useCache:!1,optimizePackageImports:["@heroicons/react","@headlessui/react","react-markdown","react-syntax-highlighter","@supabase/supabase-js","lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},htmlLimitedBots:"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti",bundlePagesRouterDependencies:!1,configFile:"C:\\RoKey App\\rokey-app\\next.config.mjs",configFileName:"next.config.mjs",serverExternalPackages:["pdf-parse","mammoth"],turbopack:{rules:{"*.svg":{loaders:["@svgr/webpack"],as:"*.js"}},root:"C:\\RoKey App\\rokey-app"},compiler:{removeConsole:!0,reactRemoveProperties:!0},api:{bodyParser:{sizeLimit:"50mb"},responseLimit:"50mb"},_originalRedirects:[]}})}},e=>{var t=t=>e(e.s=t);e.O(0,[580,918,833],()=>t(7081));var i=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/external/v1/providers/route"]=i}]);
//# sourceMappingURL=route.js.map