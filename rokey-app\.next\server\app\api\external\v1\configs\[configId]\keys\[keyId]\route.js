(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[675],{1029:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>P,default:()=>j});var a,o={};r.r(o),r.d(o,{DELETE:()=>w,GET:()=>k,OPTIONS:()=>x,PUT:()=>b,runtime:()=>h});var i={};r.r(i),r.d(i,{patchFetch:()=>R,routeModule:()=>I,serverHooks:()=>S,workAsyncStorage:()=>C,workUnitAsyncStorage:()=>A});var s=r(8429),n=r(9874),c=r(8294),l=r(6567),d=r(4144),p=r(5421),u=r(974),m=r(4429),f=r(9975),_=r(770),g=r(1109);let h="edge",y=new m.S,v=g.z.object({api_key:g.z.string().min(1).optional(),label:g.z.string().min(1).max(100).optional(),temperature:g.z.number().min(0).max(2).optional(),is_default_general_chat_model:g.z.boolean().optional(),status:g.z.enum(["active","inactive"]).optional()});async function k(e,{params:t}){try{let r=await y.authenticateRequest(e);if(!r.success)return u.Rp.json({error:{message:r.error,type:"authentication_error",code:"invalid_api_key"}},{status:r.statusCode||401});let{userApiKey:a,userConfig:o,ipAddress:i}=r,{configId:s,keyId:n}=await t,c=(0,f.Qb)(e),{data:l,error:d}=await c.from("api_keys").select(`
        id,
        provider,
        label,
        status,
        is_default_general_chat_model,
        temperature,
        created_at,
        updated_at,
        last_used_at,
        predefined_models(
          id,
          name,
          display_name,
          provider_id
        ),
        custom_api_configs!inner(
          id,
          user_id
        )
      `).eq("id",n).eq("custom_api_config_id",s).single();if(d||!l||l.custom_api_configs.user_id!==o.user_id)return u.Rp.json({error:{message:"Provider key not found",type:"not_found_error",code:"key_not_found"}},{status:404});return y.logApiUsage(a,e,{statusCode:200,modelUsed:"key_management",providerUsed:"rokey_api"},i).catch(console.error),u.Rp.json({id:l.id,object:"provider_key",provider:l.provider,label:l.label,status:l.status,is_default_general_chat_model:l.is_default_general_chat_model,temperature:l.temperature,created_at:l.created_at,updated_at:l.updated_at,last_used_at:l.last_used_at,model:l.predefined_models},{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}catch(e){return u.Rp.json({error:{message:"Internal server error",type:"server_error",code:"internal_error"}},{status:500})}}async function b(e,{params:t}){try{let r=await y.authenticateRequest(e);if(!r.success)return u.Rp.json({error:{message:r.error,type:"authentication_error",code:"invalid_api_key"}},{status:r.statusCode||401});let{userApiKey:a,userConfig:o,ipAddress:i}=r,{configId:s,keyId:n}=await t,c=await e.json(),l=v.safeParse(c);if(!l.success)return u.Rp.json({error:{message:"Invalid request data",type:"validation_error",code:"invalid_parameters",details:l.error.errors}},{status:400});let d=l.data,p=(0,f.Qb)(e),m={...d,updated_at:new Date().toISOString()};if(d.api_key){let e=await (0,_.w)(d.api_key),t=new TextEncoder().encode(d.api_key),r=await crypto.subtle.digest("SHA-256",t),a=Array.from(new Uint8Array(r)).map(e=>e.toString(16).padStart(2,"0")).join("");m.encrypted_api_key=e,m.api_key_hash=a,delete m.api_key}let{data:g,error:h}=await p.from("api_keys").update(m).eq("id",n).eq("custom_api_config_id",s).eq("user_id",o.user_id).select(`
        id,
        provider,
        label,
        status,
        is_default_general_chat_model,
        temperature,
        created_at,
        updated_at,
        predefined_models(
          id,
          name,
          display_name,
          provider_id
        )
      `).single();if(h||!g)return u.Rp.json({error:{message:"Provider key not found or failed to update",type:"not_found_error",code:"key_not_found"}},{status:404});return y.logApiUsage(a,e,{statusCode:200,modelUsed:"key_management",providerUsed:"rokey_api"},i).catch(console.error),u.Rp.json({id:g.id,object:"provider_key",provider:g.provider,label:g.label,status:g.status,is_default_general_chat_model:g.is_default_general_chat_model,temperature:g.temperature,created_at:g.created_at,updated_at:g.updated_at,model:g.predefined_models},{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}catch(e){return u.Rp.json({error:{message:"Internal server error",type:"server_error",code:"internal_error"}},{status:500})}}async function w(e,{params:t}){try{let r=await y.authenticateRequest(e);if(!r.success)return u.Rp.json({error:{message:r.error,type:"authentication_error",code:"invalid_api_key"}},{status:r.statusCode||401});let{userApiKey:a,userConfig:o,ipAddress:i}=r,{configId:s,keyId:n}=await t,c=(0,f.Qb)(e),{data:l,error:d}=await c.from("api_keys").select(`
        id,
        label,
        custom_api_configs!inner(
          user_id
        )
      `).eq("id",n).eq("custom_api_config_id",s).single();if(d||!l||l.custom_api_configs.user_id!==o.user_id)return u.Rp.json({error:{message:"Provider key not found",type:"not_found_error",code:"key_not_found"}},{status:404});let{error:p}=await c.from("api_keys").delete().eq("id",n).eq("custom_api_config_id",s).eq("user_id",o.user_id);if(p)return u.Rp.json({error:{message:"Failed to delete provider key",type:"server_error",code:"database_error"}},{status:500});return y.logApiUsage(a,e,{statusCode:200,modelUsed:"key_management",providerUsed:"rokey_api"},i).catch(console.error),u.Rp.json({id:n,object:"provider_key",deleted:!0},{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}catch(e){return u.Rp.json({error:{message:"Internal server error",type:"server_error",code:"internal_error"}},{status:500})}}async function x(){return new u.Rp(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}let I=new l.AppRouteRouteModule({definition:{kind:d.A.APP_ROUTE,page:"/api/external/v1/configs/[configId]/keys/[keyId]/route",pathname:"/api/external/v1/configs/[configId]/keys/[keyId]",filename:"route",bundlePath:"app/api/external/v1/configs/[configId]/keys/[keyId]/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\external\\v1\\configs\\[configId]\\keys\\[keyId]\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:C,workUnitAsyncStorage:A,serverHooks:S}=I;function R(){return(0,p.V5)({workAsyncStorage:C,workUnitAsyncStorage:A})}let T=null==(a=self.__RSC_MANIFEST)?void 0:a["/api/external/v1/configs/[configId]/keys/[keyId]/route"],E=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);T&&E&&(0,n.fQ)({page:"/api/external/v1/configs/[configId]/keys/[keyId]/route",clientReferenceManifest:T,serverActionsManifest:E,serverModuleMap:(0,s.e)({serverActionsManifest:E})});let P=i,j=c.s.wrap(I,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!0},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp","image/avif"],dangerouslyAllowSVG:!0,contentSecurityPolicy:"default-src 'self'; script-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"raw.githubusercontent.com",port:"",pathname:"/lobehub/lobe-icons/**"},{protocol:"https",hostname:"registry.npmmirror.com",port:"",pathname:"/@lobehub/icons-static-png/**"},{protocol:"https",hostname:"cdn.jsdelivr.net",port:"",pathname:"/npm/simple-icons@latest/icons/**"},{protocol:"https",hostname:"cdn.jsdelivr.net",port:"",pathname:"/npm/simple-icons@v11/icons/**"},{protocol:"https",hostname:"images.unsplash.com",port:"",pathname:"/**"},{protocol:"https",hostname:"cloud.gmelius.com",port:"",pathname:"/public/logos/**"},{protocol:"https",hostname:"upload.wikimedia.org",port:"",pathname:"/wikipedia/commons/**"},{protocol:"https",hostname:"kstatic.googleusercontent.com",port:"",pathname:"/files/**"}],unoptimized:!1},devIndicators:{position:"bottom-left"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"C:\\RoKey App\\rokey-app",experimental:{nodeMiddleware:!1,cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,dynamicOnHover:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!0,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!0,largePageDataBytes:128e3,typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,viewTransition:!1,routerBFCache:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,useCache:!1,optimizePackageImports:["@heroicons/react","@headlessui/react","react-markdown","react-syntax-highlighter","@supabase/supabase-js","lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},htmlLimitedBots:"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti",bundlePagesRouterDependencies:!1,configFile:"C:\\RoKey App\\rokey-app\\next.config.mjs",configFileName:"next.config.mjs",serverExternalPackages:["pdf-parse","mammoth"],turbopack:{rules:{"*.svg":{loaders:["@svgr/webpack"],as:"*.js"}},root:"C:\\RoKey App\\rokey-app"},compiler:{removeConsole:!0,reactRemoveProperties:!0},_originalRedirects:[]}})},5356:e=>{"use strict";e.exports=require("node:buffer")},5521:e=>{"use strict";e.exports=require("node:async_hooks")},9975:(e,t,r)=>{"use strict";r.d(t,{Qb:()=>o});var a=r(3339);function o(e){return(0,a.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}r(2710)}},e=>{var t=t=>e(e.s=t);e.O(0,[580,918,109,44,833],()=>t(1029));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/external/v1/configs/[configId]/keys/[keyId]/route"]=r}]);
//# sourceMappingURL=route.js.map