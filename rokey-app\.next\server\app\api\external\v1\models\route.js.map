{"version": 3, "file": "app/api/external/v1/models/route.js", "mappings": "qFAAA,wDCAA,wHGAA,0RFKO,IAAMA,EAAU,OAAO,EAEP,IAAIC,EAAAA,CAAoBA,CAGxC,UAHaC,KAGEC,EAAIC,CAAoB,EAC5C,GAAI,CAEF,IAAMC,EAAa,MAAMH,EAAeI,kBAADJ,CAAoB,CAACE,GAE5D,GAAI,CAACC,EAAWE,OAAO,CACrB,CADuB,MAChBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAASN,EAAWK,KAAK,CACzBE,KAAM,uBACNC,KAAM,iBACR,CACF,EACA,CAAEC,OAAQT,EAAWU,UAAU,EAAI,GAAI,GAI3C,GAAM,YAAEC,CAAU,YAAEC,CAAU,WAAEC,CAAS,CAAE,CAAGb,EAGxCc,EAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAqCA,CAAChB,GAEjD,CAAEiB,KAAMC,CAAM,OAAEZ,CAAK,CAAE,CAAG,MAAMS,EACnCI,IAAI,CAAC,UACLC,MAAM,CAAC,CAAC;;;;;;;;;;;;MAYT,CAAC,EACAC,EAAE,CAAC,aAAa,GAChBC,KAAK,CAAC,cAAe,CAAEC,WAAW,CAAK,GACvCD,KAAK,CAAC,OAAQ,CAAEC,WAAW,CAAK,GAEnC,GAAIjB,EAEF,KAFS,EAEFF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,yBACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,GAKlB,IAAMc,EAAyB,CAACN,GAAU,IAAIO,GAAG,CAACC,GAAU,EAC1DC,GAAID,EAAME,IAAI,CACdC,OAAQ,QACRC,QAASC,KAAKC,KAAK,CAAC,IAAIC,OAAOC,OAAO,GAAK,KAC3CC,SAAUT,EAAMU,WAAW,CAC3BC,WAAY,CACV,CACEV,GAAI,CAAC,UAAU,EAAED,EAAMC,EAAE,EAAE,CAC3BE,OAAQ,mBACRC,QAASC,KAAKC,KAAK,CAAC,IAAIC,OAAOC,OAAO,GAAK,KAC3CI,qBAAqB,EACrBC,gBAAgB,EAChBC,gBAAgB,EAChBC,sBAAsB,EACtBC,YAAY,EACZC,mBAAmB,EACnBC,aAAc,IACdC,MAAO,KACPC,aAAa,CACf,EACD,CAEDC,iBAAkB,CAChBC,aAActB,EAAMsB,YAAY,CAChCC,YAAavB,EAAMuB,WAAW,CAC9BC,OAAQxB,EAAMwB,MAAM,CACpBC,eAAgBzB,EAAMyB,cAAc,CACpCC,kBAAmB1B,EAAM0B,iBAAiB,CAC1CC,mBAAoB3B,EAAM2B,kBAAkB,CAC5CC,SAAU5B,EAAM4B,QAAQ,CACxBC,SAAU7B,EAAMU,WAAW,EAE/B,GAcA,OAXAtC,EAAe0D,WAAW,CACxB5C,EACAZ,EACA,CACEW,CAJUb,UAIE,IACZ2D,UAAW,gBACXC,aAAc,WAChB,EACA5C,GACA6C,KAAK,CAACC,QAAQtD,KAAK,EAEdF,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvBwB,OAAQ,OACRZ,KAAMO,CACR,EAAG,CACDqC,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,eAChC,+BAAgC,yCAChC,wBAAyBrC,EAAuBsC,MAAM,CAACC,QAAQ,EACjE,CACF,EAEF,CAAE,MAAOzD,EAAO,CAEd,OAAOF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,wBACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,EAElB,CACF,CAGO,eAAesD,IACpB,OAAO,IAAI5D,EAAAA,EAAYA,CAAC,KAAM,CAC5BM,OAAQ,IACRmD,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,eAChC,+BAAgC,wCAClC,CACF,EACF,CC/IA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,qCACA,mCACA,iBACA,6CACA,CAAK,CACL,2FACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,CAAQ,yDAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,8EACA,EAFA,4BAEA,2BACA,OACI,QAA8B,EAClC,qCACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA0B,aAAe,kDAAyD,wOAAuQ,ySAAoU,mBAAmB,QAAQ,uDAA2D,gGAAwG,EAAE,oGAA4G,EAAE,kGAA0G,EAAE,+FAAuG,EAAE,uEAA+E,EAAE,kFAA0F,EAAE,0FAAkG,EAAE,uFAA+F,iBAAsB,gBAAkB,uBAAyB,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,gEAAoE,6BAAoC,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,o/BAAmsC,qBAAyB,ykDAAkmD,idAAge,OAAS,SAAS,qCAAyC,iCAAmC,WAAa,0CAAkD,MAAQ,YAAc,iBAAmB,sBAAwB,uBAiBruM,CAAC,CAAC,EAAC,4DCsBI,SAAS7C,EAAsChB,CAAoB,EACxE,MAAOiE,CAAAA,EAAAA,EAAAA,kBAAAA,CAAkBA,CACvBC,0CAAoC,CACpCA,kNAAyC,CACzC,CACEC,QAAS,KACPC,GACSpE,CADO,CACCmE,OAAO,CAACC,GAAG,CAACxC,IAAOyC,MAEpCC,IAAI1C,CAAY,CAAEyC,CAAa,CAAEE,CAAsB,EAGvD,EACAC,OAAO5C,CAAY,CAAE2C,CAAsB,EAG3C,CACF,CACF,EAEJ", "sources": ["webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/./src/app/api/external/v1/models/route.ts", "webpack://_N_E/./src/app/api/external/v1/models/route.ts?4713", "webpack://_N_E/?3b35", "webpack://_N_E/./src/lib/supabase/server.ts"], "sourcesContent": ["module.exports = require(\"node:buffer\");", "module.exports = require(\"node:async_hooks\");", "import { type NextRequest, NextResponse } from 'next/server';\nimport { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';\nimport { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';\n\n// Use Edge Runtime for better performance\nexport const runtime = 'edge';\n\nconst authMiddleware = new ApiKeyAuthMiddleware();\n\n// GET /api/external/v1/models - List all available models (OpenAI-compatible)\nexport async function GET(request: NextRequest) {\n  try {\n    // 1. Authenticate using user-generated API key\n    const authResult = await authMiddleware.authenticateRequest(request);\n    \n    if (!authResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: authResult.error,\n            type: 'authentication_error',\n            code: 'invalid_api_key'\n          }\n        },\n        { status: authResult.statusCode || 401 }\n      );\n    }\n\n    const { userApiKey, userConfig, ipAddress } = authResult;\n\n    // 2. Get all available models\n    const supabase = createSupabaseServerClientFromRequest(request);\n    \n    const { data: models, error } = await supabase\n      .from('models')\n      .select(`\n        id,\n        name,\n        display_name,\n        description,\n        provider_id,\n        family,\n        context_window,\n        input_token_limit,\n        output_token_limit,\n        modality,\n        is_public\n      `)\n      .eq('is_public', true)\n      .order('provider_id', { ascending: true })\n      .order('name', { ascending: true });\n\n    if (error) {\n      console.error('Error fetching models:', error);\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Failed to fetch models',\n            type: 'server_error',\n            code: 'database_error'\n          }\n        },\n        { status: 500 }\n      );\n    }\n\n    // 3. Transform to OpenAI-compatible format\n    const openaiCompatibleModels = (models || []).map(model => ({\n      id: model.name, // Use model name as ID for OpenAI compatibility\n      object: 'model',\n      created: Math.floor(new Date().getTime() / 1000), // Unix timestamp\n      owned_by: model.provider_id,\n      permission: [\n        {\n          id: `modelperm-${model.id}`,\n          object: 'model_permission',\n          created: Math.floor(new Date().getTime() / 1000),\n          allow_create_engine: false,\n          allow_sampling: true,\n          allow_logprobs: true,\n          allow_search_indices: false,\n          allow_view: true,\n          allow_fine_tuning: false,\n          organization: '*',\n          group: null,\n          is_blocking: false\n        }\n      ],\n      // RouKey-specific extensions\n      rokey_extensions: {\n        display_name: model.display_name,\n        description: model.description,\n        family: model.family,\n        context_window: model.context_window,\n        input_token_limit: model.input_token_limit,\n        output_token_limit: model.output_token_limit,\n        modality: model.modality,\n        provider: model.provider_id\n      }\n    }));\n\n    // 4. Log API usage\n    authMiddleware.logApiUsage(\n      userApiKey!,\n      request,\n      {\n        statusCode: 200,\n        modelUsed: 'model_listing',\n        providerUsed: 'rokey_api',\n      },\n      ipAddress\n    ).catch(console.error);\n\n    return NextResponse.json({\n      object: 'list',\n      data: openaiCompatibleModels\n    }, {\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n        'X-RouKey-Total-Models': openaiCompatibleModels.length.toString(),\n      }\n    });\n\n  } catch (error) {\n    console.error('Error in models GET API:', error);\n    return NextResponse.json(\n      {\n        error: {\n          message: 'Internal server error',\n          type: 'server_error',\n          code: 'internal_error'\n        }\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// OPTIONS handler for CORS\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n    },\n  });\n}\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\models\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/external/v1/models/route\",\n        pathname: \"/api/external/v1/models\",\n        filename: \"route\",\n        bundlePath: \"app/api/external/v1/models/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\models\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fexternal%2Fv1%2Fmodels%2Froute&page=%2Fapi%2Fexternal%2Fv1%2Fmodels%2Froute&pagePath=private-next-app-dir%2Fapi%2Fexternal%2Fv1%2Fmodels%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&appPaths=%2Fapi%2Fexternal%2Fv1%2Fmodels%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/external/v1/models/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":true},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.mjs\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\",\"image/avif\"],\"dangerouslyAllowSVG\":true,\"contentSecurityPolicy\":\"default-src 'self'; script-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"raw.githubusercontent.com\",\"port\":\"\",\"pathname\":\"/lobehub/lobe-icons/**\"},{\"protocol\":\"https\",\"hostname\":\"registry.npmmirror.com\",\"port\":\"\",\"pathname\":\"/@lobehub/icons-static-png/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@latest/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@v11/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"images.unsplash.com\",\"port\":\"\",\"pathname\":\"/**\"},{\"protocol\":\"https\",\"hostname\":\"cloud.gmelius.com\",\"port\":\"\",\"pathname\":\"/public/logos/**\"},{\"protocol\":\"https\",\"hostname\":\"upload.wikimedia.org\",\"port\":\"\",\"pathname\":\"/wikipedia/commons/**\"},{\"protocol\":\"https\",\"hostname\":\"kstatic.googleusercontent.com\",\"port\":\"\",\"pathname\":\"/files/**\"}],\"unoptimized\":false},\"devIndicators\":{\"position\":\"bottom-left\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"C:\\\\RoKey App\\\\rokey-app\",\"experimental\":{\"nodeMiddleware\":false,\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"dynamicOnHover\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":3,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":true,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":true,\"largePageDataBytes\":128000,\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"viewTransition\":false,\"routerBFCache\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"useCache\":false,\"optimizePackageImports\":[\"@heroicons/react\",\"@headlessui/react\",\"react-markdown\",\"react-syntax-highlighter\",\"@supabase/supabase-js\",\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"htmlLimitedBots\":\"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti\",\"bundlePagesRouterDependencies\":false,\"configFile\":\"C:\\\\RoKey App\\\\rokey-app\\\\next.config.mjs\",\"configFileName\":\"next.config.mjs\",\"serverExternalPackages\":[\"pdf-parse\",\"mammoth\"],\"turbopack\":{\"rules\":{\"*.svg\":{\"loaders\":[\"@svgr/webpack\"],\"as\":\"*.js\"}},\"root\":\"C:\\\\RoKey App\\\\rokey-app\"},\"compiler\":{\"removeConsole\":true,\"reactRemoveProperties\":true},\"api\":{\"bodyParser\":{\"sizeLimit\":\"50mb\"},\"responseLimit\":\"50mb\"},\"_originalRedirects\":[]}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/external/v1/models/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/external/v1/models/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "import { createServerClient, type CookieOptions } from '@supabase/ssr';\r\nimport { createClient } from '@supabase/supabase-js';\r\nimport { cookies } from 'next/headers';\r\nimport { NextRequest } from 'next/server';\r\n\r\n// This is the standard setup for creating a Supabase server client\r\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\r\n// Updated for Next.js 15 async cookies requirement\r\nexport async function createSupabaseServerClientOnRequest() {\r\n  const cookieStore = await cookies();\r\n\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        get(name: string) {\r\n          return cookieStore.get(name)?.value;\r\n        },\r\n        set(name: string, value: string, options: CookieOptions) {\r\n          try {\r\n            cookieStore.set({ name, value, ...options });\r\n          } catch (error) {\r\n            // This error can be ignored if running in a Server Component\r\n            // where cookies can't be set directly. Cookie setting should be\r\n            // handled in Server Actions or Route Handlers.\r\n            console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\r\n          }\r\n        },\r\n        remove(name: string, options: CookieOptions) {\r\n          try {\r\n            // To remove a cookie using the `set` method from `next/headers`,\r\n            // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\r\n            cookieStore.set({ name, value: '', ...options });\r\n          } catch (error) {\r\n            // Similar to set, this might fail in a Server Component.\r\n            console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\r\n          }\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n\r\n// Alternative method for API routes that need to handle cookies from request\r\nexport function createSupabaseServerClientFromRequest(request: NextRequest) {\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        get(name: string) {\r\n          return request.cookies.get(name)?.value;\r\n        },\r\n        set(name: string, value: string, options: CookieOptions) {\r\n          // In API routes, we can't set cookies directly on the request\r\n          // This will be handled by the response\r\n        },\r\n        remove(name: string, options: CookieOptions) {\r\n          // In API routes, we can't remove cookies directly on the request\r\n          // This will be handled by the response\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n\r\n// Service role client for admin operations (OAuth token storage, etc.)\r\nexport function createServiceRoleClient() {\r\n  return createClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.SUPABASE_SERVICE_ROLE_KEY!,\r\n    {\r\n      auth: {\r\n        autoRefreshToken: false,\r\n        persistSession: false\r\n      }\r\n    }\r\n  );\r\n}\r\n"], "names": ["runtime", "ApiKeyAuthMiddleware", "authMiddleware", "GET", "request", "authResult", "authenticateRequest", "success", "NextResponse", "json", "error", "message", "type", "code", "status", "statusCode", "userApiKey", "userConfig", "ip<PERSON><PERSON><PERSON>", "supabase", "createSupabaseServerClientFromRequest", "data", "models", "from", "select", "eq", "order", "ascending", "openaiCompatibleModels", "map", "model", "id", "name", "object", "created", "Math", "floor", "Date", "getTime", "owned_by", "provider_id", "permission", "allow_create_engine", "allow_sampling", "allow_logprobs", "allow_search_indices", "allow_view", "allow_fine_tuning", "organization", "group", "is_blocking", "rokey_extensions", "display_name", "description", "family", "context_window", "input_token_limit", "output_token_limit", "modality", "provider", "logApiUsage", "modelUsed", "providerUsed", "catch", "console", "headers", "length", "toString", "OPTIONS", "createServerClient", "process", "cookies", "get", "value", "set", "options", "remove"], "sourceRoot": "", "ignoreList": []}