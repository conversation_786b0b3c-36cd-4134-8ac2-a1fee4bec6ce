{"version": 3, "file": "app/api/external/v1/configs/[configId]/keys/route.js", "mappings": "oFAAA,wDCAA,wHGAA,wTFOO,IAAMA,EAAU,OAAO,EAEP,IAAIC,EAAAA,CAAoBA,CASzCC,EAA0BC,EAAAA,CAAAA,CAAAA,IATZC,EASoB,CAAC,CACvCC,SAAUF,EAAAA,CAAAA,CAAAA,IAAM,CAAC,CAAC,SAAU,YAAa,SAAU,SAAU,UAAW,aAAc,OAAQ,WAAY,YAAa,WAAW,EAClIG,QAASH,EAAAA,CAAAA,CAAAA,MAAQ,GAAGI,GAAG,CAAC,GACxBC,SAAUL,EAAAA,CAAAA,CAAAA,MAAQ,GAAGM,IAAI,GAAGC,QAAQ,GACpCC,MAAOR,EAAAA,CAAAA,CAAAA,MAAQ,GAAGI,GAAG,CAAC,GAAGK,GAAG,CAAC,KAC7BC,YAAaV,EAAAA,CAAAA,CAAAA,MAAQ,GAAGI,GAAG,CAAC,GAAGK,GAAG,CAAC,GAAGF,QAAQ,GAAGI,OAAO,CAAC,IACzDC,8BAA+BZ,EAAAA,CAAAA,CAAAA,OAAS,GAAGO,QAAQ,GAAGI,OAAO,EAAC,EAChE,GAWO,eAAeE,EAAIC,CAAoB,CAAE,QAAEC,CAAM,CAAe,EACrE,GAAI,CAEF,IAAMC,EAAa,MAAMf,EAAegB,kBAADhB,CAAoB,CAACa,GAE5D,GAAI,CAACE,EAAWE,OAAO,CACrB,CADuB,MAChBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAASN,EAAWK,KAAK,CACzBE,KAAM,uBACNC,KAAM,iBACR,CACF,EACA,CAAEC,OAAQT,EAAWU,UAAU,EAAI,GAAI,GAI3C,GAAM,CAAEC,YAAU,CAAEC,YAAU,WAAEC,CAAS,CAAE,CAAGb,EACxC,UAAEc,CAAQ,CAAE,CAAG,MAAMf,EAGrBgB,EAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAqCA,CAAClB,GAEjD,CAAEmB,KAAMC,CAAM,CAAEb,MAAOc,CAAW,CAAE,CAAG,MAAMJ,EAChDK,IAAI,CAAC,sBACLC,MAAM,CAAC,MACPC,EAAE,CAAC,KAAMR,GACTQ,EAAE,CAAC,UAAWV,EAAYW,OAAO,EACjCC,MAAM,GAET,GAAIL,GAAe,CAACD,EAClB,MAD0B,CACnBf,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,0BACTC,KAAM,kBACNC,KAAM,kBACR,CACF,EACA,CAAEC,OAAQ,GAAI,GAKlB,GAAM,CAAEQ,KAAMQ,CAAI,OAAEpB,CAAK,CAAE,CAAG,MAAMU,EACjCK,IAAI,CAAC,YACLC,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;MAgBT,CAAC,EACAC,EAAE,CAAC,uBAAwBR,GAC3BQ,EAAE,CAAC,UAAWV,EAAYW,OAAO,EACjCG,KAAK,CAAC,aAAc,CAAEC,WAAW,CAAM,GAE1C,GAAItB,EAEF,KAFS,EAEFF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,gCACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,GAgBlB,OAXAxB,EAAe2C,WAAW,CACxBjB,EACAb,EACA,CACEY,CAJUzB,UAIE,IACZ4C,UAAW,iBACXC,aAAc,WAChB,EACAjB,GACAkB,KAAK,CAACC,QAAQ3B,KAAK,EAEdF,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvB6B,OAAQ,OACRhB,KAAMQ,GAAQ,EAAE,CAChBS,UAAU,CACZ,EAAG,CACDC,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,qBAChC,+BAAgC,wCAClC,CACF,EAEF,CAAE,MAAO9B,EAAO,CAEd,OAAOF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,wBACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,EAElB,CACF,CAGO,eAAe2B,EAAKtC,CAAoB,CAAE,QAAEC,CAAM,CAAe,EACtE,GAAI,CAEF,IAAMC,EAAa,MAAMf,EAAegB,kBAADhB,CAAoB,CAACa,GAE5D,GAAI,CAACE,EAAWE,OAAO,CACrB,CADuB,MAChBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAASN,EAAWK,KAAK,CACzBE,KAAM,uBACNC,KAAM,iBACR,CACF,EACA,CAAEC,OAAQT,EAAWU,UAAU,EAAI,GAAI,GAI3C,GAAM,YAAEC,CAAU,YAAEC,CAAU,WAAEC,CAAS,CAAE,CAAGb,EACxC,CAAEc,UAAQ,CAAE,CAAG,MAAMf,EAGrBsC,EAAO,MAAMvC,EAAQM,IAAI,GACzBkC,EAAmBvD,EAAwBwD,SAAS,CAACF,GAE3D,GAAI,CAACC,EAAiBpC,OAAO,CAC3B,CAD6B,MACtBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,uBACTC,KAAM,mBACNC,KAAM,qBACNgC,QAASF,EAAiBjC,KAAK,CAACoC,MAAM,CAE1C,EACA,CAAEhC,OAAQ,GAAI,GAIlB,IAAMiC,EAAUJ,EAAiBrB,IAAI,CAG/BF,EAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAqCA,CAAClB,GAEjD,CAAEmB,KAAMC,CAAM,CAAEb,MAAOc,CAAW,CAAE,CAAG,MAAMJ,EAChDK,IAAI,CAAC,sBACLC,MAAM,CAAC,MACPC,EAAE,CAAC,KAAMR,GACTQ,EAAE,CAAC,UAAWV,EAAYW,OAAO,EACjCC,MAAM,GAET,GAAIL,GAAe,CAACD,EAClB,MAD0B,CACnBf,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,0BACTC,KAAM,kBACNC,KAAM,kBACR,CACF,EACA,CAAEC,OAAQ,GAAI,GAKlB,IAAMkC,EAAkB,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,CAACF,EAAQvD,OAAO,EAI/C8B,EADU,IAAI4B,cACCC,MAAM,CAACJ,EAAQvD,OAAO,EACrC4D,EAAa,MAAMC,OAAOC,MAAM,CAACC,MAAM,CAAC,UAAWjC,GAEnDkC,EADYC,MAAMhC,IAAI,CAAC,IAAIiC,WAAWN,IACfO,GAAG,CAACC,GAAKA,EAAEC,QAAQ,CAAC,IAAIC,QAAQ,CAAC,EAAG,MAAMC,IAAI,CAAC,IAGtE,CAAEzC,KAAM0C,CAAM,OAAEtD,CAAK,CAAE,CAAG,MAAMU,EACnCK,IAAI,CAAC,YACLwC,MAAM,CAAC,CACNC,qBAAsB/C,EACtBS,QAASX,EAAYW,OAAO,CAC5BrC,SAAUwD,EAAQxD,QAAQ,CAC1B4E,oBAAqBpB,EAAQrD,QAAQ,EAAI,KACzC0E,kBAAmBpB,EACnBnD,MAAOkD,EAAQlD,KAAK,CACpBwE,aAAcb,EACd1C,OAAQ,SACRb,8BAA+B8C,EAAQ9C,6BAA6B,CACpEF,YAAagD,EAAQhD,WAAW,GAEjC2B,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;MAeT,CAAC,EACAG,MAAM,GAET,GAAInB,EAEF,KAFS,EAEFF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,gCACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,GAgBlB,OAXAxB,EAAe2C,WAAW,CACxBjB,EACAb,EACA,CACEY,CAJUzB,UAIE,IACZ4C,UAAW,iBACXC,aAAc,WAChB,EACAjB,GACAkB,KAAK,CAACC,QAAQ3B,KAAK,EAEdF,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvB6D,GAAIN,EAAOM,EAAE,CACbhC,OAAQ,eACR/C,SAAUyE,EAAOzE,QAAQ,CACzBM,MAAOmE,EAAOnE,KAAK,CACnBiB,OAAQkD,EAAOlD,MAAM,CACrBb,8BAA+B+D,EAAO/D,6BAA6B,CACnEF,YAAaiE,EAAOjE,WAAW,CAC/BwE,WAAYP,EAAOO,UAAU,CAC7BC,WAAYR,EAAOQ,UAAU,CAC7BC,MAAOT,EAAOU,iBAAiB,EAC9B,CACD5D,OAAQ,IACR0B,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,qBAChC,+BAAgC,wCAClC,CACF,EAEF,CAAE,MAAO9B,EAAO,CAEd,OAAOF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,wBACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,EAElB,CACF,CAGO,eAAe6D,IACpB,OAAO,IAAInE,EAAAA,EAAYA,CAAC,KAAM,CAC5BM,OAAQ,IACR0B,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,qBAChC,+BAAgC,wCAClC,CACF,EACF,CArTgCnD,EAAAA,CAAAA,CAAAA,MAAQ,CAAC,CACvCG,QAASH,EAAAA,CAAAA,CAAAA,MAAQ,GAAGI,GAAG,CAAC,GAAGG,QAAQ,GACnCC,MAAOR,EAAAA,CAAAA,CAAAA,MAAQ,GAAGI,GAAG,CAAC,GAAGK,GAAG,CAAC,KAAKF,QAAQ,GAC1CG,YAAaV,EAAAA,CAAAA,CAAAA,MAAQ,GAAGI,GAAG,CAAC,GAAGK,GAAG,CAAC,GAAGF,QAAQ,GAC9CK,8BAA+BZ,EAAAA,CAAAA,CAAAA,OAAS,GAAGO,QAAQ,GACnDkB,OAAQzB,EAAAA,CAAAA,CAAAA,IAAM,CAAC,CAAC,SAAU,WAAW,EAAEO,QAAQ,EACjD,GC1BA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,sDACA,oDACA,iBACA,8DACA,CAAK,CACL,8GACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,+FACA,EAFA,4BAEA,2BACA,OACI,QAA8B,EAClC,sDACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA0B,aAAe,kDAAyD,wOAAuQ,ySAAoU,mBAAmB,QAAQ,uDAA2D,gGAAwG,EAAE,oGAA4G,EAAE,kGAA0G,EAAE,+FAAuG,EAAE,uEAA+E,EAAE,kFAA0F,EAAE,0FAAkG,EAAE,uFAA+F,iBAAsB,gBAAkB,uBAAyB,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,gEAAoE,6BAAoC,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,o/BAAmsC,qBAAyB,ykDAAkmD,idAAge,OAAS,SAAS,qCAAyC,iCAAmC,WAAa,0CAAkD,uBAiBpqM,CAAC,CAAC,EAAC,4DCsBI,SAASyB,EAAsClB,CAAoB,EACxE,MAAOyE,CAAAA,EAAAA,EAAAA,kBAAAA,CAAkBA,CACvBC,0CAAoC,CACpCA,kNAAyC,CACzC,CACEC,QAAS,KACPC,GACS5E,CADO,CACC2E,OAAO,CAACC,GAAG,CAACC,IAAOC,MAEpCC,IAAIF,CAAY,CAAEC,CAAa,CAAEE,CAAsB,EAGvD,EACAC,OAAOJ,CAAY,CAAEG,CAAsB,EAG3C,CACF,CACF,EAEJ", "sources": ["webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/./src/app/api/external/v1/configs/[configId]/keys/route.ts", "webpack://_N_E/./src/app/api/external/v1/configs/[configId]/keys/route.ts?ba99", "webpack://_N_E/?eb37", "webpack://_N_E/./src/lib/supabase/server.ts"], "sourcesContent": ["module.exports = require(\"node:buffer\");", "module.exports = require(\"node:async_hooks\");", "import { type NextRequest, NextResponse } from 'next/server';\nimport { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';\nimport { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';\nimport { encrypt } from '@/lib/encryption';\nimport { z } from 'zod';\n\n// Use Edge Runtime for better performance\nexport const runtime = 'edge';\n\nconst authMiddleware = new ApiKeyAuthMiddleware();\n\ninterface RouteParams {\n  params: Promise<{\n    configId: string;\n  }>;\n}\n\n// Validation schema for creating provider keys\nconst CreateProviderKeySchema = z.object({\n  provider: z.enum(['openai', 'anthropic', 'google', 'cohere', 'mistral', 'perplexity', 'groq', 'together', 'fireworks', 'deepseek']),\n  api_key: z.string().min(1),\n  model_id: z.string().uuid().optional(),\n  label: z.string().min(1).max(100),\n  temperature: z.number().min(0).max(2).optional().default(0.7),\n  is_default_general_chat_model: z.boolean().optional().default(false)\n});\n\nconst UpdateProviderKeySchema = z.object({\n  api_key: z.string().min(1).optional(),\n  label: z.string().min(1).max(100).optional(),\n  temperature: z.number().min(0).max(2).optional(),\n  is_default_general_chat_model: z.boolean().optional(),\n  status: z.enum(['active', 'inactive']).optional()\n});\n\n// GET /api/external/v1/configs/{configId}/keys - List provider keys for configuration\nexport async function GET(request: NextRequest, { params }: RouteParams) {\n  try {\n    // 1. Authenticate using user-generated API key\n    const authResult = await authMiddleware.authenticateRequest(request);\n    \n    if (!authResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: authResult.error,\n            type: 'authentication_error',\n            code: 'invalid_api_key'\n          }\n        },\n        { status: authResult.statusCode || 401 }\n      );\n    }\n\n    const { userApiKey, userConfig, ipAddress } = authResult;\n    const { configId } = await params;\n\n    // 2. Verify config belongs to user\n    const supabase = createSupabaseServerClientFromRequest(request);\n    \n    const { data: config, error: configError } = await supabase\n      .from('custom_api_configs')\n      .select('id')\n      .eq('id', configId)\n      .eq('user_id', userConfig!.user_id)\n      .single();\n\n    if (configError || !config) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Configuration not found',\n            type: 'not_found_error',\n            code: 'config_not_found'\n          }\n        },\n        { status: 404 }\n      );\n    }\n\n    // 3. Get provider keys (without decrypted API keys for security)\n    const { data: keys, error } = await supabase\n      .from('api_keys')\n      .select(`\n        id,\n        provider,\n        label,\n        status,\n        is_default_general_chat_model,\n        temperature,\n        created_at,\n        updated_at,\n        last_used_at,\n        predefined_models(\n          id,\n          name,\n          display_name,\n          provider_id\n        )\n      `)\n      .eq('custom_api_config_id', configId)\n      .eq('user_id', userConfig!.user_id)\n      .order('created_at', { ascending: false });\n\n    if (error) {\n      console.error('Error fetching provider keys:', error);\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Failed to fetch provider keys',\n            type: 'server_error',\n            code: 'database_error'\n          }\n        },\n        { status: 500 }\n      );\n    }\n\n    // 4. Log API usage\n    authMiddleware.logApiUsage(\n      userApiKey!,\n      request,\n      {\n        statusCode: 200,\n        modelUsed: 'key_management',\n        providerUsed: 'rokey_api',\n      },\n      ipAddress\n    ).catch(console.error);\n\n    return NextResponse.json({\n      object: 'list',\n      data: keys || [],\n      has_more: false\n    }, {\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n      }\n    });\n\n  } catch (error) {\n    console.error('Error in provider keys GET API:', error);\n    return NextResponse.json(\n      {\n        error: {\n          message: 'Internal server error',\n          type: 'server_error',\n          code: 'internal_error'\n        }\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// POST /api/external/v1/configs/{configId}/keys - Add provider key to configuration\nexport async function POST(request: NextRequest, { params }: RouteParams) {\n  try {\n    // 1. Authenticate using user-generated API key\n    const authResult = await authMiddleware.authenticateRequest(request);\n    \n    if (!authResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: authResult.error,\n            type: 'authentication_error',\n            code: 'invalid_api_key'\n          }\n        },\n        { status: authResult.statusCode || 401 }\n      );\n    }\n\n    const { userApiKey, userConfig, ipAddress } = authResult;\n    const { configId } = await params;\n\n    // 2. Validate request body\n    const body = await request.json();\n    const validationResult = CreateProviderKeySchema.safeParse(body);\n\n    if (!validationResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Invalid request data',\n            type: 'validation_error',\n            code: 'invalid_parameters',\n            details: validationResult.error.errors\n          }\n        },\n        { status: 400 }\n      );\n    }\n\n    const keyData = validationResult.data;\n\n    // 3. Verify config belongs to user\n    const supabase = createSupabaseServerClientFromRequest(request);\n    \n    const { data: config, error: configError } = await supabase\n      .from('custom_api_configs')\n      .select('id')\n      .eq('id', configId)\n      .eq('user_id', userConfig!.user_id)\n      .single();\n\n    if (configError || !config) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Configuration not found',\n            type: 'not_found_error',\n            code: 'config_not_found'\n          }\n        },\n        { status: 404 }\n      );\n    }\n\n    // 4. Encrypt the API key\n    const encryptedApiKey = await encrypt(keyData.api_key);\n    \n    // 5. Create API key hash for validation\n    const encoder = new TextEncoder();\n    const data = encoder.encode(keyData.api_key);\n    const hashBuffer = await crypto.subtle.digest('SHA-256', data);\n    const hashArray = Array.from(new Uint8Array(hashBuffer));\n    const apiKeyHash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');\n\n    // 6. Insert the provider key\n    const { data: newKey, error } = await supabase\n      .from('api_keys')\n      .insert({\n        custom_api_config_id: configId,\n        user_id: userConfig!.user_id,\n        provider: keyData.provider,\n        predefined_model_id: keyData.model_id || null,\n        encrypted_api_key: encryptedApiKey,\n        label: keyData.label,\n        api_key_hash: apiKeyHash,\n        status: 'active',\n        is_default_general_chat_model: keyData.is_default_general_chat_model,\n        temperature: keyData.temperature\n      })\n      .select(`\n        id,\n        provider,\n        label,\n        status,\n        is_default_general_chat_model,\n        temperature,\n        created_at,\n        updated_at,\n        predefined_models(\n          id,\n          name,\n          display_name,\n          provider_id\n        )\n      `)\n      .single();\n\n    if (error) {\n      console.error('Error creating provider key:', error);\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Failed to create provider key',\n            type: 'server_error',\n            code: 'database_error'\n          }\n        },\n        { status: 500 }\n      );\n    }\n\n    // 7. Log API usage\n    authMiddleware.logApiUsage(\n      userApiKey!,\n      request,\n      {\n        statusCode: 201,\n        modelUsed: 'key_management',\n        providerUsed: 'rokey_api',\n      },\n      ipAddress\n    ).catch(console.error);\n\n    return NextResponse.json({\n      id: newKey.id,\n      object: 'provider_key',\n      provider: newKey.provider,\n      label: newKey.label,\n      status: newKey.status,\n      is_default_general_chat_model: newKey.is_default_general_chat_model,\n      temperature: newKey.temperature,\n      created_at: newKey.created_at,\n      updated_at: newKey.updated_at,\n      model: newKey.predefined_models\n    }, { \n      status: 201,\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n      }\n    });\n\n  } catch (error) {\n    console.error('Error in provider keys POST API:', error);\n    return NextResponse.json(\n      {\n        error: {\n          message: 'Internal server error',\n          type: 'server_error',\n          code: 'internal_error'\n        }\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// OPTIONS handler for CORS\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n    },\n  });\n}\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\configs\\\\[configId]\\\\keys\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/external/v1/configs/[configId]/keys/route\",\n        pathname: \"/api/external/v1/configs/[configId]/keys\",\n        filename: \"route\",\n        bundlePath: \"app/api/external/v1/configs/[configId]/keys/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\configs\\\\[configId]\\\\keys\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fexternal%2Fv1%2Fconfigs%2F%5BconfigId%5D%2Fkeys%2Froute&page=%2Fapi%2Fexternal%2Fv1%2Fconfigs%2F%5BconfigId%5D%2Fkeys%2Froute&pagePath=private-next-app-dir%2Fapi%2Fexternal%2Fv1%2Fconfigs%2F%5BconfigId%5D%2Fkeys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&appPaths=%2Fapi%2Fexternal%2Fv1%2Fconfigs%2F%5BconfigId%5D%2Fkeys%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/external/v1/configs/[configId]/keys/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":true},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.mjs\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\",\"image/avif\"],\"dangerouslyAllowSVG\":true,\"contentSecurityPolicy\":\"default-src 'self'; script-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"raw.githubusercontent.com\",\"port\":\"\",\"pathname\":\"/lobehub/lobe-icons/**\"},{\"protocol\":\"https\",\"hostname\":\"registry.npmmirror.com\",\"port\":\"\",\"pathname\":\"/@lobehub/icons-static-png/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@latest/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@v11/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"images.unsplash.com\",\"port\":\"\",\"pathname\":\"/**\"},{\"protocol\":\"https\",\"hostname\":\"cloud.gmelius.com\",\"port\":\"\",\"pathname\":\"/public/logos/**\"},{\"protocol\":\"https\",\"hostname\":\"upload.wikimedia.org\",\"port\":\"\",\"pathname\":\"/wikipedia/commons/**\"},{\"protocol\":\"https\",\"hostname\":\"kstatic.googleusercontent.com\",\"port\":\"\",\"pathname\":\"/files/**\"}],\"unoptimized\":false},\"devIndicators\":{\"position\":\"bottom-left\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"C:\\\\RoKey App\\\\rokey-app\",\"experimental\":{\"nodeMiddleware\":false,\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"dynamicOnHover\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":3,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":true,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":true,\"largePageDataBytes\":128000,\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"viewTransition\":false,\"routerBFCache\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"useCache\":false,\"optimizePackageImports\":[\"@heroicons/react\",\"@headlessui/react\",\"react-markdown\",\"react-syntax-highlighter\",\"@supabase/supabase-js\",\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"htmlLimitedBots\":\"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti\",\"bundlePagesRouterDependencies\":false,\"configFile\":\"C:\\\\RoKey App\\\\rokey-app\\\\next.config.mjs\",\"configFileName\":\"next.config.mjs\",\"serverExternalPackages\":[\"pdf-parse\",\"mammoth\"],\"turbopack\":{\"rules\":{\"*.svg\":{\"loaders\":[\"@svgr/webpack\"],\"as\":\"*.js\"}},\"root\":\"C:\\\\RoKey App\\\\rokey-app\"},\"compiler\":{\"removeConsole\":true,\"reactRemoveProperties\":true},\"_originalRedirects\":[]}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/external/v1/configs/[configId]/keys/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/external/v1/configs/[configId]/keys/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "import { createServerClient, type CookieOptions } from '@supabase/ssr';\r\nimport { createClient } from '@supabase/supabase-js';\r\nimport { cookies } from 'next/headers';\r\nimport { NextRequest } from 'next/server';\r\n\r\n// This is the standard setup for creating a Supabase server client\r\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\r\n// Updated for Next.js 15 async cookies requirement\r\nexport async function createSupabaseServerClientOnRequest() {\r\n  const cookieStore = await cookies();\r\n\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        get(name: string) {\r\n          return cookieStore.get(name)?.value;\r\n        },\r\n        set(name: string, value: string, options: CookieOptions) {\r\n          try {\r\n            cookieStore.set({ name, value, ...options });\r\n          } catch (error) {\r\n            // This error can be ignored if running in a Server Component\r\n            // where cookies can't be set directly. Cookie setting should be\r\n            // handled in Server Actions or Route Handlers.\r\n            console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\r\n          }\r\n        },\r\n        remove(name: string, options: CookieOptions) {\r\n          try {\r\n            // To remove a cookie using the `set` method from `next/headers`,\r\n            // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\r\n            cookieStore.set({ name, value: '', ...options });\r\n          } catch (error) {\r\n            // Similar to set, this might fail in a Server Component.\r\n            console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\r\n          }\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n\r\n// Alternative method for API routes that need to handle cookies from request\r\nexport function createSupabaseServerClientFromRequest(request: NextRequest) {\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        get(name: string) {\r\n          return request.cookies.get(name)?.value;\r\n        },\r\n        set(name: string, value: string, options: CookieOptions) {\r\n          // In API routes, we can't set cookies directly on the request\r\n          // This will be handled by the response\r\n        },\r\n        remove(name: string, options: CookieOptions) {\r\n          // In API routes, we can't remove cookies directly on the request\r\n          // This will be handled by the response\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n\r\n// Service role client for admin operations (OAuth token storage, etc.)\r\nexport function createServiceRoleClient() {\r\n  return createClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.SUPABASE_SERVICE_ROLE_KEY!,\r\n    {\r\n      auth: {\r\n        autoRefreshToken: false,\r\n        persistSession: false\r\n      }\r\n    }\r\n  );\r\n}\r\n"], "names": ["runtime", "ApiKeyAuthMiddleware", "CreateProviderKeySchema", "z", "authMiddleware", "provider", "api_key", "min", "model_id", "uuid", "optional", "label", "max", "temperature", "default", "is_default_general_chat_model", "GET", "request", "params", "authResult", "authenticateRequest", "success", "NextResponse", "json", "error", "message", "type", "code", "status", "statusCode", "userApiKey", "userConfig", "ip<PERSON><PERSON><PERSON>", "configId", "supabase", "createSupabaseServerClientFromRequest", "data", "config", "config<PERSON><PERSON>r", "from", "select", "eq", "user_id", "single", "keys", "order", "ascending", "logApiUsage", "modelUsed", "providerUsed", "catch", "console", "object", "has_more", "headers", "POST", "body", "validationResult", "safeParse", "details", "errors", "keyData", "encryptedApiKey", "encrypt", "TextEncoder", "encode", "hash<PERSON><PERSON><PERSON>", "crypto", "subtle", "digest", "apiKeyHash", "Array", "Uint8Array", "map", "b", "toString", "padStart", "join", "new<PERSON>ey", "insert", "custom_api_config_id", "predefined_model_id", "encrypted_api_key", "api_key_hash", "id", "created_at", "updated_at", "model", "predefined_models", "OPTIONS", "createServerClient", "process", "cookies", "get", "name", "value", "set", "options", "remove"], "sourceRoot": "", "ignoreList": []}