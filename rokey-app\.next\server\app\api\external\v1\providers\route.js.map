{"version": 3, "file": "app/api/external/v1/providers/route.js", "mappings": "qFAAA,wDCAA,wHGAA,gRFIO,IAAMA,EAAU,OAAO,EAEP,IAAIC,EAAAA,CAAoBA,CAGzCC,EAAY,CAChB,CACEC,GAAI,GALYC,MAMhBC,KAAM,SACNC,YAAa,8CACbC,QAAS,qBACTC,aAAc,4BACdC,mBAAoB,CAAC,OAAQ,cAAe,aAAc,SAAU,QAAS,SAAS,CACtFC,oBAAqB,eACrBC,YAAa,CACXC,oBAAqB,KACrBC,kBAAmB,GACrB,CACF,EACA,CACEV,GAAI,YACJE,KAAM,YACNC,YAAa,uCACbC,QAAS,wBACTC,aAAc,+BACdC,mBAAoB,CAAC,OAAQ,cAAe,SAAS,CACrDC,oBAAqB,UACrBC,YAAa,CACXC,oBAAqB,IACrBC,kBAAmB,GACrB,CACF,EACA,CACEV,GAAI,SACJE,KAAM,YACNC,YAAa,yCACbC,QAAS,wBACTC,aAAc,mDACdC,mBAAoB,CAAC,OAAQ,cAAe,SAAU,aAAa,CACnEC,oBAAqB,UACrBC,YAAa,CACXC,oBAAqB,KACrBC,kBAAmB,IACrB,CACF,EACA,CACEV,GAAI,SACJE,KAAM,SACNC,YAAa,qCACbC,QAAS,oBACTC,aAAc,2BACdC,mBAAoB,CAAC,OAAQ,cAAe,aAAc,SAAS,CACnEC,oBAAqB,eACrBC,YAAa,CACXC,oBAAqB,IACrBC,kBAAmB,GACrB,CACF,EACA,CACEV,GAAI,UACJE,KAAM,aACNC,YAAa,qCACbC,QAAS,qBACTC,aAAc,4BACdC,mBAAoB,CAAC,OAAQ,cAAe,aAAa,CACzDC,oBAAqB,eACrBC,YAAa,CACXC,oBAAqB,IACrBC,kBAAmB,GACrB,CACF,EACA,CACEV,GAAI,aACJE,KAAM,gBACNC,YAAa,mCACbC,QAAS,wBACTC,aAAc,4BACdC,mBAAoB,CAAC,OAAQ,cAAe,SAAS,CACrDC,oBAAqB,eACrBC,YAAa,CACXC,oBAAqB,IACrBC,kBAAmB,GACrB,CACF,EACA,CACEV,GAAI,OACJE,KAAM,OACNC,YAAa,2CACbC,QAAS,mBACTC,aAAc,iCACdC,mBAAoB,CAAC,OAAQ,cAAc,CAC3CC,oBAAqB,eACrBC,YAAa,CACXC,oBAAqB,GACrBC,kBAAmB,GACrB,CACF,EACA,CACEV,GAAI,WACJE,KAAM,cACNC,YAAa,yCACbC,QAAS,sBACTC,aAAc,8BACdC,mBAAoB,CAAC,OAAQ,cAAe,aAAa,CACzDC,oBAAqB,eACrBC,YAAa,CACXC,oBAAqB,IACrBC,kBAAmB,GACrB,CACF,EACA,CACEV,GAAI,YACJE,KAAM,eACNC,YAAa,wCACbC,QAAS,uBACTC,aAAc,wCACdC,mBAAoB,CAAC,OAAQ,cAAe,aAAa,CACzDC,oBAAqB,eACrBC,YAAa,CACXC,oBAAqB,IACrBC,kBAAmB,GACrB,CACF,EACA,CACEV,GAAI,WACJE,KAAM,WACNC,YAAa,uCACbC,QAAS,uBACTC,aAAc,8BACdC,mBAAoB,CAAC,OAAQ,cAAc,CAC3CC,oBAAqB,eACrBC,YAAa,CACXC,oBAAqB,IACrBC,kBAAmB,GACrB,CACF,EACD,CAGM,eAAeC,EAAIC,CAAoB,EAC5C,GAAI,CAEF,IAAMC,EAAa,MAAMZ,EAAea,kBAADb,CAAoB,CAACW,GAE5D,GAAI,CAACC,EAAWE,OAAO,CACrB,CADuB,MAChBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAASN,EAAWK,KAAK,CACzBE,KAAM,uBACNC,KAAM,iBACR,CACF,EACA,CAAEC,OAAQT,EAAWU,UAAU,EAAI,GAAI,GAI3C,GAAM,YAAEC,CAAU,YAAEC,CAAU,WAAEC,CAAS,CAAE,CAAGb,EAGxC,cAAEc,CAAY,CAAE,CAAG,IAAIC,IAAIhB,EAAQiB,GAAG,EACtCC,EAAUH,EAAaI,GAAG,CAAC,WAC3BC,CADuC,CAC5BL,EAAaI,GAAG,CAAC,aAG9BE,CAH4C,CAGxBlC,EA0BxB,MA9B2E,CAMvE+B,IACFG,EAAoBA,EAAkBC,CAD3B,KACiC,CAACC,GAC3CA,EAAS7B,EAPmE,gBAOjD,CAAC8B,QAAQ,CAACN,GAAAA,EAIrCE,IACFC,EAAoBA,EAAkBC,EAD1B,IACgC,CAACC,GAC3CA,EAAS5B,mBAAmB,GAAKyB,EAAAA,EAKrC/B,EAAeoC,WAAW,CACxBb,EACAZ,EACA,CACEW,CAJUtB,UAIE,IACZqC,UAAW,mBACXC,aAAc,WAChB,EACAb,GACAc,KAAK,CAACC,QAAQvB,KAAK,EAEdF,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvByB,OAAQ,OACRC,KAAMV,EACNW,UAAU,EACVC,YAAaZ,EAAkBa,MAAM,EACpC,CACDC,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,eAChC,+BAAgC,yCAChC,2BAA4BhD,EAAU+C,MAAM,CAACE,QAAQ,GACrD,0BAA2Bf,EAAkBa,MAAM,CAACE,QAAQ,EAC9D,CACF,EAEF,CAAE,MAAO9B,EAAO,CAEd,OAAOF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,wBACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,EAElB,CACF,CAGO,eAAe2B,IACpB,OAAO,IAAIjC,EAAAA,EAAYA,CAAC,KAAM,CAC5BM,OAAQ,IACRyB,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,eAChC,+BAAgC,wCAClC,CACF,EACF,CCpOA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,wCACA,sCACA,iBACA,gDACA,CAAK,CACL,8FACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,iFACA,EAFA,4BAEA,2BACA,OACI,QAA8B,EAClC,wCACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA0B,aAAe,kDAAyD,wOAAuQ,ySAAoU,mBAAmB,QAAQ,uDAA2D,gGAAwG,EAAE,oGAA4G,EAAE,kGAA0G,EAAE,+FAAuG,EAAE,uEAA+E,EAAE,kFAA0F,EAAE,0FAAkG,EAAE,uFAA+F,iBAAsB,gBAAkB,uBAAyB,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,gEAAoE,6BAAoC,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,o/BAAmsC,qBAAyB,ykDAAkmD,idAAge,OAAS,SAAS,qCAAyC,iCAAmC,WAAa,0CAAkD,MAAQ,YAAc,iBAAmB,sBAAwB,uBAiBruM,CAAC,CAAC,EAAC", "sources": ["webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/./src/app/api/external/v1/providers/route.ts", "webpack://_N_E/./src/app/api/external/v1/providers/route.ts?09cc", "webpack://_N_E/?8e8c"], "sourcesContent": ["module.exports = require(\"node:buffer\");", "module.exports = require(\"node:async_hooks\");", "import { type NextRequest, NextResponse } from 'next/server';\nimport { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';\n\n// Use Edge Runtime for better performance\nexport const runtime = 'edge';\n\nconst authMiddleware = new ApiKeyAuthMiddleware();\n\n// Static provider information\nconst PROVIDERS = [\n  {\n    id: 'openai',\n    name: 'OpenAI',\n    description: 'Leading AI research company with GPT models',\n    website: 'https://openai.com',\n    api_base_url: 'https://api.openai.com/v1',\n    supported_features: ['chat', 'completions', 'embeddings', 'images', 'audio', 'vision'],\n    authentication_type: 'bearer_token',\n    rate_limits: {\n      requests_per_minute: 3500,\n      tokens_per_minute: 90000\n    }\n  },\n  {\n    id: 'anthropic',\n    name: 'Anthropic',\n    description: 'AI safety company with Claude models',\n    website: 'https://anthropic.com',\n    api_base_url: 'https://api.anthropic.com/v1',\n    supported_features: ['chat', 'completions', 'vision'],\n    authentication_type: 'api_key',\n    rate_limits: {\n      requests_per_minute: 1000,\n      tokens_per_minute: 40000\n    }\n  },\n  {\n    id: 'google',\n    name: 'Google AI',\n    description: 'Google\\'s Gemini models and AI services',\n    website: 'https://ai.google.dev',\n    api_base_url: 'https://generativelanguage.googleapis.com/v1beta',\n    supported_features: ['chat', 'completions', 'vision', 'embeddings'],\n    authentication_type: 'api_key',\n    rate_limits: {\n      requests_per_minute: 1500,\n      tokens_per_minute: 32000\n    }\n  },\n  {\n    id: 'cohere',\n    name: 'Cohere',\n    description: 'Enterprise-focused language models',\n    website: 'https://cohere.ai',\n    api_base_url: 'https://api.cohere.ai/v1',\n    supported_features: ['chat', 'completions', 'embeddings', 'rerank'],\n    authentication_type: 'bearer_token',\n    rate_limits: {\n      requests_per_minute: 1000,\n      tokens_per_minute: 40000\n    }\n  },\n  {\n    id: 'mistral',\n    name: 'Mistral AI',\n    description: 'Open and efficient language models',\n    website: 'https://mistral.ai',\n    api_base_url: 'https://api.mistral.ai/v1',\n    supported_features: ['chat', 'completions', 'embeddings'],\n    authentication_type: 'bearer_token',\n    rate_limits: {\n      requests_per_minute: 1000,\n      tokens_per_minute: 30000\n    }\n  },\n  {\n    id: 'perplexity',\n    name: 'Perplexity AI',\n    description: 'Search-augmented language models',\n    website: 'https://perplexity.ai',\n    api_base_url: 'https://api.perplexity.ai',\n    supported_features: ['chat', 'completions', 'search'],\n    authentication_type: 'bearer_token',\n    rate_limits: {\n      requests_per_minute: 500,\n      tokens_per_minute: 20000\n    }\n  },\n  {\n    id: 'groq',\n    name: 'Groq',\n    description: 'Ultra-fast inference for language models',\n    website: 'https://groq.com',\n    api_base_url: 'https://api.groq.com/openai/v1',\n    supported_features: ['chat', 'completions'],\n    authentication_type: 'bearer_token',\n    rate_limits: {\n      requests_per_minute: 30,\n      tokens_per_minute: 6000\n    }\n  },\n  {\n    id: 'together',\n    name: 'Together AI',\n    description: 'Open source models with fast inference',\n    website: 'https://together.ai',\n    api_base_url: 'https://api.together.xyz/v1',\n    supported_features: ['chat', 'completions', 'embeddings'],\n    authentication_type: 'bearer_token',\n    rate_limits: {\n      requests_per_minute: 600,\n      tokens_per_minute: 30000\n    }\n  },\n  {\n    id: 'fireworks',\n    name: 'Fireworks AI',\n    description: 'Fast inference for open source models',\n    website: 'https://fireworks.ai',\n    api_base_url: 'https://api.fireworks.ai/inference/v1',\n    supported_features: ['chat', 'completions', 'embeddings'],\n    authentication_type: 'bearer_token',\n    rate_limits: {\n      requests_per_minute: 600,\n      tokens_per_minute: 40000\n    }\n  },\n  {\n    id: 'deepseek',\n    name: 'DeepSeek',\n    description: 'Advanced reasoning and coding models',\n    website: 'https://deepseek.com',\n    api_base_url: 'https://api.deepseek.com/v1',\n    supported_features: ['chat', 'completions'],\n    authentication_type: 'bearer_token',\n    rate_limits: {\n      requests_per_minute: 500,\n      tokens_per_minute: 30000\n    }\n  }\n];\n\n// GET /api/external/v1/providers - List all supported providers\nexport async function GET(request: NextRequest) {\n  try {\n    // 1. Authenticate using user-generated API key\n    const authResult = await authMiddleware.authenticateRequest(request);\n    \n    if (!authResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: authResult.error,\n            type: 'authentication_error',\n            code: 'invalid_api_key'\n          }\n        },\n        { status: authResult.statusCode || 401 }\n      );\n    }\n\n    const { userApiKey, userConfig, ipAddress } = authResult;\n\n    // 2. Get query parameters for filtering\n    const { searchParams } = new URL(request.url);\n    const feature = searchParams.get('feature'); // Filter by supported feature\n    const authType = searchParams.get('auth_type'); // Filter by authentication type\n\n    // 3. Filter providers based on query parameters\n    let filteredProviders = PROVIDERS;\n\n    if (feature) {\n      filteredProviders = filteredProviders.filter(provider => \n        provider.supported_features.includes(feature)\n      );\n    }\n\n    if (authType) {\n      filteredProviders = filteredProviders.filter(provider => \n        provider.authentication_type === authType\n      );\n    }\n\n    // 4. Log API usage\n    authMiddleware.logApiUsage(\n      userApiKey!,\n      request,\n      {\n        statusCode: 200,\n        modelUsed: 'provider_listing',\n        providerUsed: 'rokey_api',\n      },\n      ipAddress\n    ).catch(console.error);\n\n    return NextResponse.json({\n      object: 'list',\n      data: filteredProviders,\n      has_more: false,\n      total_count: filteredProviders.length\n    }, {\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n        'X-RouKey-Total-Providers': PROVIDERS.length.toString(),\n        'X-RouKey-Filtered-Count': filteredProviders.length.toString(),\n      }\n    });\n\n  } catch (error) {\n    console.error('Error in providers GET API:', error);\n    return NextResponse.json(\n      {\n        error: {\n          message: 'Internal server error',\n          type: 'server_error',\n          code: 'internal_error'\n        }\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// OPTIONS handler for CORS\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n    },\n  });\n}\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\providers\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/external/v1/providers/route\",\n        pathname: \"/api/external/v1/providers\",\n        filename: \"route\",\n        bundlePath: \"app/api/external/v1/providers/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\providers\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fexternal%2Fv1%2Fproviders%2Froute&page=%2Fapi%2Fexternal%2Fv1%2Fproviders%2Froute&pagePath=private-next-app-dir%2Fapi%2Fexternal%2Fv1%2Fproviders%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&appPaths=%2Fapi%2Fexternal%2Fv1%2Fproviders%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/external/v1/providers/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":true},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.mjs\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\",\"image/avif\"],\"dangerouslyAllowSVG\":true,\"contentSecurityPolicy\":\"default-src 'self'; script-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"raw.githubusercontent.com\",\"port\":\"\",\"pathname\":\"/lobehub/lobe-icons/**\"},{\"protocol\":\"https\",\"hostname\":\"registry.npmmirror.com\",\"port\":\"\",\"pathname\":\"/@lobehub/icons-static-png/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@latest/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@v11/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"images.unsplash.com\",\"port\":\"\",\"pathname\":\"/**\"},{\"protocol\":\"https\",\"hostname\":\"cloud.gmelius.com\",\"port\":\"\",\"pathname\":\"/public/logos/**\"},{\"protocol\":\"https\",\"hostname\":\"upload.wikimedia.org\",\"port\":\"\",\"pathname\":\"/wikipedia/commons/**\"},{\"protocol\":\"https\",\"hostname\":\"kstatic.googleusercontent.com\",\"port\":\"\",\"pathname\":\"/files/**\"}],\"unoptimized\":false},\"devIndicators\":{\"position\":\"bottom-left\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"C:\\\\RoKey App\\\\rokey-app\",\"experimental\":{\"nodeMiddleware\":false,\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"dynamicOnHover\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":3,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":true,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":true,\"largePageDataBytes\":128000,\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"viewTransition\":false,\"routerBFCache\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"useCache\":false,\"optimizePackageImports\":[\"@heroicons/react\",\"@headlessui/react\",\"react-markdown\",\"react-syntax-highlighter\",\"@supabase/supabase-js\",\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"htmlLimitedBots\":\"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti\",\"bundlePagesRouterDependencies\":false,\"configFile\":\"C:\\\\RoKey App\\\\rokey-app\\\\next.config.mjs\",\"configFileName\":\"next.config.mjs\",\"serverExternalPackages\":[\"pdf-parse\",\"mammoth\"],\"turbopack\":{\"rules\":{\"*.svg\":{\"loaders\":[\"@svgr/webpack\"],\"as\":\"*.js\"}},\"root\":\"C:\\\\RoKey App\\\\rokey-app\"},\"compiler\":{\"removeConsole\":true,\"reactRemoveProperties\":true},\"api\":{\"bodyParser\":{\"sizeLimit\":\"50mb\"},\"responseLimit\":\"50mb\"},\"_originalRedirects\":[]}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/external/v1/providers/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/external/v1/providers/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map"], "names": ["runtime", "ApiKeyAuthMiddleware", "PROVIDERS", "id", "authMiddleware", "name", "description", "website", "api_base_url", "supported_features", "authentication_type", "rate_limits", "requests_per_minute", "tokens_per_minute", "GET", "request", "authResult", "authenticateRequest", "success", "NextResponse", "json", "error", "message", "type", "code", "status", "statusCode", "userApiKey", "userConfig", "ip<PERSON><PERSON><PERSON>", "searchParams", "URL", "url", "feature", "get", "authType", "filteredProviders", "filter", "provider", "includes", "logApiUsage", "modelUsed", "providerUsed", "catch", "console", "object", "data", "has_more", "total_count", "length", "headers", "toString", "OPTIONS"], "sourceRoot": "", "ignoreList": []}