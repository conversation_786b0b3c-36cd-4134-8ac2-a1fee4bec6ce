{"version": 3, "file": "app/api/external/v1/configs/route.js", "mappings": "gJEAA,+SFMO,IAAMA,EAAU,OAEjBC,EAAiB,IAAIC,EAAAA,CAAoBA,CAGzCC,EAAqBC,EAAAA,CAAAA,CAAAA,IAHPH,EAGe,CAAC,CAClCI,KAAMD,EAAAA,CAAAA,CAAAA,MAAQ,GAAGE,GAAG,CAAC,GAAGC,GAAG,CAAC,KAC5BC,YAAaJ,EAAAA,CAAAA,CAAAA,MAAQ,GAAGG,GAAG,CAAC,KAAKE,QAAQ,GACzCC,iBAAkBN,EAAAA,CAAAA,CAAAA,IAAM,CAAC,CAAC,iBAAkB,eAAgB,aAAa,EAAEK,QAAQ,GAAGE,OAAO,CAAC,kBAC9FC,SAAUR,EAAAA,CAAAA,CAAAA,MAAQ,CAAC,CACjBS,YAAaT,EAAAA,CAAAA,CAAAA,MAAQ,GAAGE,GAAG,CAAC,GAAGC,GAAG,CAAC,GAAGE,QAAQ,GAC9CK,WAAYV,EAAAA,CAAAA,CAAAA,MAAQ,GAAGW,GAAG,GAAGC,QAAQ,GAAGP,QAAQ,GAChDQ,MAAOb,EAAAA,CAAAA,CAAAA,MAAQ,GAAGE,GAAG,CAAC,GAAGC,GAAG,CAAC,GAAGE,QAAQ,GACxCS,kBAAmBd,EAAAA,CAAAA,CAAAA,MAAQ,GAAGE,GAAG,CAAC,CAAC,GAAGC,GAAG,CAAC,GAAGE,QAAQ,GACrDU,iBAAkBf,EAAAA,CAAAA,CAAAA,MAAQ,GAAGE,GAAG,CAAC,CAAC,GAAGC,GAAG,CAAC,GAAGE,QAAQ,EACtD,GAAGA,QAAQ,GAAGE,OAAO,CAAC,CAAC,EACzB,GAKO,eAAeS,EAAIC,CAAoB,EAC5C,GAAI,CAEF,IAAMC,EAAa,MAAMrB,EAAesB,kBAADtB,CAAoB,CAACoB,GAE5D,GAAI,CAACC,EAAWE,OAAO,CACrB,CADuB,MAChBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAASN,EAAWK,KAAK,CACzBE,KAAM,uBACNC,KAAM,iBACR,CACF,EACA,CAAEC,OAAQT,EAAWU,UAAU,EAAI,GAAI,GAI3C,GAAM,CAAEC,YAAU,CAAEC,YAAU,WAAEC,CAAS,CAAE,CAAGb,EAGxCc,EAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAqCA,CAAChB,GAEjD,CAAEiB,KAAMC,CAAO,OAAEZ,CAAK,CAAE,CAAG,MAAMS,EACpCI,IAAI,CAAC,sBACLC,MAAM,CAAC,CAAC;;;;;;;;MAQT,CAAC,EACAC,EAAE,CAAC,UAAWR,EAAYS,OAAO,EACjCC,KAAK,CAAC,aAAc,CAAEC,WAAW,CAAM,GAE1C,GAAIlB,EAEF,KAFS,EAEFF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,iCACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,GAgBlB,OAXA9B,EAAe6C,WAAW,CACxBb,EACAZ,EACA,CACEW,CAJU/B,UAIE,IACZ8C,UAAW,oBACXC,aAAc,WAChB,EACAb,GACAc,KAAK,CAACC,QAAQvB,KAAK,EAEdF,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvByB,OAAQ,OACRb,KAAMC,GAAW,EAAE,CACnBa,UAAU,CACZ,EAAG,CACDC,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,kCAChC,+BAAgC,wCAClC,CACF,EAEF,CAAE,MAAO1B,EAAO,CAEd,OAAOF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,wBACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,EAElB,CACF,CAGO,eAAeuB,EAAKjC,CAAoB,EAC7C,GAAI,CAEF,IAAMC,EAAa,MAAMrB,EAAesB,kBAADtB,CAAoB,CAACoB,GAE5D,GAAI,CAACC,EAAWE,OAAO,CACrB,CADuB,MAChBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAASN,EAAWK,KAAK,CACzBE,KAAM,uBACNC,KAAM,iBACR,CACF,EACA,CAAEC,OAAQT,EAAWU,UAAU,EAAI,GAAI,GAI3C,GAAM,CAAEC,YAAU,YAAEC,CAAU,WAAEC,CAAS,CAAE,CAAGb,EAGxCiC,EAAO,MAAMlC,EAAQK,IAAI,GACzB8B,EAAmBrD,EAAmBsD,SAAS,CAACF,GAEtD,GAAI,CAACC,EAAiBhC,OAAO,CAC3B,CAD6B,MACtBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,uBACTC,KAAM,mBACNC,KAAM,qBACN4B,QAASF,EAAiB7B,KAAK,CAACgC,MAAM,CAE1C,EACA,CAAE5B,OAAQ,GAAI,GAIlB,IAAM6B,EAAaJ,EAAiBlB,IAAI,CAGlCF,EAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAqCA,CAAChB,GAEjD,CAAEiB,KAAMuB,CAAS,OAAElC,CAAK,CAAE,CAAG,MAAMS,EACtCI,IAAI,CAAC,sBACLsB,MAAM,CAAC,CACNnB,QAAST,EAAYS,OAAO,CAC5BtC,KAAMuD,EAAWvD,IAAI,CACrBG,YAAaoD,EAAWpD,WAAW,EAAI,GACvCE,iBAAkBkD,EAAWlD,gBAAgB,CAC7CE,SAAUgD,EAAWhD,QAAQ,GAE9B6B,MAAM,GACNsB,MAAM,GAET,GAAIpC,EAEF,KAFS,EAEFF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,iCACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,GAgBlB,OAXA9B,EAAe6C,WAAW,CACxBb,EACAZ,EACA,CACEW,CAJU/B,UAIE,IACZ8C,UAAW,oBACXC,aAAc,WAChB,EACAb,GACAc,KAAK,CAACC,QAAQvB,KAAK,EAEdF,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvBsC,GAAIH,EAAUG,EAAE,CAChBb,OAAQ,SACR9C,KAAMwD,EAAUxD,IAAI,CACpBG,YAAaqD,EAAUrD,WAAW,CAClCE,iBAAkBmD,EAAUnD,gBAAgB,CAC5CE,SAAUiD,EAAUjD,QAAQ,CAC5BqD,WAAYJ,EAAUI,UAAU,CAChCC,WAAYL,EAAUK,UAAU,EAC/B,CACDnC,OAAQ,IACRsB,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,kCAChC,+BAAgC,wCAClC,CACF,EAEF,CAAE,MAAO1B,EAAO,CAEd,OAAOF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,wBACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,EAElB,CACF,CAGO,eAAeoC,IACpB,OAAO,IAAI1C,EAAAA,EAAYA,CAAC,KAAM,CAC5BM,OAAQ,IACRsB,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,kCAChC,+BAAgC,wCAClC,CACF,EACF,CA1N2BlD,EAAmBiE,OAAO,GCjBrD,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,sCACA,oCACA,iBACA,8CACA,CAAK,CACL,4FACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,+EACA,EAFA,4BAEA,2BACA,OACI,QAA8B,EAClC,sCACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA0B,aAAe,kDAAyD,wOAAuQ,ySAAoU,mBAAmB,QAAQ,uDAA2D,gGAAwG,EAAE,oGAA4G,EAAE,kGAA0G,EAAE,+FAAuG,EAAE,uEAA+E,EAAE,kFAA0F,EAAE,0FAAkG,EAAE,uFAA+F,iBAAsB,gBAAkB,uBAAyB,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,gEAAoE,6BAAoC,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,o/BAAmsC,qBAAyB,ykDAAkmD,idAAge,OAAS,SAAS,qCAAyC,iCAAmC,WAAa,0CAAkD,uBAiBpqM,CAAC,CAAC,EAAC,sBCvBH,wDCAA,mGC6CO,SAAS/B,EAAsChB,CAAoB,EACxE,MAAOgD,CAAAA,EAAAA,EAAAA,kBAAAA,CAAkBA,CACvBC,0CAAoC,CACpCA,kNAAyC,CACzC,CACEC,QAAS,KACPC,GACSnD,CADO,CACCkD,OAAO,CAACC,GAAG,CAACnE,IAAOoE,MAEpCC,IAAIrE,CAAY,CAAEoE,CAAa,CAAEE,CAAsB,EAGvD,EACAC,OAAOvE,CAAY,CAAEsE,CAAsB,EAG3C,CACF,CACF,EAEJ", "sources": ["webpack://_N_E/./src/app/api/external/v1/configs/route.ts", "webpack://_N_E/./src/app/api/external/v1/configs/route.ts?4ba1", "webpack://_N_E/?2e87", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/./src/lib/supabase/server.ts"], "sourcesContent": ["import { type NextRequest, NextResponse } from 'next/server';\nimport { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';\nimport { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';\nimport { z } from 'zod';\n\n// Use Edge Runtime for better performance\nexport const runtime = 'edge';\n\nconst authMiddleware = new ApiKeyAuthMiddleware();\n\n// Validation schemas\nconst CreateConfigSchema = z.object({\n  name: z.string().min(1).max(100),\n  description: z.string().max(500).optional(),\n  routing_strategy: z.enum(['load_balancing', 'role_routing', 'agent_mode']).optional().default('load_balancing'),\n  settings: z.object({\n    temperature: z.number().min(0).max(2).optional(),\n    max_tokens: z.number().int().positive().optional(),\n    top_p: z.number().min(0).max(1).optional(),\n    frequency_penalty: z.number().min(-2).max(2).optional(),\n    presence_penalty: z.number().min(-2).max(2).optional(),\n  }).optional().default({})\n});\n\nconst UpdateConfigSchema = CreateConfigSchema.partial();\n\n// GET /api/external/v1/configs - List all configurations\nexport async function GET(request: NextRequest) {\n  try {\n    // 1. Authenticate using user-generated API key\n    const authResult = await authMiddleware.authenticateRequest(request);\n    \n    if (!authResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: authResult.error,\n            type: 'authentication_error',\n            code: 'invalid_api_key'\n          }\n        },\n        { status: authResult.statusCode || 401 }\n      );\n    }\n\n    const { userApiKey, userConfig, ipAddress } = authResult;\n\n    // 2. Get user's configurations\n    const supabase = createSupabaseServerClientFromRequest(request);\n    \n    const { data: configs, error } = await supabase\n      .from('custom_api_configs')\n      .select(`\n        id,\n        name,\n        description,\n        routing_strategy,\n        settings,\n        created_at,\n        updated_at\n      `)\n      .eq('user_id', userConfig!.user_id)\n      .order('created_at', { ascending: false });\n\n    if (error) {\n      console.error('Error fetching configs:', error);\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Failed to fetch configurations',\n            type: 'server_error',\n            code: 'database_error'\n          }\n        },\n        { status: 500 }\n      );\n    }\n\n    // 3. Log API usage\n    authMiddleware.logApiUsage(\n      userApiKey!,\n      request,\n      {\n        statusCode: 200,\n        modelUsed: 'config_management',\n        providerUsed: 'rokey_api',\n      },\n      ipAddress\n    ).catch(console.error);\n\n    return NextResponse.json({\n      object: 'list',\n      data: configs || [],\n      has_more: false\n    }, {\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n      }\n    });\n\n  } catch (error) {\n    console.error('Error in configs GET API:', error);\n    return NextResponse.json(\n      {\n        error: {\n          message: 'Internal server error',\n          type: 'server_error',\n          code: 'internal_error'\n        }\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// POST /api/external/v1/configs - Create new configuration\nexport async function POST(request: NextRequest) {\n  try {\n    // 1. Authenticate using user-generated API key\n    const authResult = await authMiddleware.authenticateRequest(request);\n    \n    if (!authResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: authResult.error,\n            type: 'authentication_error',\n            code: 'invalid_api_key'\n          }\n        },\n        { status: authResult.statusCode || 401 }\n      );\n    }\n\n    const { userApiKey, userConfig, ipAddress } = authResult;\n\n    // 2. Validate request body\n    const body = await request.json();\n    const validationResult = CreateConfigSchema.safeParse(body);\n\n    if (!validationResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Invalid request data',\n            type: 'validation_error',\n            code: 'invalid_parameters',\n            details: validationResult.error.errors\n          }\n        },\n        { status: 400 }\n      );\n    }\n\n    const configData = validationResult.data;\n\n    // 3. Create configuration\n    const supabase = createSupabaseServerClientFromRequest(request);\n    \n    const { data: newConfig, error } = await supabase\n      .from('custom_api_configs')\n      .insert({\n        user_id: userConfig!.user_id,\n        name: configData.name,\n        description: configData.description || '',\n        routing_strategy: configData.routing_strategy,\n        settings: configData.settings\n      })\n      .select()\n      .single();\n\n    if (error) {\n      console.error('Error creating config:', error);\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Failed to create configuration',\n            type: 'server_error',\n            code: 'database_error'\n          }\n        },\n        { status: 500 }\n      );\n    }\n\n    // 4. Log API usage\n    authMiddleware.logApiUsage(\n      userApiKey!,\n      request,\n      {\n        statusCode: 201,\n        modelUsed: 'config_management',\n        providerUsed: 'rokey_api',\n      },\n      ipAddress\n    ).catch(console.error);\n\n    return NextResponse.json({\n      id: newConfig.id,\n      object: 'config',\n      name: newConfig.name,\n      description: newConfig.description,\n      routing_strategy: newConfig.routing_strategy,\n      settings: newConfig.settings,\n      created_at: newConfig.created_at,\n      updated_at: newConfig.updated_at\n    }, { \n      status: 201,\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n      }\n    });\n\n  } catch (error) {\n    console.error('Error in configs POST API:', error);\n    return NextResponse.json(\n      {\n        error: {\n          message: 'Internal server error',\n          type: 'server_error',\n          code: 'internal_error'\n        }\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// OPTIONS handler for CORS\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n    },\n  });\n}\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\configs\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/external/v1/configs/route\",\n        pathname: \"/api/external/v1/configs\",\n        filename: \"route\",\n        bundlePath: \"app/api/external/v1/configs/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\configs\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fexternal%2Fv1%2Fconfigs%2Froute&page=%2Fapi%2Fexternal%2Fv1%2Fconfigs%2Froute&pagePath=private-next-app-dir%2Fapi%2Fexternal%2Fv1%2Fconfigs%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&appPaths=%2Fapi%2Fexternal%2Fv1%2Fconfigs%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/external/v1/configs/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":true},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.mjs\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\",\"image/avif\"],\"dangerouslyAllowSVG\":true,\"contentSecurityPolicy\":\"default-src 'self'; script-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"raw.githubusercontent.com\",\"port\":\"\",\"pathname\":\"/lobehub/lobe-icons/**\"},{\"protocol\":\"https\",\"hostname\":\"registry.npmmirror.com\",\"port\":\"\",\"pathname\":\"/@lobehub/icons-static-png/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@latest/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@v11/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"images.unsplash.com\",\"port\":\"\",\"pathname\":\"/**\"},{\"protocol\":\"https\",\"hostname\":\"cloud.gmelius.com\",\"port\":\"\",\"pathname\":\"/public/logos/**\"},{\"protocol\":\"https\",\"hostname\":\"upload.wikimedia.org\",\"port\":\"\",\"pathname\":\"/wikipedia/commons/**\"},{\"protocol\":\"https\",\"hostname\":\"kstatic.googleusercontent.com\",\"port\":\"\",\"pathname\":\"/files/**\"}],\"unoptimized\":false},\"devIndicators\":{\"position\":\"bottom-left\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"C:\\\\RoKey App\\\\rokey-app\",\"experimental\":{\"nodeMiddleware\":false,\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"dynamicOnHover\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":3,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":true,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":true,\"largePageDataBytes\":128000,\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"viewTransition\":false,\"routerBFCache\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"useCache\":false,\"optimizePackageImports\":[\"@heroicons/react\",\"@headlessui/react\",\"react-markdown\",\"react-syntax-highlighter\",\"@supabase/supabase-js\",\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"htmlLimitedBots\":\"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti\",\"bundlePagesRouterDependencies\":false,\"configFile\":\"C:\\\\RoKey App\\\\rokey-app\\\\next.config.mjs\",\"configFileName\":\"next.config.mjs\",\"serverExternalPackages\":[\"pdf-parse\",\"mammoth\"],\"turbopack\":{\"rules\":{\"*.svg\":{\"loaders\":[\"@svgr/webpack\"],\"as\":\"*.js\"}},\"root\":\"C:\\\\RoKey App\\\\rokey-app\"},\"compiler\":{\"removeConsole\":true,\"reactRemoveProperties\":true},\"_originalRedirects\":[]}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/external/v1/configs/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/external/v1/configs/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "module.exports = require(\"node:buffer\");", "module.exports = require(\"node:async_hooks\");", "import { createServerClient, type CookieOptions } from '@supabase/ssr';\r\nimport { createClient } from '@supabase/supabase-js';\r\nimport { cookies } from 'next/headers';\r\nimport { NextRequest } from 'next/server';\r\n\r\n// This is the standard setup for creating a Supabase server client\r\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\r\n// Updated for Next.js 15 async cookies requirement\r\nexport async function createSupabaseServerClientOnRequest() {\r\n  const cookieStore = await cookies();\r\n\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        get(name: string) {\r\n          return cookieStore.get(name)?.value;\r\n        },\r\n        set(name: string, value: string, options: CookieOptions) {\r\n          try {\r\n            cookieStore.set({ name, value, ...options });\r\n          } catch (error) {\r\n            // This error can be ignored if running in a Server Component\r\n            // where cookies can't be set directly. Cookie setting should be\r\n            // handled in Server Actions or Route Handlers.\r\n            console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\r\n          }\r\n        },\r\n        remove(name: string, options: CookieOptions) {\r\n          try {\r\n            // To remove a cookie using the `set` method from `next/headers`,\r\n            // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\r\n            cookieStore.set({ name, value: '', ...options });\r\n          } catch (error) {\r\n            // Similar to set, this might fail in a Server Component.\r\n            console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\r\n          }\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n\r\n// Alternative method for API routes that need to handle cookies from request\r\nexport function createSupabaseServerClientFromRequest(request: NextRequest) {\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        get(name: string) {\r\n          return request.cookies.get(name)?.value;\r\n        },\r\n        set(name: string, value: string, options: CookieOptions) {\r\n          // In API routes, we can't set cookies directly on the request\r\n          // This will be handled by the response\r\n        },\r\n        remove(name: string, options: CookieOptions) {\r\n          // In API routes, we can't remove cookies directly on the request\r\n          // This will be handled by the response\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n\r\n// Service role client for admin operations (OAuth token storage, etc.)\r\nexport function createServiceRoleClient() {\r\n  return createClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.SUPABASE_SERVICE_ROLE_KEY!,\r\n    {\r\n      auth: {\r\n        autoRefreshToken: false,\r\n        persistSession: false\r\n      }\r\n    }\r\n  );\r\n}\r\n"], "names": ["runtime", "authMiddleware", "ApiKeyAuthMiddleware", "CreateConfigSchema", "z", "name", "min", "max", "description", "optional", "routing_strategy", "default", "settings", "temperature", "max_tokens", "int", "positive", "top_p", "frequency_penalty", "presence_penalty", "GET", "request", "authResult", "authenticateRequest", "success", "NextResponse", "json", "error", "message", "type", "code", "status", "statusCode", "userApiKey", "userConfig", "ip<PERSON><PERSON><PERSON>", "supabase", "createSupabaseServerClientFromRequest", "data", "configs", "from", "select", "eq", "user_id", "order", "ascending", "logApiUsage", "modelUsed", "providerUsed", "catch", "console", "object", "has_more", "headers", "POST", "body", "validationResult", "safeParse", "details", "errors", "configData", "newConfig", "insert", "single", "id", "created_at", "updated_at", "OPTIONS", "partial", "createServerClient", "process", "cookies", "get", "value", "set", "options", "remove"], "sourceRoot": "", "ignoreList": []}