(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[956],{2632:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ComponentMod:()=>T,default:()=>M});var r,a={};i.r(a),i.d(a,{GET:()=>x,OPTIONS:()=>w,PUT:()=>k,runtime:()=>_});var o={};i.r(o),i.d(o,{patchFetch:()=>z,routeModule:()=>C,serverHooks:()=>R,workAsyncStorage:()=>I,workUnitAsyncStorage:()=>S});var s=i(8429),n=i(9874),c=i(8294),l=i(6567),p=i(4144),u=i(5421),m=i(974),d=i(4429),g=i(9975),f=i(1109);let _="edge",h=new d.S,y=f.z.object({role_name:f.z.string().min(1),api_key_id:f.z.string().uuid(),priority:f.z.number().int().min(1).optional().default(1)}),b=f.z.object({agent_number:f.z.number().int().min(1).max(5),api_key_id:f.z.string().uuid(),agent_prompt:f.z.string().optional()}),v=f.z.object({routing_strategy:f.z.enum(["load_balancing","role_routing","agent_mode"]),settings:f.z.object({load_balancing_method:f.z.enum(["round_robin","random","least_used"]).optional(),role_assignments:f.z.array(y).optional(),enable_multi_role_orchestration:f.z.boolean().optional(),synthesis_model:f.z.string().optional(),agent_assignments:f.z.array(b).optional(),complexity_threshold:f.z.number().int().min(1).max(5).optional(),max_debate_rounds:f.z.number().int().min(1).max(10).optional().default(3),consensus_threshold:f.z.number().min(.5).max(1).optional().default(.6),fallback_strategy:f.z.enum(["first_available","random","none"]).optional().default("first_available"),enable_caching:f.z.boolean().optional().default(!0),cache_ttl_minutes:f.z.number().int().min(1).max(1440).optional().default(60),timeout_seconds:f.z.number().int().min(10).max(300).optional().default(60)}).optional().default({})});async function x(e,{params:t}){try{let i=await h.authenticateRequest(e);if(!i.success)return m.Rp.json({error:{message:i.error,type:"authentication_error",code:"invalid_api_key"}},{status:i.statusCode||401});let{userApiKey:r,userConfig:a,ipAddress:o}=i,{configId:s}=await t,n=(0,g.Qb)(e),{data:c,error:l}=await n.from("custom_api_configs").select(`
        id,
        name,
        routing_strategy,
        settings,
        role_assignments(
          id,
          role_name,
          api_key_id,
          priority,
          api_keys(
            id,
            label,
            provider,
            status
          )
        ),
        agent_assignments(
          id,
          agent_number,
          api_key_id,
          agent_prompt,
          api_keys(
            id,
            label,
            provider,
            status
          )
        )
      `).eq("id",s).eq("user_id",a.user_id).single();if(l||!c)return m.Rp.json({error:{message:"Configuration not found",type:"not_found_error",code:"config_not_found"}},{status:404});return h.logApiUsage(r,e,{statusCode:200,modelUsed:"routing_management",providerUsed:"rokey_api"},o).catch(console.error),m.Rp.json({id:c.id,object:"routing_config",name:c.name,routing_strategy:c.routing_strategy,settings:c.settings||{},role_assignments:c.role_assignments||[],agent_assignments:c.agent_assignments||[]},{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, PUT, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}catch(e){return m.Rp.json({error:{message:"Internal server error",type:"server_error",code:"internal_error"}},{status:500})}}async function k(e,{params:t}){try{let i=await h.authenticateRequest(e);if(!i.success)return m.Rp.json({error:{message:i.error,type:"authentication_error",code:"invalid_api_key"}},{status:i.statusCode||401});let{userApiKey:r,userConfig:a,ipAddress:o}=i,{configId:s}=await t,n=await e.json(),c=v.safeParse(n);if(!c.success)return m.Rp.json({error:{message:"Invalid routing configuration",type:"validation_error",code:"invalid_parameters",details:c.error.errors}},{status:400});let l=c.data,p=(0,g.Qb)(e),{data:u,error:d}=await p.from("custom_api_configs").select("id, user_id").eq("id",s).eq("user_id",a.user_id).single();if(d||!u)return m.Rp.json({error:{message:"Configuration not found",type:"not_found_error",code:"config_not_found"}},{status:404});let{data:f,error:_}=await p.from("custom_api_configs").update({routing_strategy:l.routing_strategy,settings:l.settings,updated_at:new Date().toISOString()}).eq("id",s).eq("user_id",a.user_id).select().single();if(_)return m.Rp.json({error:{message:"Failed to update routing configuration",type:"server_error",code:"database_error"}},{status:500});if(l.settings.role_assignments&&(await p.from("role_assignments").delete().eq("custom_api_config_id",s),l.settings.role_assignments.length>0)){let e=l.settings.role_assignments.map(e=>({custom_api_config_id:s,role_name:e.role_name,api_key_id:e.api_key_id,priority:e.priority}));await p.from("role_assignments").insert(e)}if(l.settings.agent_assignments&&(await p.from("agent_assignments").delete().eq("custom_api_config_id",s),l.settings.agent_assignments.length>0)){let e=l.settings.agent_assignments.map(e=>({custom_api_config_id:s,agent_number:e.agent_number,api_key_id:e.api_key_id,agent_prompt:e.agent_prompt}));await p.from("agent_assignments").insert(e)}return h.logApiUsage(r,e,{statusCode:200,modelUsed:"routing_management",providerUsed:"rokey_api"},o).catch(console.error),m.Rp.json({id:f.id,object:"routing_config",name:f.name,routing_strategy:f.routing_strategy,settings:f.settings||{},updated_at:f.updated_at,message:"Routing configuration updated successfully"},{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, PUT, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}catch(e){return m.Rp.json({error:{message:"Internal server error",type:"server_error",code:"internal_error"}},{status:500})}}async function w(){return new m.Rp(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, PUT, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}let C=new l.AppRouteRouteModule({definition:{kind:p.A.APP_ROUTE,page:"/api/external/v1/configs/[configId]/routing/route",pathname:"/api/external/v1/configs/[configId]/routing",filename:"route",bundlePath:"app/api/external/v1/configs/[configId]/routing/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\external\\v1\\configs\\[configId]\\routing\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:I,workUnitAsyncStorage:S,serverHooks:R}=C;function z(){return(0,u.V5)({workAsyncStorage:I,workUnitAsyncStorage:S})}let A=null==(r=self.__RSC_MANIFEST)?void 0:r["/api/external/v1/configs/[configId]/routing/route"],P=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);A&&P&&(0,n.fQ)({page:"/api/external/v1/configs/[configId]/routing/route",clientReferenceManifest:A,serverActionsManifest:P,serverModuleMap:(0,s.e)({serverActionsManifest:P})});let T=o,M=c.s.wrap(C,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!0},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp","image/avif"],dangerouslyAllowSVG:!0,contentSecurityPolicy:"default-src 'self'; script-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"raw.githubusercontent.com",port:"",pathname:"/lobehub/lobe-icons/**"},{protocol:"https",hostname:"registry.npmmirror.com",port:"",pathname:"/@lobehub/icons-static-png/**"},{protocol:"https",hostname:"cdn.jsdelivr.net",port:"",pathname:"/npm/simple-icons@latest/icons/**"},{protocol:"https",hostname:"cdn.jsdelivr.net",port:"",pathname:"/npm/simple-icons@v11/icons/**"},{protocol:"https",hostname:"images.unsplash.com",port:"",pathname:"/**"},{protocol:"https",hostname:"cloud.gmelius.com",port:"",pathname:"/public/logos/**"},{protocol:"https",hostname:"upload.wikimedia.org",port:"",pathname:"/wikipedia/commons/**"},{protocol:"https",hostname:"kstatic.googleusercontent.com",port:"",pathname:"/files/**"}],unoptimized:!1},devIndicators:{position:"bottom-left"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"C:\\RoKey App\\rokey-app",experimental:{nodeMiddleware:!1,cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,dynamicOnHover:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!0,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!0,largePageDataBytes:128e3,typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,viewTransition:!1,routerBFCache:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,useCache:!1,optimizePackageImports:["@heroicons/react","@headlessui/react","react-markdown","react-syntax-highlighter","@supabase/supabase-js","lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},htmlLimitedBots:"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti",bundlePagesRouterDependencies:!1,configFile:"C:\\RoKey App\\rokey-app\\next.config.mjs",configFileName:"next.config.mjs",serverExternalPackages:["pdf-parse","mammoth"],turbopack:{rules:{"*.svg":{loaders:["@svgr/webpack"],as:"*.js"}},root:"C:\\RoKey App\\rokey-app"},compiler:{removeConsole:!0,reactRemoveProperties:!0},api:{bodyParser:{sizeLimit:"50mb"},responseLimit:"50mb"},_originalRedirects:[]}})},5356:e=>{"use strict";e.exports=require("node:buffer")},5521:e=>{"use strict";e.exports=require("node:async_hooks")},9975:(e,t,i)=>{"use strict";i.d(t,{Qb:()=>a});var r=i(3339);function a(e){return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,i){},remove(e,t){}}})}i(2710)}},e=>{var t=t=>e(e.s=t);e.O(0,[580,918,109,44,833],()=>t(2632));var i=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/external/v1/configs/[configId]/routing/route"]=i}]);
//# sourceMappingURL=route.js.map