"use strict";exports.id=3794,exports.ids=[3794],exports.modules={3794:(e,t,i)=>{i.a(e,async(e,r)=>{try{i.r(t),i.d(t,{plainTextSelectors:()=>w,pretty:()=>L,render:()=>N,renderAsync:()=>A});var n=i(81397),s=i(61120),a=i(2502),o=i(83505),l=i(57075),c=i(37413),u=e([a,o]);[a,o]=u.then?(await u)():u;var h=Object.defineProperty,d=Object.defineProperties,p=Object.getOwnPropertyDescriptors,f=Object.getOwnPropertySymbols,m=Object.prototype.hasOwnProperty,g=Object.prototype.propertyIsEnumerable,b=(e,t,i)=>t in e?h(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,x=(e,t)=>{for(var i in t||(t={}))m.call(t,i)&&b(e,i,t[i]);if(f)for(var i of f(t))g.call(t,i)&&b(e,i,t[i]);return e},y=(e,t)=>d(e,p(t)),k=(e,t,i)=>new Promise((r,n)=>{var s=e=>{try{o(i.next(e))}catch(e){n(e)}},a=e=>{try{o(i.throw(e))}catch(e){n(e)}},o=e=>e.done?r(e.value):Promise.resolve(e.value).then(s,a);o((i=i.apply(e,t)).next())}),w=[{selector:"img",format:"skip"},{selector:"[data-skip-in-text=true]",format:"skip"},{selector:"a",options:{linkBrackets:!1}}],S=x({},a);if(S.printers){let e=S.printers.html.print;S.printers.html.print=(t,i,r,n)=>{let s=t.getNode(),a=e(t,i,r,n);return"ieConditionalComment"===s.type?function e(t,i){if(Array.isArray(t))return t.map(t=>e(t,i));if("object"==typeof t){if("group"===t.type)return y(x({},t),{contents:e(t.contents,i),expandedStates:e(t.expandedStates,i)});if("contents"in t)return y(x({},t),{contents:e(t.contents,i)});if("parts"in t)return y(x({},t),{parts:e(t.parts,i)});if("if-break"===t.type)return y(x({},t),{breakContents:e(t.breakContents,i),flatContents:e(t.flatContents,i)})}return i(t)}(a,e=>"object"==typeof e&&"line"===e.type?e.soft?"":" ":e):a}}var E={endOfLine:"lf",tabWidth:2,plugins:[S],bracketSameLine:!0,parser:"html"},L=(e,t={})=>(0,o.format)(e.replaceAll("\0",""),x(x({},E),t)),I=new TextDecoder("utf-8"),T=e=>k(void 0,null,function*(){let t="";if("pipeTo"in e){let i=new WritableStream({write(e){t+=I.decode(e)}});yield e.pipeTo(i)}else{let i=new l.Writable({write(e,i,r){t+=I.decode(e),r()}});e.pipe(i),yield new Promise((e,t)=>{i.on("error",t),i.on("close",()=>{e()})})}return t}),N=(e,t)=>k(void 0,null,function*(){let r,a=(0,c.jsx)(s.Suspense,{children:e}),o=yield i.e(2938).then(i.t.bind(i,62938,19)).then(e=>e.default);if(Object.hasOwn(o,"renderToReadableStream")?r=yield T((yield o.renderToReadableStream(a))):yield new Promise((e,t)=>{let i=o.renderToPipeableStream(a,{onAllReady(){return k(this,null,function*(){r=yield T(i),e()})},onError(e){t(e)}})}),null==t?void 0:t.plainText)return(0,n.C6)(r,x({selectors:w},t.htmlToTextOptions));let l=`<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">${r.replace(/<!DOCTYPE.*?>/,"")}`;return(null==t?void 0:t.pretty)?L(l):l}),A=(e,t)=>N(e,t);r()}catch(e){r(e)}})},37413:(e,t,i)=>{e.exports=i(65239).vendored["react-rsc"].ReactJsxRuntime},43793:e=>{var t=function(e){var t,r,n;return!!(t=e)&&"object"==typeof t&&(r=e,"[object RegExp]"!==(n=Object.prototype.toString.call(r))&&"[object Date]"!==n&&r.$$typeof!==i)},i="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function r(e,t){return!1!==t.clone&&t.isMergeableObject(e)?o(Array.isArray(e)?[]:{},e,t):e}function n(e,t,i){return e.concat(t).map(function(e){return r(e,i)})}function s(e){return Object.keys(e).concat(Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter(function(t){return Object.propertyIsEnumerable.call(e,t)}):[])}function a(e,t){try{return t in e}catch(e){return!1}}function o(e,i,l){(l=l||{}).arrayMerge=l.arrayMerge||n,l.isMergeableObject=l.isMergeableObject||t,l.cloneUnlessOtherwiseSpecified=r;var c,u,h=Array.isArray(i);return h!==Array.isArray(e)?r(i,l):h?l.arrayMerge(e,i,l):(u={},(c=l).isMergeableObject(e)&&s(e).forEach(function(t){u[t]=r(e[t],c)}),s(i).forEach(function(t){a(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))||(a(e,t)&&c.isMergeableObject(i[t])?u[t]=(function(e,t){if(!t.customMerge)return o;var i=t.customMerge(e);return"function"==typeof i?i:o})(t,c)(e[t],i[t],c):u[t]=r(i[t],c))}),u)}o.all=function(e,t){if(!Array.isArray(e))throw Error("first argument should be an array");return e.reduce(function(e,i){return o(e,i,t)},{})},e.exports=o},81397:(e,t,i)=>{i.d(t,{C6:()=>iE}),function(e){e.Root="root",e.Text="text",e.Directive="directive",e.Comment="comment",e.Script="script",e.Style="style",e.Tag="tag",e.CDATA="cdata",e.Doctype="doctype"}(tR||(tR={}));let r=tR.Root,n=tR.Text,s=tR.Directive,a=tR.Comment,o=tR.Script,l=tR.Style,c=tR.Tag,u=tR.CDATA,h=tR.Doctype;class d{constructor(){this.parent=null,this.prev=null,this.next=null,this.startIndex=null,this.endIndex=null}get parentNode(){return this.parent}set parentNode(e){this.parent=e}get previousSibling(){return this.prev}set previousSibling(e){this.prev=e}get nextSibling(){return this.next}set nextSibling(e){this.next=e}cloneNode(e=!1){return E(this,e)}}class p extends d{constructor(e){super(),this.data=e}get nodeValue(){return this.data}set nodeValue(e){this.data=e}}class f extends p{constructor(){super(...arguments),this.type=tR.Text}get nodeType(){return 3}}class m extends p{constructor(){super(...arguments),this.type=tR.Comment}get nodeType(){return 8}}class g extends p{constructor(e,t){super(t),this.name=e,this.type=tR.Directive}get nodeType(){return 1}}class b extends d{constructor(e){super(),this.children=e}get firstChild(){var e;return null!=(e=this.children[0])?e:null}get lastChild(){return this.children.length>0?this.children[this.children.length-1]:null}get childNodes(){return this.children}set childNodes(e){this.children=e}}class x extends b{constructor(){super(...arguments),this.type=tR.CDATA}get nodeType(){return 4}}class y extends b{constructor(){super(...arguments),this.type=tR.Root}get nodeType(){return 9}}class k extends b{constructor(e,t,i=[],r="script"===e?tR.Script:"style"===e?tR.Style:tR.Tag){super(i),this.name=e,this.attribs=t,this.type=r}get nodeType(){return 1}get tagName(){return this.name}set tagName(e){this.name=e}get attributes(){return Object.keys(this.attribs).map(e=>{var t,i;return{name:e,value:this.attribs[e],namespace:null==(t=this["x-attribsNamespace"])?void 0:t[e],prefix:null==(i=this["x-attribsPrefix"])?void 0:i[e]}})}}function w(e){return e.type===tR.Tag||e.type===tR.Script||e.type===tR.Style}function S(e){return e.type===tR.Text}function E(e,t=!1){let i;if(S(e))i=new f(e.data);else if(e.type===tR.Comment)i=new m(e.data);else if(w(e)){let r=t?L(e.children):[],n=new k(e.name,{...e.attribs},r);r.forEach(e=>e.parent=n),null!=e.namespace&&(n.namespace=e.namespace),e["x-attribsNamespace"]&&(n["x-attribsNamespace"]={...e["x-attribsNamespace"]}),e["x-attribsPrefix"]&&(n["x-attribsPrefix"]={...e["x-attribsPrefix"]}),i=n}else if(e.type===tR.CDATA){let r=t?L(e.children):[],n=new x(r);r.forEach(e=>e.parent=n),i=n}else if(e.type===tR.Root){let r=t?L(e.children):[],n=new y(r);r.forEach(e=>e.parent=n),e["x-mode"]&&(n["x-mode"]=e["x-mode"]),i=n}else if(e.type===tR.Directive){let t=new g(e.name,e.data);null!=e["x-name"]&&(t["x-name"]=e["x-name"],t["x-publicId"]=e["x-publicId"],t["x-systemId"]=e["x-systemId"]),i=t}else throw Error(`Not implemented yet: ${e.type}`);return i.startIndex=e.startIndex,i.endIndex=e.endIndex,null!=e.sourceCodeLocation&&(i.sourceCodeLocation=e.sourceCodeLocation),i}function L(e){let t=e.map(e=>E(e,!0));for(let e=1;e<t.length;e++)t[e].prev=t[e-1],t[e-1].next=t[e];return t}let I={withStartIndices:!1,withEndIndices:!1,xmlMode:!1};class T{constructor(e,t,i){this.dom=[],this.root=new y(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null,"function"==typeof t&&(i=t,t=I),"object"==typeof e&&(t=e,e=void 0),this.callback=null!=e?e:null,this.options=null!=t?t:I,this.elementCB=null!=i?i:null}onparserinit(e){this.parser=e}onreset(){this.dom=[],this.root=new y(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null}onend(){this.done||(this.done=!0,this.parser=null,this.handleCallback(null))}onerror(e){this.handleCallback(e)}onclosetag(){this.lastNode=null;let e=this.tagStack.pop();this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),this.elementCB&&this.elementCB(e)}onopentag(e,t){let i=new k(e,t,void 0,this.options.xmlMode?tR.Tag:void 0);this.addNode(i),this.tagStack.push(i)}ontext(e){let{lastNode:t}=this;if(t&&t.type===tR.Text)t.data+=e,this.options.withEndIndices&&(t.endIndex=this.parser.endIndex);else{let t=new f(e);this.addNode(t),this.lastNode=t}}oncomment(e){if(this.lastNode&&this.lastNode.type===tR.Comment){this.lastNode.data+=e;return}let t=new m(e);this.addNode(t),this.lastNode=t}oncommentend(){this.lastNode=null}oncdatastart(){let e=new f(""),t=new x([e]);this.addNode(t),e.parent=t,this.lastNode=e}oncdataend(){this.lastNode=null}onprocessinginstruction(e,t){let i=new g(e,t);this.addNode(i)}handleCallback(e){if("function"==typeof this.callback)this.callback(e,this.dom);else if(e)throw e}addNode(e){let t=this.tagStack[this.tagStack.length-1],i=t.children[t.children.length-1];this.options.withStartIndices&&(e.startIndex=this.parser.startIndex),this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),t.children.push(e),i&&(e.prev=i,i.next=e),e.parent=t,this.lastNode=null}}let N=/\n/g;function A(e,t="",i={}){let r="string"==typeof t?t:"",n=e.map(B),s=!!("string"!=typeof t?t:i).lineNumbers;return function(e,t=0){let i=s?function(e){let t=[...e.matchAll(N)].map(e=>e.index||0);t.unshift(-1);let i=function e(t,i,r){if(r-i==1)return{offset:t[i],index:i+1};let n=Math.ceil((i+r)/2),s=e(t,i,n),a=e(t,n,r);return{offset:s.offset,low:s,high:a}}(t,0,t.length);return e=>(function e(t,i){return Object.prototype.hasOwnProperty.call(t,"index")?{line:t.index,column:i-t.offset}:e(t.high.offset<i?t.high:t.low,i)})(i,e)}(e):()=>({line:0,column:0}),a=t,o=[];e:for(;a<e.length;){let t=!1;for(let s of n){s.regex.lastIndex=a;let n=s.regex.exec(e);if(n&&n[0].length>0){if(!s.discard){let e=i(a),t="string"==typeof s.replace?n[0].replace(new RegExp(s.regex.source,s.regex.flags),s.replace):n[0];o.push({state:r,name:s.name,text:t,offset:a,len:n[0].length,line:e.line,column:e.column})}if(a=s.regex.lastIndex,t=!0,s.push){let t=s.push(e,a);o.push(...t.tokens),a=t.offset}if(s.pop)break e;break}}if(!t)break}return{tokens:o,offset:a,complete:e.length<=a}}}function B(e,t){return{...e,regex:function(e,t){if(0===e.name.length)throw Error(`Rule #${t} has empty name, which is not allowed.`);if(Object.prototype.hasOwnProperty.call(e,"regex")){var i=e.regex;if(i.global)throw Error(`Regular expression /${i.source}/${i.flags} contains the global flag, which is not allowed.`);return i.sticky?i:RegExp(i.source,i.flags+"y")}if(Object.prototype.hasOwnProperty.call(e,"str")){if(0===e.str.length)throw Error(`Rule #${t} ("${e.name}") has empty "str" property, which is not allowed.`);return RegExp(C(e.str),"y")}return RegExp(C(e.name),"y")}(e,t)}}function C(e){return e.replace(/[-[\]{}()*+!<=:?./\\^$|#\s,]/g,"\\$&")}function v(e,t){return(i,r)=>{let n,s=r;return r<i.tokens.length?void 0!==(n=e(i.tokens[r],i,r))&&s++:t?.(i,r),void 0===n?{matched:!1}:{matched:!0,position:s,value:n}}}function q(e,t){return e.matched?{matched:!0,position:e.position,value:t(e.value,e.position)}:e}function D(e,t){return e.matched?t(e):e}function R(e,t){return(i,r)=>q(e(i,r),(e,n)=>t(e,i,r,n))}function _(e,t){return(i,r)=>{let n=e(i,r);return n.matched?n:{matched:!0,position:r,value:t}}}function O(...e){return(t,i)=>{for(let r of e){let e=r(t,i);if(e.matched)return e}return{matched:!1}}}function W(e,t){return(i,r)=>{let n=e(i,r);return n.matched?n:t(i,r)}}function P(e){var t,i;return t=e,i=()=>!0,(e,r)=>{let n=[],s=!0;do{let a=t(e,r);a.matched&&i(a.value,n.length+1,e,r,a.position)?(n.push(a.value),r=a.position):s=!1}while(s);return{matched:!0,position:r,value:n}}}function M(e,t,i){return(r,n)=>D(e(r,n),e=>q(t(r,e.position),(t,s)=>i(e.value,t,r,n,s)))}function $(e,t){return M(e,t,(e,t)=>t)}function V(e,t,i,r){return(n,s)=>D(e(n,s),e=>D(t(n,e.position),t=>q(i(n,t.position),(i,a)=>r(e.value,t.value,i,n,s,a))))}function U(e,t,i){return V(e,t,i,(e,t)=>t)}function j(e,t,i){return function(e,t){return(i,r)=>D(e(i,r),e=>t(e.value,i,r,e.position)(i,e.position))}(e,e=>{var r,n,s,a;return r=e,n=M(t,i,(e,t)=>[e,t]),s=(e,[t,i])=>t(e,i),a=e=>R(n,(t,i,r,n)=>s(e,t,i,r,n)),(e,t)=>{let i=!0,n=r,s=t;do{let t=a(n,e,s)(e,s);t.matched?(n=t.value,s=t.position):i=!1}while(i);return{matched:!0,position:s,value:n}}})}let H=`(?:\\n|\\r\\n|\\r|\\f)`,G=`[^\\x00-\\x7F]`,F=`(?:\\\\[0-9a-f]{1,6}(?:\\r\\n|[ \\n\\r\\t\\f])?)`,z=`(?:\\\\[^\\n\\r\\f0-9a-f])`,Q=`(?:[_a-z]|${G}|${F}|${z})`,Z=`(?:[_a-z0-9-]|${G}|${F}|${z})`,X=`(?:${Z}+)`,J=`(?:[-]?${Q}${Z}*)`,Y=`'([^\\n\\r\\f\\\\']|\\\\${H}|${G}|${F}|${z})*'`,K=`"([^\\n\\r\\f\\\\"]|\\\\${H}|${G}|${F}|${z})*"`,ee=A([{name:"ws",regex:RegExp(`(?:[ \\t\\r\\n\\f]*)`)},{name:"hash",regex:RegExp(`#${X}`,"i")},{name:"ident",regex:RegExp(J,"i")},{name:"str1",regex:RegExp(Y,"i")},{name:"str2",regex:RegExp(K,"i")},{name:"*"},{name:"."},{name:","},{name:"["},{name:"]"},{name:"="},{name:">"},{name:"|"},{name:"+"},{name:"~"},{name:"^"},{name:"$"}]),et=A([{name:"unicode",regex:RegExp(F,"i")},{name:"escape",regex:RegExp(z,"i")},{name:"any",regex:RegExp("[\\s\\S]","i")}]);function ei([e,t,i],[r,n,s]){return[e+r,t+n,i+s]}let er=v(e=>"unicode"===e.name?String.fromCodePoint(parseInt(e.text.slice(1),16)):void 0),en=R(P(O(er,v(e=>"escape"===e.name?e.text.slice(1):void 0),v(e=>"any"===e.name?e.text:void 0))),e=>e.join(""));function es(e){return en({tokens:et(e).tokens,options:void 0},0).value}function ea(e){return v(t=>t.name===e||void 0)}let eo=v(e=>"ws"===e.name?null:void 0),el=_(eo,null);function ec(e){return U(el,e,el)}let eu=v(e=>"ident"===e.name?es(e.text):void 0),eh=v(e=>"hash"===e.name?es(e.text.slice(1)):void 0),ed=v(e=>e.name.startsWith("str")?es(e.text.slice(1,-1)):void 0),ep=M(_(eu,""),ea("|"),e=>e),ef=W(M(ep,eu,(e,t)=>({name:t,namespace:e})),R(eu,e=>({name:e,namespace:null}))),em=W(M(ep,ea("*"),e=>({type:"universal",namespace:e,specificity:[0,0,0]})),R(ea("*"),()=>({type:"universal",namespace:null,specificity:[0,0,0]}))),eg=R(ef,({name:e,namespace:t})=>({type:"tag",name:e,namespace:t,specificity:[0,0,1]})),eb=M(ea("."),eu,(e,t)=>({type:"class",name:t,specificity:[0,1,0]})),ex=R(eh,e=>({type:"id",name:e,specificity:[1,0,0]})),ey=v(e=>{if("ident"===e.name){if("i"===e.text||"I"===e.text)return"i";if("s"===e.text||"S"===e.text)return"s"}}),ek=W(M(ed,_($(el,ey),null),(e,t)=>({value:e,modifier:t})),M(eu,_($(eo,ey),null),(e,t)=>({value:e,modifier:t}))),ew=O(R(ea("="),()=>"="),M(ea("~"),ea("="),()=>"~="),M(ea("|"),ea("="),()=>"|="),M(ea("^"),ea("="),()=>"^="),M(ea("$"),ea("="),()=>"$="),M(ea("*"),ea("="),()=>"*=")),eS=W(V(ea("["),ec(ef),ea("]"),(e,{name:t,namespace:i})=>({type:"attrPresence",name:t,namespace:i,specificity:[0,1,0]})),U(ea("["),V(ec(ef),ew,ec(ek),({name:e,namespace:t},i,{value:r,modifier:n})=>({type:"attrValue",name:e,namespace:t,matcher:i,value:r,modifier:n,specificity:[0,1,0]})),ea("]"))),eE=W(em,eg),eL=O(ex,eb,eS),eI=R(W(function(...e){return R(function(...e){return(t,i)=>{let r=[],n=i;for(let i of e){let e=i(t,n);if(!e.matched)return{matched:!1};r.push(e.value),n=e.position}return{matched:!0,position:n,value:r}}}(...e),e=>e.flatMap(e=>e))}(eE,P(eL)),function(e){return M(e,P(e),(e,t)=>[e,...t])}(eL)),e=>({type:"compound",list:e,specificity:e.map(e=>e.specificity).reduce(ei,[0,0,0])})),eT=W(ec(O(R(ea(">"),()=>">"),R(ea("+"),()=>"+"),R(ea("~"),()=>"~"),M(ea("|"),ea("|"),()=>"||"))),R(eo,()=>" ")),eN=j(eI,R(eT,e=>(t,i)=>({type:"compound",list:[...i.list,{type:"combinator",combinator:e,left:t,specificity:t.specificity}],specificity:ei(t.specificity,i.specificity)})),eI);j(R(eN,e=>({type:"list",list:[e]})),R(ec(ea(",")),()=>(e,t)=>({type:"list",list:[...e.list,t]})),eN);function eA(e,t,i=1){return`${e.replace(/(\t)|(\r)|(\n)/g,(e,t,i)=>t?"␉":i?"␍":"␊")}
${"".padEnd(t)}${"^".repeat(i)}`}function eB(e){if(!e.type)throw Error("This is not an AST node.");switch(e.type){case"universal":return eC(e.namespace)+"*";case"tag":return eC(e.namespace)+eq(e.name);case"class":return"."+eq(e.name);case"id":return"#"+eq(e.name);case"attrPresence":return`[${eC(e.namespace)}${eq(e.name)}]`;case"attrValue":return`[${eC(e.namespace)}${eq(e.name)}${e.matcher}"${e.value.replace(/(")|(\\)|(\x00)|([\x01-\x1f]|\x7f)/g,(e,t,i,r,n)=>t?'\\"':i?"\\\\":r?"�":ev(n))}"${e.modifier?e.modifier:""}]`;case"combinator":return eB(e.left)+e.combinator;case"compound":return e.list.reduce((e,t)=>"combinator"===t.type?eB(t)+e:e+eB(t),"");case"list":return e.list.map(eB).join(",")}}function eC(e){return e||""===e?eq(e)+"|":""}function ev(e){return`\\${e.codePointAt(0).toString(16)} `}function eq(e){return e.replace(/(^[0-9])|(^-[0-9])|(^-$)|([-0-9a-zA-Z_]|[^\x00-\x7F])|(\x00)|([\x01-\x1f]|\x7f)|([\s\S])/g,(e,t,i,r,n,s,a,o)=>t?ev(t):i?"-"+ev(i.slice(1)):r?"\\-":n||(s?"�":a?ev(a):"\\"+o))}function eD(e){switch(e.type){case"universal":case"tag":return[1];case"id":return[2];case"class":return[3,e.name];case"attrPresence":return[4,eB(e)];case"attrValue":return[5,eB(e)];case"combinator":return[15,eB(e)]}}function eR(e,t){if(!Array.isArray(e)||!Array.isArray(t))throw Error("Arguments must be arrays.");let i=e.length<t.length?e.length:t.length;for(let r=0;r<i;r++)if(e[r]!==t[r])return e[r]<t[r]?-1:1;return e.length-t.length}let e_=[["├─","│ "],["└─","  "]],eO=[["┠─","┃ "],["┖─","  "]],eW=[["╟─","║ "],["╙─","  "]];class eP{constructor(e){this.branches=eM(function(e){let t=e.length,i=Array(t);for(let n=0;n<t;n++){var r;let[t,s]=e[n],a=(function e(t){let i=[];t.list.forEach(t=>{switch(t.type){case"class":i.push({matcher:"~=",modifier:null,name:"class",namespace:null,specificity:t.specificity,type:"attrValue",value:t.name});break;case"id":i.push({matcher:"=",modifier:null,name:"id",namespace:null,specificity:t.specificity,type:"attrValue",value:t.name});break;case"combinator":e(t.left),i.push(t);break;case"universal":break;default:i.push(t)}}),t.list=i}(r=function(e,t){if(!("string"==typeof t||t instanceof String))throw Error("Expected a selector string. Actual input is not a string!");let i=ee(t);if(!i.complete)throw Error(`The input "${t}" was only partially tokenized, stopped at offset ${i.offset}!
`+eA(t,i.offset));let r=ec(e)({tokens:i.tokens,options:void 0},0);if(!r.matched)throw Error(`No match for "${t}" input!`);if(r.position<i.tokens.length){let e=i.tokens[r.position];throw Error(`The input "${t}" was only partially parsed, stopped at offset ${e.offset}!
`+eA(t,e.offset,e.len))}return r.value}(eN,t)),!function e(t){if(!t.type)throw Error("This is not an AST node.");switch(t.type){case"compound":t.list.forEach(e),t.list.sort((e,t)=>eR(eD(e),eD(t)));break;case"combinator":e(t.left);break;case"list":t.list.forEach(e),t.list.sort((e,t)=>eB(e)<eB(t)?-1:1)}return t}(r),r);i[n]={ast:a,terminal:{type:"terminal",valueContainer:{index:n,value:s,specificity:a.specificity}}}}return i}(e))}build(e){return e(this.branches)}}function eM(e){let t=[];for(;e.length;){let i=eH(e,e=>!0,e$),{matches:r,nonmatches:n,empty:s}=function(e,t){let i=[],r=[],n=[];for(let s of e){let e=s.ast.list;e.length?(e.some(e=>e$(e)===t)?i:r).push(s):n.push(s)}return{matches:i,nonmatches:r,empty:n}}(e,i);e=n,r.length&&t.push(function(e,t){if("tag"===e)return{type:"tagName",variants:Object.entries(eU(t,e=>"tag"===e.type,e=>e.name)).map(([e,t])=>({type:"variant",value:e,cont:eM(t.items)}))};if(e.startsWith("attrValue "))return function(e,t){let i=eU(t,t=>"attrValue"===t.type&&t.name===e,e=>`${e.matcher} ${e.modifier||""} ${e.value}`),r=[];for(let e of Object.values(i)){let t=e.oneSimpleSelector,i=function(e){if("i"===e.modifier){let t=e.value.toLowerCase();switch(e.matcher){case"=":return e=>t===e.toLowerCase();case"~=":return e=>e.toLowerCase().split(/[ \t]+/).includes(t);case"^=":return e=>e.toLowerCase().startsWith(t);case"$=":return e=>e.toLowerCase().endsWith(t);case"*=":return e=>e.toLowerCase().includes(t);case"|=":return e=>{let i=e.toLowerCase();return t===i||i.startsWith(t)&&"-"===i[t.length]}}}else{let t=e.value;switch(e.matcher){case"=":return e=>t===e;case"~=":return e=>e.split(/[ \t]+/).includes(t);case"^=":return e=>e.startsWith(t);case"$=":return e=>e.endsWith(t);case"*=":return e=>e.includes(t);case"|=":return e=>t===e||e.startsWith(t)&&"-"===e[t.length]}}}(t),n=eM(e.items);r.push({type:"matcher",matcher:t.matcher,modifier:t.modifier,value:t.value,predicate:i,cont:n})}return{type:"attrValue",name:e,matchers:r}}(e.substring(10),t);if(e.startsWith("attrPresence "))return function(e,t){for(let i of t)ej(i,t=>"attrPresence"===t.type&&t.name===e);return{type:"attrPresence",name:e,cont:eM(t)}}(e.substring(13),t);if("combinator >"===e)return eV(">",t);if("combinator +"===e)return eV("+",t);throw Error(`Unsupported selector kind: ${e}`)}(i,r)),s.length&&t.push(...function(e){let t=[];for(let i of e){let e=i.terminal;if("terminal"===e.type)t.push(e);else{let{matches:i,rest:r}=function(e,t){let i=[],r=[];for(let n of e)t(n)?i.push(n):r.push(n);return{matches:i,rest:r}}(e.cont,e=>"terminal"===e.type);i.forEach(e=>t.push(e)),r.length&&(e.cont=r,t.push(e))}}return t}(s))}return t}function e$(e){switch(e.type){case"attrPresence":return`attrPresence ${e.name}`;case"attrValue":return`attrValue ${e.name}`;case"combinator":return`combinator ${e.combinator}`;default:return e.type}}function eV(e,t){let i=eU(t,t=>"combinator"===t.type&&t.combinator===e,e=>eB(e.left)),r=[];for(let e of Object.values(i)){let t=eM(e.items),i=e.oneSimpleSelector.left;r.push({ast:i,terminal:{type:"popElement",cont:t}})}return{type:"pushElement",combinator:e,cont:eM(r)}}function eU(e,t,i){let r={};for(;e.length;){let n=eH(e,t,i),s=e=>t(e)&&i(e)===n,{matches:a,rest:o}=function(e,t){let i=[],r=[];for(let n of e)t(n)?i.push(n):r.push(n);return{matches:i,rest:r}}(e,e=>e.ast.list.some(s)),l=null;for(let e of a){let t=ej(e,s);l||(l=t)}if(null==l)throw Error("No simple selector is found.");r[n]={oneSimpleSelector:l,items:a},e=o}return r}function ej(e,t){let i=e.ast.list,r=Array(i.length),n=-1;for(let e=i.length;e-- >0;)t(i[e])&&(r[e]=!0,n=e);if(-1==n)throw Error("Couldn't find the required simple selector.");let s=i[n];return e.ast.list=i.filter((e,t)=>!r[t]),s}function eH(e,t,i){let r={};for(let n of e){let e={};for(let r of n.ast.list.filter(t))e[i(r)]=!0;for(let t of Object.keys(e))r[t]?r[t]++:r[t]=1}let n="",s=0;for(let e of Object.entries(r))e[1]>s&&(n=e[0],s=e[1]);return n}class eG{constructor(e){this.f=e}pickAll(e){return this.f(e)}pick1(e,t=!1){let i=this.f(e),r=i.length;if(0===r)return null;if(1===r)return i[0].value;let n=t?eF:ez,s=i[0];for(let e=1;e<r;e++){let t=i[e];n(s,t)&&(s=t)}return s.value}}function eF(e,t){let i=eR(t.specificity,e.specificity);return i>0||0===i&&t.index<e.index}function ez(e,t){let i=eR(t.specificity,e.specificity);return i>0||0===i&&t.index>e.index}function eQ(e){return new eG(eZ(e))}function eZ(e){let t=e.map(eX);return(e,...i)=>t.flatMap(t=>t(e,...i))}function eX(e){switch(e.type){case"terminal":{let t=[e.valueContainer];return(e,...i)=>t}case"tagName":var t=e;let i={};for(let e of t.variants)i[e.value]=eZ(e.cont);return(e,...t)=>{let r=i[e.name];return r?r(e,...t):[]};case"attrValue":var r=e;let n=[];for(let e of r.matchers){let t=e.predicate,i=eZ(e.cont);n.push((e,r,...n)=>t(e)?i(r,...n):[])}let s=r.name;return(e,...t)=>{let i=e.attribs[s];return i||""===i?n.flatMap(r=>r(i,e,...t)):[]};case"attrPresence":var a=e;let o=a.name,l=eZ(a.cont);return(e,...t)=>Object.prototype.hasOwnProperty.call(e.attribs,o)?l(e,...t):[];case"pushElement":var c=e;let u=eZ(c.cont),h="+"===c.combinator?eJ:eY;return(e,...t)=>{let i=h(e);return null===i?[]:u(i,e,...t)};case"popElement":var d=e;let p=eZ(d.cont);return(e,t,...i)=>p(t,...i)}}let eJ=e=>{let t=e.prev;return null===t?null:w(t)?t:eJ(t)},eY=e=>{let t=e.parent;return t&&w(t)?t:null},eK=new Uint16Array('ᵁ<\xd5ıʊҝջאٵ۞ޢߖࠏ੊ઑඡ๭༉༦჊ረዡᐕᒝᓃᓟᔥ\0\0\0\0\0\0ᕫᛍᦍᰒᷝ὾⁠↰⊍⏀⏻⑂⠤⤒ⴈ⹈⿎〖㊺㘹㞬㣾㨨㩱㫠㬮ࠀEMabcfglmnoprstu\\bfms\x7f\x84\x8b\x90\x95\x98\xa6\xb3\xb9\xc8\xcflig耻\xc6䃆P耻&䀦cute耻\xc1䃁reve;䄂Āiyx}rc耻\xc2䃂;䐐r;쀀\ud835\udd04rave耻\xc0䃀pha;䎑acr;䄀d;橓Āgp\x9d\xa1on;䄄f;쀀\ud835\udd38plyFunction;恡ing耻\xc5䃅Ācs\xbe\xc3r;쀀\ud835\udc9cign;扔ilde耻\xc3䃃ml耻\xc4䃄Ѐaceforsu\xe5\xfb\xfeėĜĢħĪĀcr\xea\xf2kslash;或Ŷ\xf6\xf8;櫧ed;挆y;䐑ƀcrtąċĔause;戵noullis;愬a;䎒r;쀀\ud835\udd05pf;쀀\ud835\udd39eve;䋘c\xf2ēmpeq;扎܀HOacdefhilorsuōőŖƀƞƢƵƷƺǜȕɳɸɾcy;䐧PY耻\xa9䂩ƀcpyŝŢźute;䄆Ā;iŧŨ拒talDifferentialD;慅leys;愭ȀaeioƉƎƔƘron;䄌dil耻\xc7䃇rc;䄈nint;戰ot;䄊ĀdnƧƭilla;䂸terDot;䂷\xf2ſi;䎧rcleȀDMPTǇǋǑǖot;抙inus;抖lus;投imes;抗oĀcsǢǸkwiseContourIntegral;戲eCurlyĀDQȃȏoubleQuote;思uote;怙ȀlnpuȞȨɇɕonĀ;eȥȦ户;橴ƀgitȯȶȺruent;扡nt;戯ourIntegral;戮ĀfrɌɎ;愂oduct;成nterClockwiseContourIntegral;戳oss;樯cr;쀀\ud835\udc9epĀ;Cʄʅ拓ap;才րDJSZacefiosʠʬʰʴʸˋ˗ˡ˦̳ҍĀ;oŹʥtrahd;椑cy;䐂cy;䐅cy;䐏ƀgrsʿ˄ˇger;怡r;憡hv;櫤Āayː˕ron;䄎;䐔lĀ;t˝˞戇a;䎔r;쀀\ud835\udd07Āaf˫̧Ācm˰̢riticalȀADGT̖̜̀̆cute;䂴oŴ̋̍;䋙bleAcute;䋝rave;䁠ilde;䋜ond;拄ferentialD;慆Ѱ̽\0\0\0͔͂\0Ѕf;쀀\ud835\udd3bƀ;DE͈͉͍䂨ot;惜qual;扐blèCDLRUVͣͲ΂ϏϢϸontourIntegra\xecȹoɴ͹\0\0ͻ\xbb͉nArrow;懓Āeo·ΤftƀARTΐΖΡrrow;懐ightArrow;懔e\xe5ˊngĀLRΫτeftĀARγιrrow;柸ightArrow;柺ightArrow;柹ightĀATϘϞrrow;懒ee;抨pɁϩ\0\0ϯrrow;懑ownArrow;懕erticalBar;戥ǹABLRTaВЪаўѿͼrrowƀ;BUНОТ憓ar;椓pArrow;懵reve;䌑eft˒к\0ц\0ѐightVector;楐eeVector;楞ectorĀ;Bљњ憽ar;楖ightǔѧ\0ѱeeVector;楟ectorĀ;BѺѻ懁ar;楗eeĀ;A҆҇护rrow;憧ĀctҒҗr;쀀\ud835\udc9frok;䄐ࠀNTacdfglmopqstuxҽӀӄӋӞӢӧӮӵԡԯԶՒ՝ՠեG;䅊H耻\xd0䃐cute耻\xc9䃉ƀaiyӒӗӜron;䄚rc耻\xca䃊;䐭ot;䄖r;쀀\ud835\udd08rave耻\xc8䃈ement;戈ĀapӺӾcr;䄒tyɓԆ\0\0ԒmallSquare;旻erySmallSquare;斫ĀgpԦԪon;䄘f;쀀\ud835\udd3csilon;䎕uĀaiԼՉlĀ;TՂՃ橵ilde;扂librium;懌Āci՗՚r;愰m;橳a;䎗ml耻\xcb䃋Āipժկsts;戃onentialE;慇ʀcfiosօֈ֍ֲ׌y;䐤r;쀀\ud835\udd09lledɓ֗\0\0֣mallSquare;旼erySmallSquare;斪Ͱֺ\0ֿ\0\0ׄf;쀀\ud835\udd3dAll;戀riertrf;愱c\xf2׋؀JTabcdfgorstר׬ׯ׺؀ؒؖ؛؝أ٬ٲcy;䐃耻>䀾mmaĀ;d׷׸䎓;䏜reve;䄞ƀeiy؇،ؐdil;䄢rc;䄜;䐓ot;䄠r;쀀\ud835\udd0a;拙pf;쀀\ud835\udd3eeater̀EFGLSTصلَٖٛ٦qualĀ;Lؾؿ扥ess;招ullEqual;执reater;檢ess;扷lantEqual;橾ilde;扳cr;쀀\ud835\udca2;扫ЀAacfiosuڅڋږڛڞڪھۊRDcy;䐪Āctڐڔek;䋇;䁞irc;䄤r;愌lbertSpace;愋ǰگ\0ڲf;愍izontalLine;攀Āctۃۅ\xf2کrok;䄦mpńېۘownHum\xf0įqual;扏܀EJOacdfgmnostuۺ۾܃܇܎ܚܞܡܨ݄ݸދޏޕcy;䐕lig;䄲cy;䐁cute耻\xcd䃍Āiyܓܘrc耻\xce䃎;䐘ot;䄰r;愑rave耻\xcc䃌ƀ;apܠܯܿĀcgܴܷr;䄪inaryI;慈lie\xf3ϝǴ݉\0ݢĀ;eݍݎ戬Āgrݓݘral;戫section;拂isibleĀCTݬݲomma;恣imes;恢ƀgptݿރވon;䄮f;쀀\ud835\udd40a;䎙cr;愐ilde;䄨ǫޚ\0ޞcy;䐆l耻\xcf䃏ʀcfosuެ޷޼߂ߐĀiyޱ޵rc;䄴;䐙r;쀀\ud835\udd0dpf;쀀\ud835\udd41ǣ߇\0ߌr;쀀\ud835\udca5rcy;䐈kcy;䐄΀HJacfosߤߨ߽߬߱ࠂࠈcy;䐥cy;䐌ppa;䎚Āey߶߻dil;䄶;䐚r;쀀\ud835\udd0epf;쀀\ud835\udd42cr;쀀\ud835\udca6րJTaceflmostࠥࠩࠬࡐࡣ঳সে্਷ੇcy;䐉耻<䀼ʀcmnpr࠷࠼ࡁࡄࡍute;䄹bda;䎛g;柪lacetrf;愒r;憞ƀaeyࡗ࡜ࡡron;䄽dil;䄻;䐛Āfsࡨ॰tԀACDFRTUVarࡾࢩࢱࣦ࣠ࣼयज़ΐ४Ānrࢃ࢏gleBracket;柨rowƀ;BR࢙࢚࢞憐ar;懤ightArrow;懆eiling;挈oǵࢷ\0ࣃbleBracket;柦nǔࣈ\0࣒eeVector;楡ectorĀ;Bࣛࣜ懃ar;楙loor;挊ightĀAV࣯ࣵrrow;憔ector;楎Āerँगeƀ;AVउऊऐ抣rrow;憤ector;楚iangleƀ;BEतथऩ抲ar;槏qual;抴pƀDTVषूौownVector;楑eeVector;楠ectorĀ;Bॖॗ憿ar;楘ectorĀ;B॥०憼ar;楒ight\xe1Μs̀EFGLSTॾঋকঝঢভqualGreater;拚ullEqual;扦reater;扶ess;檡lantEqual;橽ilde;扲r;쀀\ud835\udd0fĀ;eঽা拘ftarrow;懚idot;䄿ƀnpw৔ਖਛgȀLRlr৞৷ਂਐeftĀAR০৬rrow;柵ightArrow;柷ightArrow;柶eftĀarγਊight\xe1οight\xe1ϊf;쀀\ud835\udd43erĀLRਢਬeftArrow;憙ightArrow;憘ƀchtਾੀੂ\xf2ࡌ;憰rok;䅁;扪Ѐacefiosuਗ਼੝੠੷੼અઋ઎p;椅y;䐜Ādl੥੯iumSpace;恟lintrf;愳r;쀀\ud835\udd10nusPlus;戓pf;쀀\ud835\udd44c\xf2੶;䎜ҀJacefostuણધભીଔଙඑ඗ඞcy;䐊cute;䅃ƀaey઴હાron;䅇dil;䅅;䐝ƀgswે૰଎ativeƀMTV૓૟૨ediumSpace;怋hiĀcn૦૘\xeb૙eryThi\xee૙tedĀGL૸ଆreaterGreate\xf2ٳessLes\xf3ੈLine;䀊r;쀀\ud835\udd11ȀBnptଢନଷ଺reak;恠BreakingSpace;䂠f;愕ڀ;CDEGHLNPRSTV୕ୖ୪୼஡௫ఄ౞಄ದ೘ൡඅ櫬Āou୛୤ngruent;扢pCap;扭oubleVerticalBar;戦ƀlqxஃஊ஛ement;戉ualĀ;Tஒஓ扠ilde;쀀≂̸ists;戄reater΀;EFGLSTஶஷ஽௉௓௘௥扯qual;扱ullEqual;쀀≧̸reater;쀀≫̸ess;批lantEqual;쀀⩾̸ilde;扵umpń௲௽ownHump;쀀≎̸qual;쀀≏̸eĀfsఊధtTriangleƀ;BEచఛడ拪ar;쀀⧏̸qual;括s̀;EGLSTవశ఼ౄోౘ扮qual;扰reater;扸ess;쀀≪̸lantEqual;쀀⩽̸ilde;扴estedĀGL౨౹reaterGreater;쀀⪢̸essLess;쀀⪡̸recedesƀ;ESಒಓಛ技qual;쀀⪯̸lantEqual;拠ĀeiಫಹverseElement;戌ghtTriangleƀ;BEೋೌ೒拫ar;쀀⧐̸qual;拭ĀquೝഌuareSuĀbp೨೹setĀ;E೰ೳ쀀⊏̸qual;拢ersetĀ;Eഃആ쀀⊐̸qual;拣ƀbcpഓതൎsetĀ;Eഛഞ쀀⊂⃒qual;抈ceedsȀ;ESTലള഻െ抁qual;쀀⪰̸lantEqual;拡ilde;쀀≿̸ersetĀ;E൘൛쀀⊃⃒qual;抉ildeȀ;EFT൮൯൵ൿ扁qual;扄ullEqual;扇ilde;扉erticalBar;戤cr;쀀\ud835\udca9ilde耻\xd1䃑;䎝܀Eacdfgmoprstuvලෂ෉෕ෛ෠෧෼ขภยา฿ไlig;䅒cute耻\xd3䃓Āiy෎ීrc耻\xd4䃔;䐞blac;䅐r;쀀\ud835\udd12rave耻\xd2䃒ƀaei෮ෲ෶cr;䅌ga;䎩cron;䎟pf;쀀\ud835\udd46enCurlyĀDQฎบoubleQuote;怜uote;怘;橔Āclวฬr;쀀\ud835\udcaaash耻\xd8䃘iŬื฼de耻\xd5䃕es;樷ml耻\xd6䃖erĀBP๋๠Āar๐๓r;怾acĀek๚๜;揞et;掴arenthesis;揜Ҁacfhilors๿ງຊຏຒດຝະ໼rtialD;戂y;䐟r;쀀\ud835\udd13i;䎦;䎠usMinus;䂱Āipຢອncareplan\xe5ڝf;愙Ȁ;eio຺ູ໠໤檻cedesȀ;EST່້໏໚扺qual;檯lantEqual;扼ilde;找me;怳Ādp໩໮uct;戏ortionĀ;aȥ໹l;戝Āci༁༆r;쀀\ud835\udcab;䎨ȀUfos༑༖༛༟OT耻"䀢r;쀀\ud835\udd14pf;愚cr;쀀\ud835\udcac؀BEacefhiorsu༾གྷཇའཱིྦྷྪྭ႖ႩႴႾarr;椐G耻\xae䂮ƀcnrཎནབute;䅔g;柫rĀ;tཛྷཝ憠l;椖ƀaeyཧཬཱron;䅘dil;䅖;䐠Ā;vླྀཹ愜erseĀEUྂྙĀlq྇ྎement;戋uilibrium;懋pEquilibrium;楯r\xbbཹo;䎡ghtЀACDFTUVa࿁࿫࿳ဢဨၛႇϘĀnr࿆࿒gleBracket;柩rowƀ;BL࿜࿝࿡憒ar;懥eftArrow;懄eiling;按oǵ࿹\0စbleBracket;柧nǔည\0နeeVector;楝ectorĀ;Bဝသ懂ar;楕loor;挋Āerိ၃eƀ;AVဵံြ抢rrow;憦ector;楛iangleƀ;BEၐၑၕ抳ar;槐qual;抵pƀDTVၣၮၸownVector;楏eeVector;楜ectorĀ;Bႂႃ憾ar;楔ectorĀ;B႑႒懀ar;楓Āpuႛ႞f;愝ndImplies;楰ightarrow;懛ĀchႹႼr;愛;憱leDelayed;槴ڀHOacfhimoqstuფჱჷჽᄙᄞᅑᅖᅡᅧᆵᆻᆿĀCcჩხHcy;䐩y;䐨FTcy;䐬cute;䅚ʀ;aeiyᄈᄉᄎᄓᄗ檼ron;䅠dil;䅞rc;䅜;䐡r;쀀\ud835\udd16ortȀDLRUᄪᄴᄾᅉownArrow\xbbОeftArrow\xbb࢚ightArrow\xbb࿝pArrow;憑gma;䎣allCircle;战pf;쀀\ud835\udd4aɲᅭ\0\0ᅰt;戚areȀ;ISUᅻᅼᆉᆯ斡ntersection;抓uĀbpᆏᆞsetĀ;Eᆗᆘ抏qual;抑ersetĀ;Eᆨᆩ抐qual;抒nion;抔cr;쀀\ud835\udcaear;拆ȀbcmpᇈᇛሉላĀ;sᇍᇎ拐etĀ;Eᇍᇕqual;抆ĀchᇠህeedsȀ;ESTᇭᇮᇴᇿ扻qual;檰lantEqual;扽ilde;承Th\xe1ྌ;我ƀ;esሒሓሣ拑rsetĀ;Eሜም抃qual;抇et\xbbሓրHRSacfhiorsሾቄ቉ቕ቞ቱቶኟዂወዑORN耻\xde䃞ADE;愢ĀHc቎ቒcy;䐋y;䐦Ābuቚቜ;䀉;䎤ƀaeyብቪቯron;䅤dil;䅢;䐢r;쀀\ud835\udd17Āeiቻ኉ǲኀ\0ኇefore;戴a;䎘Ācn኎ኘkSpace;쀀  Space;怉ldeȀ;EFTካኬኲኼ戼qual;扃ullEqual;扅ilde;扈pf;쀀\ud835\udd4bipleDot;惛Āctዖዛr;쀀\ud835\udcafrok;䅦ૡዷጎጚጦ\0ጬጱ\0\0\0\0\0ጸጽ፷ᎅ\0᏿ᐄᐊᐐĀcrዻጁute耻\xda䃚rĀ;oጇገ憟cir;楉rǣጓ\0጖y;䐎ve;䅬Āiyጞጣrc耻\xdb䃛;䐣blac;䅰r;쀀\ud835\udd18rave耻\xd9䃙acr;䅪Ādiፁ፩erĀBPፈ፝Āarፍፐr;䁟acĀekፗፙ;揟et;掵arenthesis;揝onĀ;P፰፱拃lus;抎Āgp፻፿on;䅲f;쀀\ud835\udd4cЀADETadps᎕ᎮᎸᏄϨᏒᏗᏳrrowƀ;BDᅐᎠᎤar;椒ownArrow;懅ownArrow;憕quilibrium;楮eeĀ;AᏋᏌ报rrow;憥own\xe1ϳerĀLRᏞᏨeftArrow;憖ightArrow;憗iĀ;lᏹᏺ䏒on;䎥ing;䅮cr;쀀\ud835\udcb0ilde;䅨ml耻\xdc䃜ҀDbcdefosvᐧᐬᐰᐳᐾᒅᒊᒐᒖash;披ar;櫫y;䐒ashĀ;lᐻᐼ抩;櫦Āerᑃᑅ;拁ƀbtyᑌᑐᑺar;怖Ā;iᑏᑕcalȀBLSTᑡᑥᑪᑴar;戣ine;䁼eparator;杘ilde;所ThinSpace;怊r;쀀\ud835\udd19pf;쀀\ud835\udd4dcr;쀀\ud835\udcb1dash;抪ʀcefosᒧᒬᒱᒶᒼirc;䅴dge;拀r;쀀\ud835\udd1apf;쀀\ud835\udd4ecr;쀀\ud835\udcb2Ȁfiosᓋᓐᓒᓘr;쀀\ud835\udd1b;䎞pf;쀀\ud835\udd4fcr;쀀\ud835\udcb3ҀAIUacfosuᓱᓵᓹᓽᔄᔏᔔᔚᔠcy;䐯cy;䐇cy;䐮cute耻\xdd䃝Āiyᔉᔍrc;䅶;䐫r;쀀\ud835\udd1cpf;쀀\ud835\udd50cr;쀀\ud835\udcb4ml;䅸ЀHacdefosᔵᔹᔿᕋᕏᕝᕠᕤcy;䐖cute;䅹Āayᕄᕉron;䅽;䐗ot;䅻ǲᕔ\0ᕛoWidt\xe8૙a;䎖r;愨pf;愤cr;쀀\ud835\udcb5௡ᖃᖊᖐ\0ᖰᖶᖿ\0\0\0\0ᗆᗛᗫᙟ᙭\0ᚕ᚛ᚲᚹ\0ᚾcute耻\xe1䃡reve;䄃̀;Ediuyᖜᖝᖡᖣᖨᖭ戾;쀀∾̳;房rc耻\xe2䃢te肻\xb4̆;䐰lig耻\xe6䃦Ā;r\xb2ᖺ;쀀\ud835\udd1erave耻\xe0䃠ĀepᗊᗖĀfpᗏᗔsym;愵\xe8ᗓha;䎱ĀapᗟcĀclᗤᗧr;䄁g;樿ɤᗰ\0\0ᘊʀ;adsvᗺᗻᗿᘁᘇ戧nd;橕;橜lope;橘;橚΀;elmrszᘘᘙᘛᘞᘿᙏᙙ戠;榤e\xbbᘙsdĀ;aᘥᘦ戡ѡᘰᘲᘴᘶᘸᘺᘼᘾ;榨;榩;榪;榫;榬;榭;榮;榯tĀ;vᙅᙆ戟bĀ;dᙌᙍ抾;榝Āptᙔᙗh;戢\xbb\xb9arr;捼Āgpᙣᙧon;䄅f;쀀\ud835\udd52΀;Eaeiop዁ᙻᙽᚂᚄᚇᚊ;橰cir;橯;扊d;手s;䀧roxĀ;e዁ᚒ\xf1ᚃing耻\xe5䃥ƀctyᚡᚦᚨr;쀀\ud835\udcb6;䀪mpĀ;e዁ᚯ\xf1ʈilde耻\xe3䃣ml耻\xe4䃤Āciᛂᛈonin\xf4ɲnt;樑ࠀNabcdefiklnoprsu᛭ᛱᜰ᜼ᝃᝈ᝸᝽០៦ᠹᡐᜍ᤽᥈ᥰot;櫭Ācrᛶ᜞kȀcepsᜀᜅᜍᜓong;扌psilon;䏶rime;怵imĀ;e᜚᜛戽q;拍Ŷᜢᜦee;抽edĀ;gᜬᜭ挅e\xbbᜭrkĀ;t፜᜷brk;掶Āoyᜁᝁ;䐱quo;怞ʀcmprtᝓ᝛ᝡᝤᝨausĀ;eĊĉptyv;榰s\xe9ᜌno\xf5ēƀahwᝯ᝱ᝳ;䎲;愶een;扬r;쀀\ud835\udd1fg΀costuvwឍឝឳេ៕៛៞ƀaiuបពរ\xf0ݠrc;旯p\xbb፱ƀdptឤឨឭot;樀lus;樁imes;樂ɱឹ\0\0ើcup;樆ar;昅riangleĀdu៍្own;施p;斳plus;樄e\xe5ᑄ\xe5ᒭarow;植ƀako៭ᠦᠵĀcn៲ᠣkƀlst៺֫᠂ozenge;槫riangleȀ;dlr᠒᠓᠘᠝斴own;斾eft;旂ight;斸k;搣Ʊᠫ\0ᠳƲᠯ\0ᠱ;斒;斑4;斓ck;斈ĀeoᠾᡍĀ;qᡃᡆ쀀=⃥uiv;쀀≡⃥t;挐Ȁptwxᡙᡞᡧᡬf;쀀\ud835\udd53Ā;tᏋᡣom\xbbᏌtie;拈؀DHUVbdhmptuvᢅᢖᢪᢻᣗᣛᣬ᣿ᤅᤊᤐᤡȀLRlrᢎᢐᢒᢔ;敗;敔;敖;敓ʀ;DUduᢡᢢᢤᢦᢨ敐;敦;敩;敤;敧ȀLRlrᢳᢵᢷᢹ;敝;敚;敜;教΀;HLRhlrᣊᣋᣍᣏᣑᣓᣕ救;敬;散;敠;敫;敢;敟ox;槉ȀLRlrᣤᣦᣨᣪ;敕;敒;攐;攌ʀ;DUduڽ᣷᣹᣻᣽;敥;敨;攬;攴inus;抟lus;択imes;抠ȀLRlrᤙᤛᤝ᤟;敛;敘;攘;攔΀;HLRhlrᤰᤱᤳᤵᤷ᤻᤹攂;敪;敡;敞;攼;攤;攜Āevģ᥂bar耻\xa6䂦Ȁceioᥑᥖᥚᥠr;쀀\ud835\udcb7mi;恏mĀ;e᜚᜜lƀ;bhᥨᥩᥫ䁜;槅sub;柈Ŭᥴ᥾lĀ;e᥹᥺怢t\xbb᥺pƀ;Eeįᦅᦇ;檮Ā;qۜۛೡᦧ\0᧨ᨑᨕᨲ\0ᨷᩐ\0\0᪴\0\0᫁\0\0ᬡᬮ᭍᭒\0᯽\0ᰌƀcpr᦭ᦲ᧝ute;䄇̀;abcdsᦿᧀᧄ᧊᧕᧙戩nd;橄rcup;橉Āau᧏᧒p;橋p;橇ot;橀;쀀∩︀Āeo᧢᧥t;恁\xeeړȀaeiu᧰᧻ᨁᨅǰ᧵\0᧸s;橍on;䄍dil耻\xe7䃧rc;䄉psĀ;sᨌᨍ橌m;橐ot;䄋ƀdmnᨛᨠᨦil肻\xb8ƭptyv;榲t脀\xa2;eᨭᨮ䂢r\xe4Ʋr;쀀\ud835\udd20ƀceiᨽᩀᩍy;䑇ckĀ;mᩇᩈ朓ark\xbbᩈ;䏇r΀;Ecefms᩟᩠ᩢᩫ᪤᪪᪮旋;槃ƀ;elᩩᩪᩭ䋆q;扗eɡᩴ\0\0᪈rrowĀlr᩼᪁eft;憺ight;憻ʀRSacd᪒᪔᪖᪚᪟\xbbཇ;擈st;抛irc;抚ash;抝nint;樐id;櫯cir;槂ubsĀ;u᪻᪼晣it\xbb᪼ˬ᫇᫔᫺\0ᬊonĀ;eᫍᫎ䀺Ā;q\xc7\xc6ɭ᫙\0\0᫢aĀ;t᫞᫟䀬;䁀ƀ;fl᫨᫩᫫戁\xeeᅠeĀmx᫱᫶ent\xbb᫩e\xf3ɍǧ᫾\0ᬇĀ;dኻᬂot;橭n\xf4Ɇƀfryᬐᬔᬗ;쀀\ud835\udd54o\xe4ɔ脀\xa9;sŕᬝr;愗Āaoᬥᬩrr;憵ss;朗Ācuᬲᬷr;쀀\ud835\udcb8Ābpᬼ᭄Ā;eᭁᭂ櫏;櫑Ā;eᭉᭊ櫐;櫒dot;拯΀delprvw᭠᭬᭷ᮂᮬᯔ᯹arrĀlr᭨᭪;椸;椵ɰ᭲\0\0᭵r;拞c;拟arrĀ;p᭿ᮀ憶;椽̀;bcdosᮏᮐᮖᮡᮥᮨ截rcap;橈Āauᮛᮞp;橆p;橊ot;抍r;橅;쀀∪︀Ȁalrv᮵ᮿᯞᯣrrĀ;mᮼᮽ憷;椼yƀevwᯇᯔᯘqɰᯎ\0\0ᯒre\xe3᭳u\xe3᭵ee;拎edge;拏en耻\xa4䂤earrowĀlrᯮ᯳eft\xbbᮀight\xbbᮽe\xe4ᯝĀciᰁᰇonin\xf4Ƿnt;戱lcty;挭ঀAHabcdefhijlorstuwz᰸᰻᰿ᱝᱩᱵᲊᲞᲬᲷ᳻᳿ᴍᵻᶑᶫᶻ᷆᷍r\xf2΁ar;楥Ȁglrs᱈ᱍ᱒᱔ger;怠eth;愸\xf2ᄳhĀ;vᱚᱛ怐\xbbऊūᱡᱧarow;椏a\xe3̕Āayᱮᱳron;䄏;䐴ƀ;ao̲ᱼᲄĀgrʿᲁr;懊tseq;橷ƀglmᲑᲔᲘ耻\xb0䂰ta;䎴ptyv;榱ĀirᲣᲨsht;楿;쀀\ud835\udd21arĀlrᲳᲵ\xbbࣜ\xbbသʀaegsv᳂͸᳖᳜᳠mƀ;oș᳊᳔ndĀ;ș᳑uit;晦amma;䏝in;拲ƀ;io᳧᳨᳸䃷de脀\xf7;o᳧ᳰntimes;拇n\xf8᳷cy;䑒cɯᴆ\0\0ᴊrn;挞op;挍ʀlptuwᴘᴝᴢᵉᵕlar;䀤f;쀀\ud835\udd55ʀ;emps̋ᴭᴷᴽᵂqĀ;d͒ᴳot;扑inus;戸lus;戔quare;抡blebarwedg\xe5\xfanƀadhᄮᵝᵧownarrow\xf3ᲃarpoonĀlrᵲᵶef\xf4Ჴigh\xf4ᲶŢᵿᶅkaro\xf7གɯᶊ\0\0ᶎrn;挟op;挌ƀcotᶘᶣᶦĀryᶝᶡ;쀀\ud835\udcb9;䑕l;槶rok;䄑Ādrᶰᶴot;拱iĀ;fᶺ᠖斿Āah᷀᷃r\xf2Щa\xf2ྦangle;榦Āci᷒ᷕy;䑟grarr;柿ऀDacdefglmnopqrstuxḁḉḙḸոḼṉṡṾấắẽỡἪἷὄ὎὚ĀDoḆᴴo\xf4ᲉĀcsḎḔute耻\xe9䃩ter;橮ȀaioyḢḧḱḶron;䄛rĀ;cḭḮ扖耻\xea䃪lon;払;䑍ot;䄗ĀDrṁṅot;扒;쀀\ud835\udd22ƀ;rsṐṑṗ檚ave耻\xe8䃨Ā;dṜṝ檖ot;檘Ȁ;ilsṪṫṲṴ檙nters;揧;愓Ā;dṹṺ檕ot;檗ƀapsẅẉẗcr;䄓tyƀ;svẒẓẕ戅et\xbbẓpĀ1;ẝẤĳạả;怄;怅怃ĀgsẪẬ;䅋p;怂ĀgpẴẸon;䄙f;쀀\ud835\udd56ƀalsỄỎỒrĀ;sỊị拕l;槣us;橱iƀ;lvỚớở䎵on\xbbớ;䏵ȀcsuvỪỳἋἣĀioữḱrc\xbbḮɩỹ\0\0ỻ\xedՈantĀglἂἆtr\xbbṝess\xbbṺƀaeiἒ἖Ἒls;䀽st;扟vĀ;DȵἠD;橸parsl;槥ĀDaἯἳot;打rr;楱ƀcdiἾὁỸr;愯o\xf4͒ĀahὉὋ;䎷耻\xf0䃰Āmrὓὗl耻\xeb䃫o;悬ƀcipὡὤὧl;䀡s\xf4ծĀeoὬὴctatio\xeeՙnential\xe5չৡᾒ\0ᾞ\0ᾡᾧ\0\0ῆῌ\0ΐ\0ῦῪ \0 ⁚llingdotse\xf1Ṅy;䑄male;晀ƀilrᾭᾳ῁lig;耀ﬃɩᾹ\0\0᾽g;耀ﬀig;耀ﬄ;쀀\ud835\udd23lig;耀ﬁlig;쀀fjƀaltῙ῜ῡt;晭ig;耀ﬂns;斱of;䆒ǰ΅\0ῳf;쀀\ud835\udd57ĀakֿῷĀ;vῼ´拔;櫙artint;樍Āao‌⁕Ācs‑⁒α‚‰‸⁅⁈\0⁐β•‥‧‪‬\0‮耻\xbd䂽;慓耻\xbc䂼;慕;慙;慛Ƴ‴\0‶;慔;慖ʴ‾⁁\0\0⁃耻\xbe䂾;慗;慜5;慘ƶ⁌\0⁎;慚;慝8;慞l;恄wn;挢cr;쀀\ud835\udcbbࢀEabcdefgijlnorstv₂₉₟₥₰₴⃰⃵⃺⃿℃ℒℸ̗ℾ⅒↞Ā;lٍ₇;檌ƀcmpₐₕ₝ute;䇵maĀ;dₜ᳚䎳;檆reve;䄟Āiy₪₮rc;䄝;䐳ot;䄡Ȁ;lqsؾق₽⃉ƀ;qsؾٌ⃄lan\xf4٥Ȁ;cdl٥⃒⃥⃕c;檩otĀ;o⃜⃝檀Ā;l⃢⃣檂;檄Ā;e⃪⃭쀀⋛︀s;檔r;쀀\ud835\udd24Ā;gٳ؛mel;愷cy;䑓Ȁ;Eajٚℌℎℐ;檒;檥;檤ȀEaesℛℝ℩ℴ;扩pĀ;p℣ℤ檊rox\xbbℤĀ;q℮ℯ檈Ā;q℮ℛim;拧pf;쀀\ud835\udd58Āci⅃ⅆr;愊mƀ;el٫ⅎ⅐;檎;檐茀>;cdlqr׮ⅠⅪⅮⅳⅹĀciⅥⅧ;檧r;橺ot;拗Par;榕uest;橼ʀadelsↄⅪ←ٖ↛ǰ↉\0↎pro\xf8₞r;楸qĀlqؿ↖les\xf3₈i\xed٫Āen↣↭rtneqq;쀀≩︀\xc5↪ԀAabcefkosy⇄⇇⇱⇵⇺∘∝∯≨≽r\xf2ΠȀilmr⇐⇔⇗⇛rs\xf0ᒄf\xbb․il\xf4کĀdr⇠⇤cy;䑊ƀ;cwࣴ⇫⇯ir;楈;憭ar;意irc;䄥ƀalr∁∎∓rtsĀ;u∉∊晥it\xbb∊lip;怦con;抹r;쀀\ud835\udd25sĀew∣∩arow;椥arow;椦ʀamopr∺∾≃≞≣rr;懿tht;戻kĀlr≉≓eftarrow;憩ightarrow;憪f;쀀\ud835\udd59bar;怕ƀclt≯≴≸r;쀀\ud835\udcbdas\xe8⇴rok;䄧Ābp⊂⊇ull;恃hen\xbbᱛૡ⊣\0⊪\0⊸⋅⋎\0⋕⋳\0\0⋸⌢⍧⍢⍿\0⎆⎪⎴cute耻\xed䃭ƀ;iyݱ⊰⊵rc耻\xee䃮;䐸Ācx⊼⊿y;䐵cl耻\xa1䂡ĀfrΟ⋉;쀀\ud835\udd26rave耻\xec䃬Ȁ;inoܾ⋝⋩⋮Āin⋢⋦nt;樌t;戭fin;槜ta;愩lig;䄳ƀaop⋾⌚⌝ƀcgt⌅⌈⌗r;䄫ƀelpܟ⌏⌓in\xe5ގar\xf4ܠh;䄱f;抷ed;䆵ʀ;cfotӴ⌬⌱⌽⍁are;愅inĀ;t⌸⌹戞ie;槝do\xf4⌙ʀ;celpݗ⍌⍐⍛⍡al;抺Āgr⍕⍙er\xf3ᕣ\xe3⍍arhk;樗rod;樼Ȁcgpt⍯⍲⍶⍻y;䑑on;䄯f;쀀\ud835\udd5aa;䎹uest耻\xbf䂿Āci⎊⎏r;쀀\ud835\udcbenʀ;EdsvӴ⎛⎝⎡ӳ;拹ot;拵Ā;v⎦⎧拴;拳Ā;iݷ⎮lde;䄩ǫ⎸\0⎼cy;䑖l耻\xef䃯̀cfmosu⏌⏗⏜⏡⏧⏵Āiy⏑⏕rc;䄵;䐹r;쀀\ud835\udd27ath;䈷pf;쀀\ud835\udd5bǣ⏬\0⏱r;쀀\ud835\udcbfrcy;䑘kcy;䑔Ѐacfghjos␋␖␢␧␭␱␵␻ppaĀ;v␓␔䎺;䏰Āey␛␠dil;䄷;䐺r;쀀\ud835\udd28reen;䄸cy;䑅cy;䑜pf;쀀\ud835\udd5ccr;쀀\ud835\udcc0஀ABEHabcdefghjlmnoprstuv⑰⒁⒆⒍⒑┎┽╚▀♎♞♥♹♽⚚⚲⛘❝❨➋⟀⠁⠒ƀart⑷⑺⑼r\xf2৆\xf2Εail;椛arr;椎Ā;gঔ⒋;檋ar;楢ॣ⒥\0⒪\0⒱\0\0\0\0\0⒵Ⓔ\0ⓆⓈⓍ\0⓹ute;䄺mptyv;榴ra\xeeࡌbda;䎻gƀ;dlࢎⓁⓃ;榑\xe5ࢎ;檅uo耻\xab䂫rЀ;bfhlpst࢙ⓞⓦⓩ⓫⓮⓱⓵Ā;f࢝ⓣs;椟s;椝\xeb≒p;憫l;椹im;楳l;憢ƀ;ae⓿─┄檫il;椙Ā;s┉┊檭;쀀⪭︀ƀabr┕┙┝rr;椌rk;杲Āak┢┬cĀek┨┪;䁻;䁛Āes┱┳;榋lĀdu┹┻;榏;榍Ȁaeuy╆╋╖╘ron;䄾Ādi═╔il;䄼\xecࢰ\xe2┩;䐻Ȁcqrs╣╦╭╽a;椶uoĀ;rนᝆĀdu╲╷har;楧shar;楋h;憲ʀ;fgqs▋▌উ◳◿扤tʀahlrt▘▤▷◂◨rrowĀ;t࢙□a\xe9⓶arpoonĀdu▯▴own\xbbњp\xbb०eftarrows;懇ightƀahs◍◖◞rrowĀ;sࣴࢧarpoon\xf3྘quigarro\xf7⇰hreetimes;拋ƀ;qs▋ও◺lan\xf4বʀ;cdgsব☊☍☝☨c;檨otĀ;o☔☕橿Ā;r☚☛檁;檃Ā;e☢☥쀀⋚︀s;檓ʀadegs☳☹☽♉♋ppro\xf8Ⓠot;拖qĀgq♃♅\xf4উgt\xf2⒌\xf4ছi\xedলƀilr♕࣡♚sht;楼;쀀\ud835\udd29Ā;Eজ♣;檑š♩♶rĀdu▲♮Ā;l॥♳;楪lk;斄cy;䑙ʀ;achtੈ⚈⚋⚑⚖r\xf2◁orne\xf2ᴈard;楫ri;旺Āio⚟⚤dot;䅀ustĀ;a⚬⚭掰che\xbb⚭ȀEaes⚻⚽⛉⛔;扨pĀ;p⛃⛄檉rox\xbb⛄Ā;q⛎⛏檇Ā;q⛎⚻im;拦Ѐabnoptwz⛩⛴⛷✚✯❁❇❐Ānr⛮⛱g;柬r;懽r\xebࣁgƀlmr⛿✍✔eftĀar০✇ight\xe1৲apsto;柼ight\xe1৽parrowĀlr✥✩ef\xf4⓭ight;憬ƀafl✶✹✽r;榅;쀀\ud835\udd5dus;樭imes;樴š❋❏st;戗\xe1ፎƀ;ef❗❘᠀旊nge\xbb❘arĀ;l❤❥䀨t;榓ʀachmt❳❶❼➅➇r\xf2ࢨorne\xf2ᶌarĀ;d྘➃;業;怎ri;抿̀achiqt➘➝ੀ➢➮➻quo;怹r;쀀\ud835\udcc1mƀ;egল➪➬;檍;檏Ābu┪➳oĀ;rฟ➹;怚rok;䅂萀<;cdhilqrࠫ⟒☹⟜⟠⟥⟪⟰Āci⟗⟙;檦r;橹re\xe5◲mes;拉arr;楶uest;橻ĀPi⟵⟹ar;榖ƀ;ef⠀भ᠛旃rĀdu⠇⠍shar;楊har;楦Āen⠗⠡rtneqq;쀀≨︀\xc5⠞܀Dacdefhilnopsu⡀⡅⢂⢎⢓⢠⢥⢨⣚⣢⣤ઃ⣳⤂Dot;戺Ȁclpr⡎⡒⡣⡽r耻\xaf䂯Āet⡗⡙;時Ā;e⡞⡟朠se\xbb⡟Ā;sျ⡨toȀ;dluျ⡳⡷⡻ow\xeeҌef\xf4ए\xf0Ꮡker;斮Āoy⢇⢌mma;権;䐼ash;怔asuredangle\xbbᘦr;쀀\ud835\udd2ao;愧ƀcdn⢯⢴⣉ro耻\xb5䂵Ȁ;acdᑤ⢽⣀⣄s\xf4ᚧir;櫰ot肻\xb7Ƶusƀ;bd⣒ᤃ⣓戒Ā;uᴼ⣘;横ţ⣞⣡p;櫛\xf2−\xf0ઁĀdp⣩⣮els;抧f;쀀\ud835\udd5eĀct⣸⣽r;쀀\ud835\udcc2pos\xbbᖝƀ;lm⤉⤊⤍䎼timap;抸ఀGLRVabcdefghijlmoprstuvw⥂⥓⥾⦉⦘⧚⧩⨕⨚⩘⩝⪃⪕⪤⪨⬄⬇⭄⭿⮮ⰴⱧⱼ⳩Āgt⥇⥋;쀀⋙̸Ā;v⥐௏쀀≫⃒ƀelt⥚⥲⥶ftĀar⥡⥧rrow;懍ightarrow;懎;쀀⋘̸Ā;v⥻ే쀀≪⃒ightarrow;懏ĀDd⦎⦓ash;抯ash;抮ʀbcnpt⦣⦧⦬⦱⧌la\xbb˞ute;䅄g;쀀∠⃒ʀ;Eiop඄⦼⧀⧅⧈;쀀⩰̸d;쀀≋̸s;䅉ro\xf8඄urĀ;a⧓⧔普lĀ;s⧓ସǳ⧟\0⧣p肻\xa0ଷmpĀ;e௹ఀʀaeouy⧴⧾⨃⨐⨓ǰ⧹\0⧻;橃on;䅈dil;䅆ngĀ;dൾ⨊ot;쀀⩭̸p;橂;䐽ash;怓΀;Aadqsxஒ⨩⨭⨻⩁⩅⩐rr;懗rĀhr⨳⨶k;椤Ā;oᏲᏰot;쀀≐̸ui\xf6ୣĀei⩊⩎ar;椨\xed஘istĀ;s஠டr;쀀\ud835\udd2bȀEest௅⩦⩹⩼ƀ;qs஼⩭௡ƀ;qs஼௅⩴lan\xf4௢i\xed௪Ā;rஶ⪁\xbbஷƀAap⪊⪍⪑r\xf2⥱rr;憮ar;櫲ƀ;svྍ⪜ྌĀ;d⪡⪢拼;拺cy;䑚΀AEadest⪷⪺⪾⫂⫅⫶⫹r\xf2⥦;쀀≦̸rr;憚r;急Ȁ;fqs఻⫎⫣⫯tĀar⫔⫙rro\xf7⫁ightarro\xf7⪐ƀ;qs఻⪺⫪lan\xf4ౕĀ;sౕ⫴\xbbశi\xedౝĀ;rవ⫾iĀ;eచథi\xe4ඐĀpt⬌⬑f;쀀\ud835\udd5f膀\xac;in⬙⬚⬶䂬nȀ;Edvஉ⬤⬨⬮;쀀⋹̸ot;쀀⋵̸ǡஉ⬳⬵;拷;拶iĀ;vಸ⬼ǡಸ⭁⭃;拾;拽ƀaor⭋⭣⭩rȀ;ast୻⭕⭚⭟lle\xec୻l;쀀⫽⃥;쀀∂̸lint;樔ƀ;ceಒ⭰⭳u\xe5ಥĀ;cಘ⭸Ā;eಒ⭽\xf1ಘȀAait⮈⮋⮝⮧r\xf2⦈rrƀ;cw⮔⮕⮙憛;쀀⤳̸;쀀↝̸ghtarrow\xbb⮕riĀ;eೋೖ΀chimpqu⮽⯍⯙⬄୸⯤⯯Ȁ;cerല⯆ഷ⯉u\xe5൅;쀀\ud835\udcc3ortɭ⬅\0\0⯖ar\xe1⭖mĀ;e൮⯟Ā;q൴൳suĀbp⯫⯭\xe5೸\xe5ഋƀbcp⯶ⰑⰙȀ;Ees⯿ⰀഢⰄ抄;쀀⫅̸etĀ;eഛⰋqĀ;qണⰀcĀ;eലⰗ\xf1സȀ;EesⰢⰣൟⰧ抅;쀀⫆̸etĀ;e൘ⰮqĀ;qൠⰣȀgilrⰽⰿⱅⱇ\xecௗlde耻\xf1䃱\xe7ృiangleĀlrⱒⱜeftĀ;eచⱚ\xf1దightĀ;eೋⱥ\xf1೗Ā;mⱬⱭ䎽ƀ;esⱴⱵⱹ䀣ro;愖p;怇ҀDHadgilrsⲏⲔⲙⲞⲣⲰⲶⳓⳣash;抭arr;椄p;쀀≍⃒ash;抬ĀetⲨⲬ;쀀≥⃒;쀀>⃒nfin;槞ƀAetⲽⳁⳅrr;椂;쀀≤⃒Ā;rⳊⳍ쀀<⃒ie;쀀⊴⃒ĀAtⳘⳜrr;椃rie;쀀⊵⃒im;쀀∼⃒ƀAan⳰⳴ⴂrr;懖rĀhr⳺⳽k;椣Ā;oᏧᏥear;椧ቓ᪕\0\0\0\0\0\0\0\0\0\0\0\0\0ⴭ\0ⴸⵈⵠⵥ⵲ⶄᬇ\0\0ⶍⶫ\0ⷈⷎ\0ⷜ⸙⸫⸾⹃Ācsⴱ᪗ute耻\xf3䃳ĀiyⴼⵅrĀ;c᪞ⵂ耻\xf4䃴;䐾ʀabios᪠ⵒⵗǈⵚlac;䅑v;樸old;榼lig;䅓Ācr⵩⵭ir;榿;쀀\ud835\udd2cͯ⵹\0\0⵼\0ⶂn;䋛ave耻\xf2䃲;槁Ābmⶈ෴ar;榵Ȁacitⶕ⶘ⶥⶨr\xf2᪀Āir⶝ⶠr;榾oss;榻n\xe5๒;槀ƀaeiⶱⶵⶹcr;䅍ga;䏉ƀcdnⷀⷅǍron;䎿;榶pf;쀀\ud835\udd60ƀaelⷔ⷗ǒr;榷rp;榹΀;adiosvⷪⷫⷮ⸈⸍⸐⸖戨r\xf2᪆Ȁ;efmⷷⷸ⸂⸅橝rĀ;oⷾⷿ愴f\xbbⷿ耻\xaa䂪耻\xba䂺gof;抶r;橖lope;橗;橛ƀclo⸟⸡⸧\xf2⸁ash耻\xf8䃸l;折iŬⸯ⸴de耻\xf5䃵esĀ;aǛ⸺s;樶ml耻\xf6䃶bar;挽ૡ⹞\0⹽\0⺀⺝\0⺢⺹\0\0⻋ຜ\0⼓\0\0⼫⾼\0⿈rȀ;astЃ⹧⹲຅脀\xb6;l⹭⹮䂶le\xecЃɩ⹸\0\0⹻m;櫳;櫽y;䐿rʀcimpt⺋⺏⺓ᡥ⺗nt;䀥od;䀮il;怰enk;怱r;쀀\ud835\udd2dƀimo⺨⺰⺴Ā;v⺭⺮䏆;䏕ma\xf4੶ne;明ƀ;tv⺿⻀⻈䏀chfork\xbb´;䏖Āau⻏⻟nĀck⻕⻝kĀ;h⇴⻛;愎\xf6⇴sҀ;abcdemst⻳⻴ᤈ⻹⻽⼄⼆⼊⼎䀫cir;樣ir;樢Āouᵀ⼂;樥;橲n肻\xb1ຝim;樦wo;樧ƀipu⼙⼠⼥ntint;樕f;쀀\ud835\udd61nd耻\xa3䂣Ԁ;Eaceinosu່⼿⽁⽄⽇⾁⾉⾒⽾⾶;檳p;檷u\xe5໙Ā;c໎⽌̀;acens່⽙⽟⽦⽨⽾ppro\xf8⽃urlye\xf1໙\xf1໎ƀaes⽯⽶⽺pprox;檹qq;檵im;拨i\xedໟmeĀ;s⾈ຮ怲ƀEas⽸⾐⽺\xf0⽵ƀdfp໬⾙⾯ƀals⾠⾥⾪lar;挮ine;挒urf;挓Ā;t໻⾴\xef໻rel;抰Āci⿀⿅r;쀀\ud835\udcc5;䏈ncsp;怈̀fiopsu⿚⋢⿟⿥⿫⿱r;쀀\ud835\udd2epf;쀀\ud835\udd62rime;恗cr;쀀\ud835\udcc6ƀaeo⿸〉〓tĀei⿾々rnion\xf3ڰnt;樖stĀ;e【】䀿\xf1Ἑ\xf4༔઀ABHabcdefhilmnoprstux぀けさすムㄎㄫㅇㅢㅲㆎ㈆㈕㈤㈩㉘㉮㉲㊐㊰㊷ƀartぇおがr\xf2Ⴓ\xf2ϝail;検ar\xf2ᱥar;楤΀cdenqrtとふへみわゔヌĀeuねぱ;쀀∽̱te;䅕i\xe3ᅮmptyv;榳gȀ;del࿑らるろ;榒;榥\xe5࿑uo耻\xbb䂻rր;abcfhlpstw࿜ガクシスゼゾダッデナp;極Ā;f࿠ゴs;椠;椳s;椞\xeb≝\xf0✮l;楅im;楴l;憣;憝Āaiパフil;椚oĀ;nホボ戶al\xf3༞ƀabrョリヮr\xf2៥rk;杳ĀakンヽcĀekヹ・;䁽;䁝Āes㄂㄄;榌lĀduㄊㄌ;榎;榐Ȁaeuyㄗㄜㄧㄩron;䅙Ādiㄡㄥil;䅗\xec࿲\xe2ヺ;䑀Ȁclqsㄴㄷㄽㅄa;椷dhar;楩uoĀ;rȎȍh;憳ƀacgㅎㅟངlȀ;ipsླྀㅘㅛႜn\xe5Ⴛar\xf4ྩt;断ƀilrㅩဣㅮsht;楽;쀀\ud835\udd2fĀaoㅷㆆrĀduㅽㅿ\xbbѻĀ;l႑ㆄ;楬Ā;vㆋㆌ䏁;䏱ƀgns㆕ㇹㇼht̀ahlrstㆤㆰ㇂㇘㇤㇮rrowĀ;t࿜ㆭa\xe9トarpoonĀduㆻㆿow\xeeㅾp\xbb႒eftĀah㇊㇐rrow\xf3࿪arpoon\xf3Ցightarrows;應quigarro\xf7ニhreetimes;拌g;䋚ingdotse\xf1ἲƀahm㈍㈐㈓r\xf2࿪a\xf2Ց;怏oustĀ;a㈞㈟掱che\xbb㈟mid;櫮Ȁabpt㈲㈽㉀㉒Ānr㈷㈺g;柭r;懾r\xebဃƀafl㉇㉊㉎r;榆;쀀\ud835\udd63us;樮imes;樵Āap㉝㉧rĀ;g㉣㉤䀩t;榔olint;樒ar\xf2㇣Ȁachq㉻㊀Ⴜ㊅quo;怺r;쀀\ud835\udcc7Ābu・㊊oĀ;rȔȓƀhir㊗㊛㊠re\xe5ㇸmes;拊iȀ;efl㊪ၙᠡ㊫方tri;槎luhar;楨;愞ൡ㋕㋛㋟㌬㌸㍱\0㍺㎤\0\0㏬㏰\0㐨㑈㑚㒭㒱㓊㓱\0㘖\0\0㘳cute;䅛qu\xef➺Ԁ;Eaceinpsyᇭ㋳㋵㋿㌂㌋㌏㌟㌦㌩;檴ǰ㋺\0㋼;檸on;䅡u\xe5ᇾĀ;dᇳ㌇il;䅟rc;䅝ƀEas㌖㌘㌛;檶p;檺im;择olint;樓i\xedሄ;䑁otƀ;be㌴ᵇ㌵担;橦΀Aacmstx㍆㍊㍗㍛㍞㍣㍭rr;懘rĀhr㍐㍒\xeb∨Ā;oਸ਼਴t耻\xa7䂧i;䀻war;椩mĀin㍩\xf0nu\xf3\xf1t;朶rĀ;o㍶⁕쀀\ud835\udd30Ȁacoy㎂㎆㎑㎠rp;景Āhy㎋㎏cy;䑉;䑈rtɭ㎙\0\0㎜i\xe4ᑤara\xec⹯耻\xad䂭Āgm㎨㎴maƀ;fv㎱㎲㎲䏃;䏂Ѐ;deglnprካ㏅㏉㏎㏖㏞㏡㏦ot;橪Ā;q኱ኰĀ;E㏓㏔檞;檠Ā;E㏛㏜檝;檟e;扆lus;樤arr;楲ar\xf2ᄽȀaeit㏸㐈㐏㐗Āls㏽㐄lsetm\xe9㍪hp;樳parsl;槤Ādlᑣ㐔e;挣Ā;e㐜㐝檪Ā;s㐢㐣檬;쀀⪬︀ƀflp㐮㐳㑂tcy;䑌Ā;b㐸㐹䀯Ā;a㐾㐿槄r;挿f;쀀\ud835\udd64aĀdr㑍ЂesĀ;u㑔㑕晠it\xbb㑕ƀcsu㑠㑹㒟Āau㑥㑯pĀ;sᆈ㑫;쀀⊓︀pĀ;sᆴ㑵;쀀⊔︀uĀbp㑿㒏ƀ;esᆗᆜ㒆etĀ;eᆗ㒍\xf1ᆝƀ;esᆨᆭ㒖etĀ;eᆨ㒝\xf1ᆮƀ;afᅻ㒦ְrť㒫ֱ\xbbᅼar\xf2ᅈȀcemt㒹㒾㓂㓅r;쀀\ud835\udcc8tm\xee\xf1i\xec㐕ar\xe6ᆾĀar㓎㓕rĀ;f㓔ឿ昆Āan㓚㓭ightĀep㓣㓪psilo\xeeỠh\xe9⺯s\xbb⡒ʀbcmnp㓻㕞ሉ㖋㖎Ҁ;Edemnprs㔎㔏㔑㔕㔞㔣㔬㔱㔶抂;櫅ot;檽Ā;dᇚ㔚ot;櫃ult;櫁ĀEe㔨㔪;櫋;把lus;檿arr;楹ƀeiu㔽㕒㕕tƀ;en㔎㕅㕋qĀ;qᇚ㔏eqĀ;q㔫㔨m;櫇Ābp㕚㕜;櫕;櫓c̀;acensᇭ㕬㕲㕹㕻㌦ppro\xf8㋺urlye\xf1ᇾ\xf1ᇳƀaes㖂㖈㌛ppro\xf8㌚q\xf1㌗g;晪ڀ123;Edehlmnps㖩㖬㖯ሜ㖲㖴㗀㗉㗕㗚㗟㗨㗭耻\xb9䂹耻\xb2䂲耻\xb3䂳;櫆Āos㖹㖼t;檾ub;櫘Ā;dሢ㗅ot;櫄sĀou㗏㗒l;柉b;櫗arr;楻ult;櫂ĀEe㗤㗦;櫌;抋lus;櫀ƀeiu㗴㘉㘌tƀ;enሜ㗼㘂qĀ;qሢ㖲eqĀ;q㗧㗤m;櫈Ābp㘑㘓;櫔;櫖ƀAan㘜㘠㘭rr;懙rĀhr㘦㘨\xeb∮Ā;oਫ਩war;椪lig耻\xdf䃟௡㙑㙝㙠ዎ㙳㙹\0㙾㛂\0\0\0\0\0㛛㜃\0㜉㝬\0\0\0㞇ɲ㙖\0\0㙛get;挖;䏄r\xeb๟ƀaey㙦㙫㙰ron;䅥dil;䅣;䑂lrec;挕r;쀀\ud835\udd31Ȁeiko㚆㚝㚵㚼ǲ㚋\0㚑eĀ4fኄኁaƀ;sv㚘㚙㚛䎸ym;䏑Ācn㚢㚲kĀas㚨㚮ppro\xf8዁im\xbbኬs\xf0ኞĀas㚺㚮\xf0዁rn耻\xfe䃾Ǭ̟㛆⋧es膀\xd7;bd㛏㛐㛘䃗Ā;aᤏ㛕r;樱;樰ƀeps㛡㛣㜀\xe1⩍Ȁ;bcf҆㛬㛰㛴ot;挶ir;櫱Ā;o㛹㛼쀀\ud835\udd65rk;櫚\xe1㍢rime;怴ƀaip㜏㜒㝤d\xe5ቈ΀adempst㜡㝍㝀㝑㝗㝜㝟ngleʀ;dlqr㜰㜱㜶㝀㝂斵own\xbbᶻeftĀ;e⠀㜾\xf1म;扜ightĀ;e㊪㝋\xf1ၚot;旬inus;樺lus;樹b;槍ime;樻ezium;揢ƀcht㝲㝽㞁Āry㝷㝻;쀀\ud835\udcc9;䑆cy;䑛rok;䅧Āio㞋㞎x\xf4᝷headĀlr㞗㞠eftarro\xf7ࡏightarrow\xbbཝऀAHabcdfghlmoprstuw㟐㟓㟗㟤㟰㟼㠎㠜㠣㠴㡑㡝㡫㢩㣌㣒㣪㣶r\xf2ϭar;楣Ācr㟜㟢ute耻\xfa䃺\xf2ᅐrǣ㟪\0㟭y;䑞ve;䅭Āiy㟵㟺rc耻\xfb䃻;䑃ƀabh㠃㠆㠋r\xf2Ꭽlac;䅱a\xf2ᏃĀir㠓㠘sht;楾;쀀\ud835\udd32rave耻\xf9䃹š㠧㠱rĀlr㠬㠮\xbbॗ\xbbႃlk;斀Āct㠹㡍ɯ㠿\0\0㡊rnĀ;e㡅㡆挜r\xbb㡆op;挏ri;旸Āal㡖㡚cr;䅫肻\xa8͉Āgp㡢㡦on;䅳f;쀀\ud835\udd66̀adhlsuᅋ㡸㡽፲㢑㢠own\xe1ᎳarpoonĀlr㢈㢌ef\xf4㠭igh\xf4㠯iƀ;hl㢙㢚㢜䏅\xbbᏺon\xbb㢚parrows;懈ƀcit㢰㣄㣈ɯ㢶\0\0㣁rnĀ;e㢼㢽挝r\xbb㢽op;挎ng;䅯ri;旹cr;쀀\ud835\udccaƀdir㣙㣝㣢ot;拰lde;䅩iĀ;f㜰㣨\xbb᠓Āam㣯㣲r\xf2㢨l耻\xfc䃼angle;榧ހABDacdeflnoprsz㤜㤟㤩㤭㦵㦸㦽㧟㧤㧨㧳㧹㧽㨁㨠r\xf2ϷarĀ;v㤦㤧櫨;櫩as\xe8ϡĀnr㤲㤷grt;榜΀eknprst㓣㥆㥋㥒㥝㥤㦖app\xe1␕othin\xe7ẖƀhir㓫⻈㥙op\xf4⾵Ā;hᎷ㥢\xefㆍĀiu㥩㥭gm\xe1㎳Ābp㥲㦄setneqĀ;q㥽㦀쀀⊊︀;쀀⫋︀setneqĀ;q㦏㦒쀀⊋︀;쀀⫌︀Āhr㦛㦟et\xe1㚜iangleĀlr㦪㦯eft\xbbथight\xbbၑy;䐲ash\xbbံƀelr㧄㧒㧗ƀ;beⷪ㧋㧏ar;抻q;扚lip;拮Ābt㧜ᑨa\xf2ᑩr;쀀\ud835\udd33tr\xe9㦮suĀbp㧯㧱\xbbജ\xbb൙pf;쀀\ud835\udd67ro\xf0໻tr\xe9㦴Ācu㨆㨋r;쀀\ud835\udccbĀbp㨐㨘nĀEe㦀㨖\xbb㥾nĀEe㦒㨞\xbb㦐igzag;榚΀cefoprs㨶㨻㩖㩛㩔㩡㩪irc;䅵Ādi㩀㩑Ābg㩅㩉ar;機eĀ;qᗺ㩏;扙erp;愘r;쀀\ud835\udd34pf;쀀\ud835\udd68Ā;eᑹ㩦at\xe8ᑹcr;쀀\ud835\udcccૣណ㪇\0㪋\0㪐㪛\0\0㪝㪨㪫㪯\0\0㫃㫎\0㫘ៜ៟tr\xe9៑r;쀀\ud835\udd35ĀAa㪔㪗r\xf2σr\xf2৶;䎾ĀAa㪡㪤r\xf2θr\xf2৫a\xf0✓is;拻ƀdptឤ㪵㪾Āfl㪺ឩ;쀀\ud835\udd69im\xe5ឲĀAa㫇㫊r\xf2ώr\xf2ਁĀcq㫒ីr;쀀\ud835\udccdĀpt៖㫜r\xe9។Ѐacefiosu㫰㫽㬈㬌㬑㬕㬛㬡cĀuy㫶㫻te耻\xfd䃽;䑏Āiy㬂㬆rc;䅷;䑋n耻\xa5䂥r;쀀\ud835\udd36cy;䑗pf;쀀\ud835\udd6acr;쀀\ud835\udcceĀcm㬦㬩y;䑎l耻\xff䃿Ԁacdefhiosw㭂㭈㭔㭘㭤㭩㭭㭴㭺㮀cute;䅺Āay㭍㭒ron;䅾;䐷ot;䅼Āet㭝㭡tr\xe6ᕟa;䎶r;쀀\ud835\udd37cy;䐶grarr;懝pf;쀀\ud835\udd6bcr;쀀\ud835\udccfĀjn㮅㮇;怍j;怌'.split("").map(e=>e.charCodeAt(0))),e0=new Uint16Array("Ȁaglq	\x15\x18\x1bɭ\x0f\0\0\x12p;䀦os;䀧t;䀾t;䀼uot;䀢".split("").map(e=>e.charCodeAt(0))),e1=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]),e3=null!=(t_=String.fromCodePoint)?t_:function(e){let t="";return e>65535&&(e-=65536,t+=String.fromCharCode(e>>>10&1023|55296),e=56320|1023&e),t+=String.fromCharCode(e)};function e5(e){var t;return e>=55296&&e<=57343||e>1114111?65533:null!=(t=e1.get(e))?t:e}function e8(e){return e>=tO.ZERO&&e<=tO.NINE}!function(e){e[e.NUM=35]="NUM",e[e.SEMI=59]="SEMI",e[e.EQUALS=61]="EQUALS",e[e.ZERO=48]="ZERO",e[e.NINE=57]="NINE",e[e.LOWER_A=97]="LOWER_A",e[e.LOWER_F=102]="LOWER_F",e[e.LOWER_X=120]="LOWER_X",e[e.LOWER_Z=122]="LOWER_Z",e[e.UPPER_A=65]="UPPER_A",e[e.UPPER_F=70]="UPPER_F",e[e.UPPER_Z=90]="UPPER_Z"}(tO||(tO={})),!function(e){e[e.VALUE_LENGTH=49152]="VALUE_LENGTH",e[e.BRANCH_LENGTH=16256]="BRANCH_LENGTH",e[e.JUMP_TABLE=127]="JUMP_TABLE"}(tW||(tW={})),!function(e){e[e.EntityStart=0]="EntityStart",e[e.NumericStart=1]="NumericStart",e[e.NumericDecimal=2]="NumericDecimal",e[e.NumericHex=3]="NumericHex",e[e.NamedEntity=4]="NamedEntity"}(tP||(tP={})),function(e){e[e.Legacy=0]="Legacy",e[e.Strict=1]="Strict",e[e.Attribute=2]="Attribute"}(tM||(tM={}));class e2{constructor(e,t,i){this.decodeTree=e,this.emitCodePoint=t,this.errors=i,this.state=tP.EntityStart,this.consumed=1,this.result=0,this.treeIndex=0,this.excess=1,this.decodeMode=tM.Strict}startEntity(e){this.decodeMode=e,this.state=tP.EntityStart,this.result=0,this.treeIndex=0,this.excess=1,this.consumed=1}write(e,t){switch(this.state){case tP.EntityStart:if(e.charCodeAt(t)===tO.NUM)return this.state=tP.NumericStart,this.consumed+=1,this.stateNumericStart(e,t+1);return this.state=tP.NamedEntity,this.stateNamedEntity(e,t);case tP.NumericStart:return this.stateNumericStart(e,t);case tP.NumericDecimal:return this.stateNumericDecimal(e,t);case tP.NumericHex:return this.stateNumericHex(e,t);case tP.NamedEntity:return this.stateNamedEntity(e,t)}}stateNumericStart(e,t){return t>=e.length?-1:(32|e.charCodeAt(t))===tO.LOWER_X?(this.state=tP.NumericHex,this.consumed+=1,this.stateNumericHex(e,t+1)):(this.state=tP.NumericDecimal,this.stateNumericDecimal(e,t))}addToNumericResult(e,t,i,r){if(t!==i){let n=i-t;this.result=this.result*Math.pow(r,n)+parseInt(e.substr(t,n),r),this.consumed+=n}}stateNumericHex(e,t){let i=t;for(;t<e.length;){var r;let n=e.charCodeAt(t);if(!e8(n)&&(!((r=n)>=tO.UPPER_A)||!(r<=tO.UPPER_F))&&(!(r>=tO.LOWER_A)||!(r<=tO.LOWER_F)))return this.addToNumericResult(e,i,t,16),this.emitNumericEntity(n,3);t+=1}return this.addToNumericResult(e,i,t,16),-1}stateNumericDecimal(e,t){let i=t;for(;t<e.length;){let r=e.charCodeAt(t);if(!e8(r))return this.addToNumericResult(e,i,t,10),this.emitNumericEntity(r,2);t+=1}return this.addToNumericResult(e,i,t,10),-1}emitNumericEntity(e,t){var i;if(this.consumed<=t)return null==(i=this.errors)||i.absenceOfDigitsInNumericCharacterReference(this.consumed),0;if(e===tO.SEMI)this.consumed+=1;else if(this.decodeMode===tM.Strict)return 0;return this.emitCodePoint(e5(this.result),this.consumed),this.errors&&(e!==tO.SEMI&&this.errors.missingSemicolonAfterCharacterReference(),this.errors.validateNumericCharacterReference(this.result)),this.consumed}stateNamedEntity(e,t){let{decodeTree:i}=this,r=i[this.treeIndex],n=(r&tW.VALUE_LENGTH)>>14;for(;t<e.length;t++,this.excess++){let s=e.charCodeAt(t);if(this.treeIndex=e4(i,r,this.treeIndex+Math.max(1,n),s),this.treeIndex<0)return 0===this.result||this.decodeMode===tM.Attribute&&(0===n||function(e){var t;return e===tO.EQUALS||(t=e)>=tO.UPPER_A&&t<=tO.UPPER_Z||t>=tO.LOWER_A&&t<=tO.LOWER_Z||e8(t)}(s))?0:this.emitNotTerminatedNamedEntity();if(0!=(n=((r=i[this.treeIndex])&tW.VALUE_LENGTH)>>14)){if(s===tO.SEMI)return this.emitNamedEntityData(this.treeIndex,n,this.consumed+this.excess);this.decodeMode!==tM.Strict&&(this.result=this.treeIndex,this.consumed+=this.excess,this.excess=0)}}return -1}emitNotTerminatedNamedEntity(){var e;let{result:t,decodeTree:i}=this,r=(i[t]&tW.VALUE_LENGTH)>>14;return this.emitNamedEntityData(t,r,this.consumed),null==(e=this.errors)||e.missingSemicolonAfterCharacterReference(),this.consumed}emitNamedEntityData(e,t,i){let{decodeTree:r}=this;return this.emitCodePoint(1===t?r[e]&~tW.VALUE_LENGTH:r[e+1],i),3===t&&this.emitCodePoint(r[e+2],i),i}end(){var e;switch(this.state){case tP.NamedEntity:return 0!==this.result&&(this.decodeMode!==tM.Attribute||this.result===this.treeIndex)?this.emitNotTerminatedNamedEntity():0;case tP.NumericDecimal:return this.emitNumericEntity(0,2);case tP.NumericHex:return this.emitNumericEntity(0,3);case tP.NumericStart:return null==(e=this.errors)||e.absenceOfDigitsInNumericCharacterReference(this.consumed),0;case tP.EntityStart:return 0}}}function e6(e){let t="",i=new e2(e,e=>t+=e3(e));return function(e,r){let n=0,s=0;for(;(s=e.indexOf("&",s))>=0;){t+=e.slice(n,s),i.startEntity(r);let a=i.write(e,s+1);if(a<0){n=s+i.end();break}n=s+a,s=0===a?n+1:n}let a=t+e.slice(n);return t="",a}}function e4(e,t,i,r){let n=(t&tW.BRANCH_LENGTH)>>7,s=t&tW.JUMP_TABLE;if(0===n)return 0!==s&&r===s?i:-1;if(s){let t=r-s;return t<0||t>=n?-1:e[i+t]-1}let a=i,o=a+n-1;for(;a<=o;){let t=a+o>>>1,i=e[t];if(i<r)a=t+1;else{if(!(i>r))return e[t+n];o=t-1}}return -1}function e9(e){return e===t$.Space||e===t$.NewLine||e===t$.Tab||e===t$.FormFeed||e===t$.CarriageReturn}function e7(e){return e===t$.Slash||e===t$.Gt||e9(e)}function te(e){return e>=t$.Zero&&e<=t$.Nine}e6(eK),e6(e0),!function(e){e[e.Tab=9]="Tab",e[e.NewLine=10]="NewLine",e[e.FormFeed=12]="FormFeed",e[e.CarriageReturn=13]="CarriageReturn",e[e.Space=32]="Space",e[e.ExclamationMark=33]="ExclamationMark",e[e.Number=35]="Number",e[e.Amp=38]="Amp",e[e.SingleQuote=39]="SingleQuote",e[e.DoubleQuote=34]="DoubleQuote",e[e.Dash=45]="Dash",e[e.Slash=47]="Slash",e[e.Zero=48]="Zero",e[e.Nine=57]="Nine",e[e.Semi=59]="Semi",e[e.Lt=60]="Lt",e[e.Eq=61]="Eq",e[e.Gt=62]="Gt",e[e.Questionmark=63]="Questionmark",e[e.UpperA=65]="UpperA",e[e.LowerA=97]="LowerA",e[e.UpperF=70]="UpperF",e[e.LowerF=102]="LowerF",e[e.UpperZ=90]="UpperZ",e[e.LowerZ=122]="LowerZ",e[e.LowerX=120]="LowerX",e[e.OpeningSquareBracket=91]="OpeningSquareBracket"}(t$||(t$={})),function(e){e[e.Text=1]="Text",e[e.BeforeTagName=2]="BeforeTagName",e[e.InTagName=3]="InTagName",e[e.InSelfClosingTag=4]="InSelfClosingTag",e[e.BeforeClosingTagName=5]="BeforeClosingTagName",e[e.InClosingTagName=6]="InClosingTagName",e[e.AfterClosingTagName=7]="AfterClosingTagName",e[e.BeforeAttributeName=8]="BeforeAttributeName",e[e.InAttributeName=9]="InAttributeName",e[e.AfterAttributeName=10]="AfterAttributeName",e[e.BeforeAttributeValue=11]="BeforeAttributeValue",e[e.InAttributeValueDq=12]="InAttributeValueDq",e[e.InAttributeValueSq=13]="InAttributeValueSq",e[e.InAttributeValueNq=14]="InAttributeValueNq",e[e.BeforeDeclaration=15]="BeforeDeclaration",e[e.InDeclaration=16]="InDeclaration",e[e.InProcessingInstruction=17]="InProcessingInstruction",e[e.BeforeComment=18]="BeforeComment",e[e.CDATASequence=19]="CDATASequence",e[e.InSpecialComment=20]="InSpecialComment",e[e.InCommentLike=21]="InCommentLike",e[e.BeforeSpecialS=22]="BeforeSpecialS",e[e.SpecialStartSequence=23]="SpecialStartSequence",e[e.InSpecialTag=24]="InSpecialTag",e[e.BeforeEntity=25]="BeforeEntity",e[e.BeforeNumericEntity=26]="BeforeNumericEntity",e[e.InNamedEntity=27]="InNamedEntity",e[e.InNumericEntity=28]="InNumericEntity",e[e.InHexEntity=29]="InHexEntity"}(tV||(tV={})),!function(e){e[e.NoValue=0]="NoValue",e[e.Unquoted=1]="Unquoted",e[e.Single=2]="Single",e[e.Double=3]="Double"}(tU||(tU={}));let tt={Cdata:new Uint8Array([67,68,65,84,65,91]),CdataEnd:new Uint8Array([93,93,62]),CommentEnd:new Uint8Array([45,45,62]),ScriptEnd:new Uint8Array([60,47,115,99,114,105,112,116]),StyleEnd:new Uint8Array([60,47,115,116,121,108,101]),TitleEnd:new Uint8Array([60,47,116,105,116,108,101])};class ti{constructor({xmlMode:e=!1,decodeEntities:t=!0},i){this.cbs=i,this.state=tV.Text,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=tV.Text,this.isSpecial=!1,this.running=!0,this.offset=0,this.currentSequence=void 0,this.sequenceIndex=0,this.trieIndex=0,this.trieCurrent=0,this.entityResult=0,this.entityExcess=0,this.xmlMode=e,this.decodeEntities=t,this.entityTrie=e?e0:eK}reset(){this.state=tV.Text,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=tV.Text,this.currentSequence=void 0,this.running=!0,this.offset=0}write(e){this.offset+=this.buffer.length,this.buffer=e,this.parse()}end(){this.running&&this.finish()}pause(){this.running=!1}resume(){this.running=!0,this.index<this.buffer.length+this.offset&&this.parse()}getIndex(){return this.index}getSectionStart(){return this.sectionStart}stateText(e){e===t$.Lt||!this.decodeEntities&&this.fastForwardTo(t$.Lt)?(this.index>this.sectionStart&&this.cbs.ontext(this.sectionStart,this.index),this.state=tV.BeforeTagName,this.sectionStart=this.index):this.decodeEntities&&e===t$.Amp&&(this.state=tV.BeforeEntity)}stateSpecialStartSequence(e){let t=this.sequenceIndex===this.currentSequence.length;if(t?e7(e):(32|e)===this.currentSequence[this.sequenceIndex]){if(!t)return void this.sequenceIndex++}else this.isSpecial=!1;this.sequenceIndex=0,this.state=tV.InTagName,this.stateInTagName(e)}stateInSpecialTag(e){if(this.sequenceIndex===this.currentSequence.length){if(e===t$.Gt||e9(e)){let t=this.index-this.currentSequence.length;if(this.sectionStart<t){let e=this.index;this.index=t,this.cbs.ontext(this.sectionStart,t),this.index=e}this.isSpecial=!1,this.sectionStart=t+2,this.stateInClosingTagName(e);return}this.sequenceIndex=0}(32|e)===this.currentSequence[this.sequenceIndex]?this.sequenceIndex+=1:0===this.sequenceIndex?this.currentSequence===tt.TitleEnd?this.decodeEntities&&e===t$.Amp&&(this.state=tV.BeforeEntity):this.fastForwardTo(t$.Lt)&&(this.sequenceIndex=1):this.sequenceIndex=Number(e===t$.Lt)}stateCDATASequence(e){e===tt.Cdata[this.sequenceIndex]?++this.sequenceIndex===tt.Cdata.length&&(this.state=tV.InCommentLike,this.currentSequence=tt.CdataEnd,this.sequenceIndex=0,this.sectionStart=this.index+1):(this.sequenceIndex=0,this.state=tV.InDeclaration,this.stateInDeclaration(e))}fastForwardTo(e){for(;++this.index<this.buffer.length+this.offset;)if(this.buffer.charCodeAt(this.index-this.offset)===e)return!0;return this.index=this.buffer.length+this.offset-1,!1}stateInCommentLike(e){e===this.currentSequence[this.sequenceIndex]?++this.sequenceIndex===this.currentSequence.length&&(this.currentSequence===tt.CdataEnd?this.cbs.oncdata(this.sectionStart,this.index,2):this.cbs.oncomment(this.sectionStart,this.index,2),this.sequenceIndex=0,this.sectionStart=this.index+1,this.state=tV.Text):0===this.sequenceIndex?this.fastForwardTo(this.currentSequence[0])&&(this.sequenceIndex=1):e!==this.currentSequence[this.sequenceIndex-1]&&(this.sequenceIndex=0)}isTagStartChar(e){return this.xmlMode?!e7(e):e>=t$.LowerA&&e<=t$.LowerZ||e>=t$.UpperA&&e<=t$.UpperZ}startSpecial(e,t){this.isSpecial=!0,this.currentSequence=e,this.sequenceIndex=t,this.state=tV.SpecialStartSequence}stateBeforeTagName(e){if(e===t$.ExclamationMark)this.state=tV.BeforeDeclaration,this.sectionStart=this.index+1;else if(e===t$.Questionmark)this.state=tV.InProcessingInstruction,this.sectionStart=this.index+1;else if(this.isTagStartChar(e)){let t=32|e;this.sectionStart=this.index,this.xmlMode||t!==tt.TitleEnd[2]?this.state=this.xmlMode||t!==tt.ScriptEnd[2]?tV.InTagName:tV.BeforeSpecialS:this.startSpecial(tt.TitleEnd,3)}else e===t$.Slash?this.state=tV.BeforeClosingTagName:(this.state=tV.Text,this.stateText(e))}stateInTagName(e){e7(e)&&(this.cbs.onopentagname(this.sectionStart,this.index),this.sectionStart=-1,this.state=tV.BeforeAttributeName,this.stateBeforeAttributeName(e))}stateBeforeClosingTagName(e){e9(e)||(e===t$.Gt?this.state=tV.Text:(this.state=this.isTagStartChar(e)?tV.InClosingTagName:tV.InSpecialComment,this.sectionStart=this.index))}stateInClosingTagName(e){(e===t$.Gt||e9(e))&&(this.cbs.onclosetag(this.sectionStart,this.index),this.sectionStart=-1,this.state=tV.AfterClosingTagName,this.stateAfterClosingTagName(e))}stateAfterClosingTagName(e){(e===t$.Gt||this.fastForwardTo(t$.Gt))&&(this.state=tV.Text,this.baseState=tV.Text,this.sectionStart=this.index+1)}stateBeforeAttributeName(e){e===t$.Gt?(this.cbs.onopentagend(this.index),this.isSpecial?(this.state=tV.InSpecialTag,this.sequenceIndex=0):this.state=tV.Text,this.baseState=this.state,this.sectionStart=this.index+1):e===t$.Slash?this.state=tV.InSelfClosingTag:e9(e)||(this.state=tV.InAttributeName,this.sectionStart=this.index)}stateInSelfClosingTag(e){e===t$.Gt?(this.cbs.onselfclosingtag(this.index),this.state=tV.Text,this.baseState=tV.Text,this.sectionStart=this.index+1,this.isSpecial=!1):e9(e)||(this.state=tV.BeforeAttributeName,this.stateBeforeAttributeName(e))}stateInAttributeName(e){(e===t$.Eq||e7(e))&&(this.cbs.onattribname(this.sectionStart,this.index),this.sectionStart=-1,this.state=tV.AfterAttributeName,this.stateAfterAttributeName(e))}stateAfterAttributeName(e){e===t$.Eq?this.state=tV.BeforeAttributeValue:e===t$.Slash||e===t$.Gt?(this.cbs.onattribend(tU.NoValue,this.index),this.state=tV.BeforeAttributeName,this.stateBeforeAttributeName(e)):e9(e)||(this.cbs.onattribend(tU.NoValue,this.index),this.state=tV.InAttributeName,this.sectionStart=this.index)}stateBeforeAttributeValue(e){e===t$.DoubleQuote?(this.state=tV.InAttributeValueDq,this.sectionStart=this.index+1):e===t$.SingleQuote?(this.state=tV.InAttributeValueSq,this.sectionStart=this.index+1):e9(e)||(this.sectionStart=this.index,this.state=tV.InAttributeValueNq,this.stateInAttributeValueNoQuotes(e))}handleInAttributeValue(e,t){e===t||!this.decodeEntities&&this.fastForwardTo(t)?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(t===t$.DoubleQuote?tU.Double:tU.Single,this.index),this.state=tV.BeforeAttributeName):this.decodeEntities&&e===t$.Amp&&(this.baseState=this.state,this.state=tV.BeforeEntity)}stateInAttributeValueDoubleQuotes(e){this.handleInAttributeValue(e,t$.DoubleQuote)}stateInAttributeValueSingleQuotes(e){this.handleInAttributeValue(e,t$.SingleQuote)}stateInAttributeValueNoQuotes(e){e9(e)||e===t$.Gt?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(tU.Unquoted,this.index),this.state=tV.BeforeAttributeName,this.stateBeforeAttributeName(e)):this.decodeEntities&&e===t$.Amp&&(this.baseState=this.state,this.state=tV.BeforeEntity)}stateBeforeDeclaration(e){e===t$.OpeningSquareBracket?(this.state=tV.CDATASequence,this.sequenceIndex=0):this.state=e===t$.Dash?tV.BeforeComment:tV.InDeclaration}stateInDeclaration(e){(e===t$.Gt||this.fastForwardTo(t$.Gt))&&(this.cbs.ondeclaration(this.sectionStart,this.index),this.state=tV.Text,this.sectionStart=this.index+1)}stateInProcessingInstruction(e){(e===t$.Gt||this.fastForwardTo(t$.Gt))&&(this.cbs.onprocessinginstruction(this.sectionStart,this.index),this.state=tV.Text,this.sectionStart=this.index+1)}stateBeforeComment(e){e===t$.Dash?(this.state=tV.InCommentLike,this.currentSequence=tt.CommentEnd,this.sequenceIndex=2,this.sectionStart=this.index+1):this.state=tV.InDeclaration}stateInSpecialComment(e){(e===t$.Gt||this.fastForwardTo(t$.Gt))&&(this.cbs.oncomment(this.sectionStart,this.index,0),this.state=tV.Text,this.sectionStart=this.index+1)}stateBeforeSpecialS(e){let t=32|e;t===tt.ScriptEnd[3]?this.startSpecial(tt.ScriptEnd,4):t===tt.StyleEnd[3]?this.startSpecial(tt.StyleEnd,4):(this.state=tV.InTagName,this.stateInTagName(e))}stateBeforeEntity(e){this.entityExcess=1,this.entityResult=0,e===t$.Number?this.state=tV.BeforeNumericEntity:e===t$.Amp||(this.trieIndex=0,this.trieCurrent=this.entityTrie[0],this.state=tV.InNamedEntity,this.stateInNamedEntity(e))}stateInNamedEntity(e){if(this.entityExcess+=1,this.trieIndex=e4(this.entityTrie,this.trieCurrent,this.trieIndex+1,e),this.trieIndex<0){this.emitNamedEntity(),this.index--;return}this.trieCurrent=this.entityTrie[this.trieIndex];let t=this.trieCurrent&tW.VALUE_LENGTH;if(t){let i=(t>>14)-1;if(this.allowLegacyEntity()||e===t$.Semi){let e=this.index-this.entityExcess+1;e>this.sectionStart&&this.emitPartial(this.sectionStart,e),this.entityResult=this.trieIndex,this.trieIndex+=i,this.entityExcess=0,this.sectionStart=this.index+1,0===i&&this.emitNamedEntity()}else this.trieIndex+=i}}emitNamedEntity(){if(this.state=this.baseState,0!==this.entityResult)switch((this.entityTrie[this.entityResult]&tW.VALUE_LENGTH)>>14){case 1:this.emitCodePoint(this.entityTrie[this.entityResult]&~tW.VALUE_LENGTH);break;case 2:this.emitCodePoint(this.entityTrie[this.entityResult+1]);break;case 3:this.emitCodePoint(this.entityTrie[this.entityResult+1]),this.emitCodePoint(this.entityTrie[this.entityResult+2])}}stateBeforeNumericEntity(e){(32|e)===t$.LowerX?(this.entityExcess++,this.state=tV.InHexEntity):(this.state=tV.InNumericEntity,this.stateInNumericEntity(e))}emitNumericEntity(e){let t=this.index-this.entityExcess-1;t+2+Number(this.state===tV.InHexEntity)!==this.index&&(t>this.sectionStart&&this.emitPartial(this.sectionStart,t),this.sectionStart=this.index+Number(e),this.emitCodePoint(e5(this.entityResult))),this.state=this.baseState}stateInNumericEntity(e){e===t$.Semi?this.emitNumericEntity(!0):te(e)?(this.entityResult=10*this.entityResult+(e-t$.Zero),this.entityExcess++):(this.allowLegacyEntity()?this.emitNumericEntity(!1):this.state=this.baseState,this.index--)}stateInHexEntity(e){if(e===t$.Semi)this.emitNumericEntity(!0);else if(te(e))this.entityResult=16*this.entityResult+(e-t$.Zero),this.entityExcess++;else e>=t$.UpperA&&e<=t$.UpperF||e>=t$.LowerA&&e<=t$.LowerF?(this.entityResult=16*this.entityResult+((32|e)-t$.LowerA+10),this.entityExcess++):(this.allowLegacyEntity()?this.emitNumericEntity(!1):this.state=this.baseState,this.index--)}allowLegacyEntity(){return!this.xmlMode&&(this.baseState===tV.Text||this.baseState===tV.InSpecialTag)}cleanup(){this.running&&this.sectionStart!==this.index&&(this.state===tV.Text||this.state===tV.InSpecialTag&&0===this.sequenceIndex?(this.cbs.ontext(this.sectionStart,this.index),this.sectionStart=this.index):(this.state===tV.InAttributeValueDq||this.state===tV.InAttributeValueSq||this.state===tV.InAttributeValueNq)&&(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=this.index))}shouldContinue(){return this.index<this.buffer.length+this.offset&&this.running}parse(){for(;this.shouldContinue();){let e=this.buffer.charCodeAt(this.index-this.offset);switch(this.state){case tV.Text:this.stateText(e);break;case tV.SpecialStartSequence:this.stateSpecialStartSequence(e);break;case tV.InSpecialTag:this.stateInSpecialTag(e);break;case tV.CDATASequence:this.stateCDATASequence(e);break;case tV.InAttributeValueDq:this.stateInAttributeValueDoubleQuotes(e);break;case tV.InAttributeName:this.stateInAttributeName(e);break;case tV.InCommentLike:this.stateInCommentLike(e);break;case tV.InSpecialComment:this.stateInSpecialComment(e);break;case tV.BeforeAttributeName:this.stateBeforeAttributeName(e);break;case tV.InTagName:this.stateInTagName(e);break;case tV.InClosingTagName:this.stateInClosingTagName(e);break;case tV.BeforeTagName:this.stateBeforeTagName(e);break;case tV.AfterAttributeName:this.stateAfterAttributeName(e);break;case tV.InAttributeValueSq:this.stateInAttributeValueSingleQuotes(e);break;case tV.BeforeAttributeValue:this.stateBeforeAttributeValue(e);break;case tV.BeforeClosingTagName:this.stateBeforeClosingTagName(e);break;case tV.AfterClosingTagName:this.stateAfterClosingTagName(e);break;case tV.BeforeSpecialS:this.stateBeforeSpecialS(e);break;case tV.InAttributeValueNq:this.stateInAttributeValueNoQuotes(e);break;case tV.InSelfClosingTag:this.stateInSelfClosingTag(e);break;case tV.InDeclaration:this.stateInDeclaration(e);break;case tV.BeforeDeclaration:this.stateBeforeDeclaration(e);break;case tV.BeforeComment:this.stateBeforeComment(e);break;case tV.InProcessingInstruction:this.stateInProcessingInstruction(e);break;case tV.InNamedEntity:this.stateInNamedEntity(e);break;case tV.BeforeEntity:this.stateBeforeEntity(e);break;case tV.InHexEntity:this.stateInHexEntity(e);break;case tV.InNumericEntity:this.stateInNumericEntity(e);break;default:this.stateBeforeNumericEntity(e)}this.index++}this.cleanup()}finish(){this.state===tV.InNamedEntity&&this.emitNamedEntity(),this.sectionStart<this.index&&this.handleTrailingData(),this.cbs.onend()}handleTrailingData(){let e=this.buffer.length+this.offset;this.state===tV.InCommentLike?this.currentSequence===tt.CdataEnd?this.cbs.oncdata(this.sectionStart,e,0):this.cbs.oncomment(this.sectionStart,e,0):this.state===tV.InNumericEntity&&this.allowLegacyEntity()||this.state===tV.InHexEntity&&this.allowLegacyEntity()?this.emitNumericEntity(!1):this.state===tV.InTagName||this.state===tV.BeforeAttributeName||this.state===tV.BeforeAttributeValue||this.state===tV.AfterAttributeName||this.state===tV.InAttributeName||this.state===tV.InAttributeValueSq||this.state===tV.InAttributeValueDq||this.state===tV.InAttributeValueNq||this.state===tV.InClosingTagName||this.cbs.ontext(this.sectionStart,e)}emitPartial(e,t){this.baseState!==tV.Text&&this.baseState!==tV.InSpecialTag?this.cbs.onattribdata(e,t):this.cbs.ontext(e,t)}emitCodePoint(e){this.baseState!==tV.Text&&this.baseState!==tV.InSpecialTag?this.cbs.onattribentity(e):this.cbs.ontextentity(e)}}let tr=new Set(["input","option","optgroup","select","button","datalist","textarea"]),tn=new Set(["p"]),ts=new Set(["thead","tbody"]),ta=new Set(["dd","dt"]),to=new Set(["rt","rp"]),tl=new Map([["tr",new Set(["tr","th","td"])],["th",new Set(["th"])],["td",new Set(["thead","th","td"])],["body",new Set(["head","link","script"])],["li",new Set(["li"])],["p",tn],["h1",tn],["h2",tn],["h3",tn],["h4",tn],["h5",tn],["h6",tn],["select",tr],["input",tr],["output",tr],["button",tr],["datalist",tr],["textarea",tr],["option",new Set(["option"])],["optgroup",new Set(["optgroup","option"])],["dd",ta],["dt",ta],["address",tn],["article",tn],["aside",tn],["blockquote",tn],["details",tn],["div",tn],["dl",tn],["fieldset",tn],["figcaption",tn],["figure",tn],["footer",tn],["form",tn],["header",tn],["hr",tn],["main",tn],["nav",tn],["ol",tn],["pre",tn],["section",tn],["table",tn],["ul",tn],["rt",to],["rp",to],["tbody",ts],["tfoot",ts]]),tc=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]),tu=new Set(["math","svg"]),th=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignobject","desc","title"]),td=/\s|\//;class tp{constructor(e,t={}){var i,r,n,s,a;this.options=t,this.startIndex=0,this.endIndex=0,this.openTagStart=0,this.tagname="",this.attribname="",this.attribvalue="",this.attribs=null,this.stack=[],this.foreignContext=[],this.buffers=[],this.bufferOffset=0,this.writeIndex=0,this.ended=!1,this.cbs=null!=e?e:{},this.lowerCaseTagNames=null!=(i=t.lowerCaseTags)?i:!t.xmlMode,this.lowerCaseAttributeNames=null!=(r=t.lowerCaseAttributeNames)?r:!t.xmlMode,this.tokenizer=new(null!=(n=t.Tokenizer)?n:ti)(this.options,this),null==(a=(s=this.cbs).onparserinit)||a.call(s,this)}ontext(e,t){var i,r;let n=this.getSlice(e,t);this.endIndex=t-1,null==(r=(i=this.cbs).ontext)||r.call(i,n),this.startIndex=t}ontextentity(e){var t,i;let r=this.tokenizer.getSectionStart();this.endIndex=r-1,null==(i=(t=this.cbs).ontext)||i.call(t,e3(e)),this.startIndex=r}isVoidElement(e){return!this.options.xmlMode&&tc.has(e)}onopentagname(e,t){this.endIndex=t;let i=this.getSlice(e,t);this.lowerCaseTagNames&&(i=i.toLowerCase()),this.emitOpenTag(i)}emitOpenTag(e){var t,i,r,n;this.openTagStart=this.startIndex,this.tagname=e;let s=!this.options.xmlMode&&tl.get(e);if(s)for(;this.stack.length>0&&s.has(this.stack[this.stack.length-1]);){let e=this.stack.pop();null==(i=(t=this.cbs).onclosetag)||i.call(t,e,!0)}!this.isVoidElement(e)&&(this.stack.push(e),tu.has(e)?this.foreignContext.push(!0):th.has(e)&&this.foreignContext.push(!1)),null==(n=(r=this.cbs).onopentagname)||n.call(r,e),this.cbs.onopentag&&(this.attribs={})}endOpenTag(e){var t,i;this.startIndex=this.openTagStart,this.attribs&&(null==(i=(t=this.cbs).onopentag)||i.call(t,this.tagname,this.attribs,e),this.attribs=null),this.cbs.onclosetag&&this.isVoidElement(this.tagname)&&this.cbs.onclosetag(this.tagname,!0),this.tagname=""}onopentagend(e){this.endIndex=e,this.endOpenTag(!1),this.startIndex=e+1}onclosetag(e,t){var i,r,n,s,a,o;this.endIndex=t;let l=this.getSlice(e,t);if(this.lowerCaseTagNames&&(l=l.toLowerCase()),(tu.has(l)||th.has(l))&&this.foreignContext.pop(),this.isVoidElement(l))this.options.xmlMode||"br"!==l||(null==(r=(i=this.cbs).onopentagname)||r.call(i,"br"),null==(s=(n=this.cbs).onopentag)||s.call(n,"br",{},!0),null==(o=(a=this.cbs).onclosetag)||o.call(a,"br",!1));else{let e=this.stack.lastIndexOf(l);if(-1!==e)if(this.cbs.onclosetag){let t=this.stack.length-e;for(;t--;)this.cbs.onclosetag(this.stack.pop(),0!==t)}else this.stack.length=e;else this.options.xmlMode||"p"!==l||(this.emitOpenTag("p"),this.closeCurrentTag(!0))}this.startIndex=t+1}onselfclosingtag(e){this.endIndex=e,this.options.xmlMode||this.options.recognizeSelfClosing||this.foreignContext[this.foreignContext.length-1]?(this.closeCurrentTag(!1),this.startIndex=e+1):this.onopentagend(e)}closeCurrentTag(e){var t,i;let r=this.tagname;this.endOpenTag(e),this.stack[this.stack.length-1]===r&&(null==(i=(t=this.cbs).onclosetag)||i.call(t,r,!e),this.stack.pop())}onattribname(e,t){this.startIndex=e;let i=this.getSlice(e,t);this.attribname=this.lowerCaseAttributeNames?i.toLowerCase():i}onattribdata(e,t){this.attribvalue+=this.getSlice(e,t)}onattribentity(e){this.attribvalue+=e3(e)}onattribend(e,t){var i,r;this.endIndex=t,null==(r=(i=this.cbs).onattribute)||r.call(i,this.attribname,this.attribvalue,e===tU.Double?'"':e===tU.Single?"'":e===tU.NoValue?void 0:null),this.attribs&&!Object.prototype.hasOwnProperty.call(this.attribs,this.attribname)&&(this.attribs[this.attribname]=this.attribvalue),this.attribvalue=""}getInstructionName(e){let t=e.search(td),i=t<0?e:e.substr(0,t);return this.lowerCaseTagNames&&(i=i.toLowerCase()),i}ondeclaration(e,t){this.endIndex=t;let i=this.getSlice(e,t);if(this.cbs.onprocessinginstruction){let e=this.getInstructionName(i);this.cbs.onprocessinginstruction(`!${e}`,`!${i}`)}this.startIndex=t+1}onprocessinginstruction(e,t){this.endIndex=t;let i=this.getSlice(e,t);if(this.cbs.onprocessinginstruction){let e=this.getInstructionName(i);this.cbs.onprocessinginstruction(`?${e}`,`?${i}`)}this.startIndex=t+1}oncomment(e,t,i){var r,n,s,a;this.endIndex=t,null==(n=(r=this.cbs).oncomment)||n.call(r,this.getSlice(e,t-i)),null==(a=(s=this.cbs).oncommentend)||a.call(s),this.startIndex=t+1}oncdata(e,t,i){var r,n,s,a,o,l,c,u,h,d;this.endIndex=t;let p=this.getSlice(e,t-i);this.options.xmlMode||this.options.recognizeCDATA?(null==(n=(r=this.cbs).oncdatastart)||n.call(r),null==(a=(s=this.cbs).ontext)||a.call(s,p),null==(l=(o=this.cbs).oncdataend)||l.call(o)):(null==(u=(c=this.cbs).oncomment)||u.call(c,`[CDATA[${p}]]`),null==(d=(h=this.cbs).oncommentend)||d.call(h)),this.startIndex=t+1}onend(){var e,t;if(this.cbs.onclosetag){this.endIndex=this.startIndex;for(let e=this.stack.length;e>0;this.cbs.onclosetag(this.stack[--e],!0));}null==(t=(e=this.cbs).onend)||t.call(e)}reset(){var e,t,i,r;null==(t=(e=this.cbs).onreset)||t.call(e),this.tokenizer.reset(),this.tagname="",this.attribname="",this.attribs=null,this.stack.length=0,this.startIndex=0,this.endIndex=0,null==(r=(i=this.cbs).onparserinit)||r.call(i,this),this.buffers.length=0,this.bufferOffset=0,this.writeIndex=0,this.ended=!1}parseComplete(e){this.reset(),this.end(e)}getSlice(e,t){for(;e-this.bufferOffset>=this.buffers[0].length;)this.shiftBuffer();let i=this.buffers[0].slice(e-this.bufferOffset,t-this.bufferOffset);for(;t-this.bufferOffset>this.buffers[0].length;)this.shiftBuffer(),i+=this.buffers[0].slice(0,t-this.bufferOffset);return i}shiftBuffer(){this.bufferOffset+=this.buffers[0].length,this.writeIndex--,this.buffers.shift()}write(e){var t,i;if(this.ended){null==(i=(t=this.cbs).onerror)||i.call(t,Error(".write() after done!"));return}this.buffers.push(e),this.tokenizer.running&&(this.tokenizer.write(e),this.writeIndex++)}end(e){var t,i;if(this.ended){null==(i=(t=this.cbs).onerror)||i.call(t,Error(".end() after done!"));return}e&&this.write(e),this.ended=!0,this.tokenizer.end()}pause(){this.tokenizer.pause()}resume(){for(this.tokenizer.resume();this.tokenizer.running&&this.writeIndex<this.buffers.length;)this.tokenizer.write(this.buffers[this.writeIndex++]);this.ended&&this.tokenizer.end()}parseChunk(e){this.write(e)}done(e){this.end(e)}}let tf=/["&'<>$\x80-\uFFFF]/g,tm=new Map([[34,"&quot;"],[38,"&amp;"],[39,"&apos;"],[60,"&lt;"],[62,"&gt;"]]),tg=null!=String.prototype.codePointAt?(e,t)=>e.codePointAt(t):(e,t)=>(64512&e.charCodeAt(t))==55296?(e.charCodeAt(t)-55296)*1024+e.charCodeAt(t+1)-56320+65536:e.charCodeAt(t);function tb(e){let t,i="",r=0;for(;null!==(t=tf.exec(e));){let n=t.index,s=e.charCodeAt(n),a=tm.get(s);void 0!==a?(i+=e.substring(r,n)+a,r=n+1):(i+=`${e.substring(r,n)}&#x${tg(e,n).toString(16)};`,r=tf.lastIndex+=Number((64512&s)==55296))}return i+e.substr(r)}function tx(e,t){return function(i){let r,n=0,s="";for(;r=e.exec(i);)n!==r.index&&(s+=i.substring(n,r.index)),s+=t.get(r[0].charCodeAt(0)),n=r.index+1;return s+i.substring(n)}}tx(/[&<>'"]/g,tm);let ty=tx(/["&\u00A0]/g,new Map([[34,"&quot;"],[38,"&amp;"],[160,"&nbsp;"]])),tk=tx(/[&<>\u00A0]/g,new Map([[38,"&amp;"],[60,"&lt;"],[62,"&gt;"],[160,"&nbsp;"]]));!function(e){e[e.XML=0]="XML",e[e.HTML=1]="HTML"}(tj||(tj={})),function(e){e[e.UTF8=0]="UTF8",e[e.ASCII=1]="ASCII",e[e.Extensive=2]="Extensive",e[e.Attribute=3]="Attribute",e[e.Text=4]="Text"}(tH||(tH={}));let tw=new Map(["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","textPath"].map(e=>[e.toLowerCase(),e])),tS=new Map(["definitionURL","attributeName","attributeType","baseFrequency","baseProfile","calcMode","clipPathUnits","diffuseConstant","edgeMode","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector","zoomAndPan"].map(e=>[e.toLowerCase(),e])),tE=new Set(["style","script","xmp","iframe","noembed","noframes","plaintext","noscript"]);function tL(e){return e.replace(/"/g,"&quot;")}let tI=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]);function tT(e,t={}){let i="length"in e?e:[e],d="";for(let e=0;e<i.length;e++)d+=function(e,t){var i,d,p;switch(e.type){case r:return tT(e.children,t);case h:case s:return i=e,`<${i.data}>`;case a:return d=e,`<!--${d.data}-->`;case u:return p=e,`<![CDATA[${p.children[0].data}]]>`;case o:case l:case c:return function(e,t){var i;"foreign"===t.xmlMode&&(e.name=null!=(i=tw.get(e.name))?i:e.name,e.parent&&tN.has(e.parent.name)&&(t={...t,xmlMode:!1})),!t.xmlMode&&tA.has(e.name)&&(t={...t,xmlMode:"foreign"});let r=`<${e.name}`,n=function(e,t){var i;if(!e)return;let r=(null!=(i=t.encodeEntities)?i:t.decodeEntities)===!1?tL:t.xmlMode||"utf8"!==t.encodeEntities?tb:ty;return Object.keys(e).map(i=>{var n,s;let a=null!=(n=e[i])?n:"";return("foreign"===t.xmlMode&&(i=null!=(s=tS.get(i))?s:i),t.emptyAttrs||t.xmlMode||""!==a)?`${i}="${r(a)}"`:i}).join(" ")}(e.attribs,t);return n&&(r+=` ${n}`),0===e.children.length&&(t.xmlMode?!1!==t.selfClosingTags:t.selfClosingTags&&tI.has(e.name))?(t.xmlMode||(r+=" "),r+="/>"):(r+=">",e.children.length>0&&(r+=tT(e.children,t)),(t.xmlMode||!tI.has(e.name))&&(r+=`</${e.name}>`)),r}(e,t);case n:return function(e,t){var i;let r=e.data||"";return(null!=(i=t.encodeEntities)?i:t.decodeEntities)===!1||!t.xmlMode&&e.parent&&tE.has(e.parent.name)||(r=t.xmlMode||"utf8"!==t.encodeEntities?tb(r):tk(r)),r}(e,t)}}(i[e],t);return d}let tN=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignObject","desc","title"]),tA=new Set(["svg","math"]);function tB(e){return Array.isArray(e)?e.map(tB).join(""):isTag(e)?"br"===e.name?"\n":tB(e.children):isCDATA(e)?tB(e.children):isText(e)?e.data:""}function tC(e){return Array.isArray(e)?e.map(tC).join(""):hasChildren(e)&&!isComment(e)?tC(e.children):isText(e)?e.data:""}function tv(e){return Array.isArray(e)?e.map(tv).join(""):hasChildren(e)&&(e.type===ElementType.Tag||isCDATA(e))?tv(e.children):isText(e)?e.data:""}let tq={tag_name:e=>"function"==typeof e?t=>w(t)&&e(t.name):"*"===e?w:t=>w(t)&&t.name===e,tag_type:e=>"function"==typeof e?t=>e(t.type):t=>t.type===e,tag_contains:e=>"function"==typeof e?t=>S(t)&&e(t.data):t=>S(t)&&t.data===e};function tD(e,t){return i=>e(i)||t(i)}!function(e){e[e.DISCONNECTED=1]="DISCONNECTED",e[e.PRECEDING=2]="PRECEDING",e[e.FOLLOWING=4]="FOLLOWING",e[e.CONTAINS=8]="CONTAINS",e[e.CONTAINED_BY=16]="CONTAINED_BY"}(tG||(tG={}));var tR,t_,tO,tW,tP,tM,t$,tV,tU,tj,tH,tG,tF=i(43793);function tz(e,t,i=()=>void 0){if(void 0===e){let e=function(...i){return t(e,...i)};return e}return e>=0?function(...r){return t(tz(e-1,t,i),...r)}:i}function tQ(e,t){let i=0,r=e.length;for(;i<r&&e[i]===t;)++i;for(;r>i&&e[r-1]===t;)--r;return i>0||r<e.length?e.substring(i,r):e}function tZ(e,t){let i=new Map;for(let r=e.length;r-- >0;){let n=e[r],s=t(n);i.set(s,i.has(s)?tF(n,i.get(s),{arrayMerge:tX}):n)}return[...i.values()].reverse()}let tX=(e,t,i)=>[...t];function tJ(e,t){for(let i of t){if(!e)return;e=e[i]}return e}function tY(e,t="a",i=26){let r=[];do r.push((e-=1)%i),e=e/i|0;while(e>0);let n=t.charCodeAt(0);return r.reverse().map(e=>String.fromCharCode(n+e)).join("")}let tK=["I","X","C","M"],t0=["V","L","D"];function t1(e){return[...e+""].map(e=>+e).reverse().map((e,t)=>e%5<4?(e<5?"":t0[t])+tK[t].repeat(e%5):tK[t]+(e<5?t0[t]:tK[t+1])).reverse().join("")}class t3{constructor(e,t){this.lines=[],this.nextLineWords=[],this.maxLineLength=t||e.wordwrap||Number.MAX_VALUE,this.nextLineAvailableChars=this.maxLineLength,this.wrapCharacters=tJ(e,["longWordSplit","wrapCharacters"])||[],this.forceWrapOnLimit=tJ(e,["longWordSplit","forceWrapOnLimit"])||!1,this.stashedSpace=!1,this.wordBreakOpportunity=!1}pushWord(e,t=!1){this.nextLineAvailableChars<=0&&!t&&this.startNewLine();let i=0===this.nextLineWords.length,r=e.length+ +!i;if(r<=this.nextLineAvailableChars||t)this.nextLineWords.push(e),this.nextLineAvailableChars-=r;else{let[t,...r]=this.splitLongWord(e);for(let e of(i||this.startNewLine(),this.nextLineWords.push(t),this.nextLineAvailableChars-=t.length,r))this.startNewLine(),this.nextLineWords.push(e),this.nextLineAvailableChars-=e.length}}popWord(){let e=this.nextLineWords.pop();if(void 0!==e){let t=0===this.nextLineWords.length,i=e.length+ +!t;this.nextLineAvailableChars+=i}return e}concatWord(e,t=!1){if(this.wordBreakOpportunity&&e.length>this.nextLineAvailableChars)this.pushWord(e,t),this.wordBreakOpportunity=!1;else{let i=this.popWord();this.pushWord(i?i.concat(e):e,t)}}startNewLine(e=1){this.lines.push(this.nextLineWords),e>1&&this.lines.push(...Array.from({length:e-1},()=>[])),this.nextLineWords=[],this.nextLineAvailableChars=this.maxLineLength}isEmpty(){return 0===this.lines.length&&0===this.nextLineWords.length}clear(){this.lines.length=0,this.nextLineWords.length=0,this.nextLineAvailableChars=this.maxLineLength}toString(){return[...this.lines,this.nextLineWords].map(e=>e.join(" ")).join("\n")}splitLongWord(e){let t=[],i=0;for(;e.length>this.maxLineLength;){let r=e.substring(0,this.maxLineLength),n=e.substring(this.maxLineLength),s=r.lastIndexOf(this.wrapCharacters[i]);if(s>-1)e=r.substring(s+1)+n,t.push(r.substring(0,s+1));else if(++i<this.wrapCharacters.length)e=r+n;else{if(this.forceWrapOnLimit){if(t.push(r),(e=n).length>this.maxLineLength)continue}else e=r+n;break}}return t.push(e),t}}class t5{constructor(e=null){this.next=e}getRoot(){return this.next?this.next:this}}class t8 extends t5{constructor(e,t=null,i=1,r){super(t),this.leadingLineBreaks=i,this.inlineTextBuilder=new t3(e,r),this.rawText="",this.stashedLineBreaks=0,this.isPre=t&&t.isPre,this.isNoWrap=t&&t.isNoWrap}}class t2 extends t8{constructor(e,t=null,{interRowLineBreaks:i=1,leadingLineBreaks:r=2,maxLineLength:n,maxPrefixLength:s=0,prefixAlign:a="left"}={}){super(e,t,r,n),this.maxPrefixLength=s,this.prefixAlign=a,this.interRowLineBreaks=i}}class t6 extends t8{constructor(e,t=null,{leadingLineBreaks:i=1,maxLineLength:r,prefix:n=""}={}){super(e,t,i,r),this.prefix=n}}class t4 extends t5{constructor(e=null){super(e),this.rows=[],this.isPre=e&&e.isPre,this.isNoWrap=e&&e.isNoWrap}}class t9 extends t5{constructor(e=null){super(e),this.cells=[],this.isPre=e&&e.isPre,this.isNoWrap=e&&e.isNoWrap}}class t7 extends t5{constructor(e,t=null,i){super(t),this.inlineTextBuilder=new t3(e,i),this.rawText="",this.stashedLineBreaks=0,this.isPre=t&&t.isPre,this.isNoWrap=t&&t.isNoWrap}}class ie extends t5{constructor(e=null,t){super(e),this.transform=t}}class it{constructor(e){this.whitespaceChars=e.preserveNewlines?e.whitespaceCharacters.replace(/\n/g,""):e.whitespaceCharacters;let t=[...this.whitespaceChars].map(e=>"\\u"+e.charCodeAt(0).toString(16).padStart(4,"0")).join("");if(this.leadingWhitespaceRe=RegExp(`^[${t}]`),this.trailingWhitespaceRe=RegExp(`[${t}]$`),this.allWhitespaceOrEmptyRe=RegExp(`^[${t}]*$`),this.newlineOrNonWhitespaceRe=RegExp(`(\\n|[^\\n${t}])`,"g"),this.newlineOrNonNewlineStringRe=RegExp(`(\\n|[^\\n]+)`,"g"),e.preserveNewlines){let e=RegExp(`\\n|[^\\n${t}]+`,"gm");this.shrinkWrapAdd=function(t,i,r=e=>e,n=!1){if(!t)return;let s=i.stashedSpace,a=!1,o=e.exec(t);if(o)for(a=!0,"\n"===o[0]?i.startNewLine():s||this.testLeadingWhitespace(t)?i.pushWord(r(o[0]),n):i.concatWord(r(o[0]),n);null!==(o=e.exec(t));)"\n"===o[0]?i.startNewLine():i.pushWord(r(o[0]),n);i.stashedSpace=s&&!a||this.testTrailingWhitespace(t)}}else{let e=RegExp(`[^${t}]+`,"g");this.shrinkWrapAdd=function(t,i,r=e=>e,n=!1){if(!t)return;let s=i.stashedSpace,a=!1,o=e.exec(t);if(o)for(a=!0,s||this.testLeadingWhitespace(t)?i.pushWord(r(o[0]),n):i.concatWord(r(o[0]),n);null!==(o=e.exec(t));)i.pushWord(r(o[0]),n);i.stashedSpace=s&&!a||this.testTrailingWhitespace(t)}}}addLiteral(e,t,i=!0){if(!e)return;let r=t.stashedSpace,n=!1,s=this.newlineOrNonNewlineStringRe.exec(e);if(s)for(n=!0,"\n"===s[0]?t.startNewLine():r?t.pushWord(s[0],i):t.concatWord(s[0],i);null!==(s=this.newlineOrNonNewlineStringRe.exec(e));)"\n"===s[0]?t.startNewLine():t.pushWord(s[0],i);t.stashedSpace=r&&!n}testLeadingWhitespace(e){return this.leadingWhitespaceRe.test(e)}testTrailingWhitespace(e){return this.trailingWhitespaceRe.test(e)}testContainsWords(e){return!this.allWhitespaceOrEmptyRe.test(e)}countNewlinesNoWords(e){let t;this.newlineOrNonWhitespaceRe.lastIndex=0;let i=0;for(;null!==(t=this.newlineOrNonWhitespaceRe.exec(e));)if("\n"!==t[0])return 0;else i++;return i}}class ii{constructor(e,t,i){this.options=e,this.picker=t,this.metadata=i,this.whitespaceProcessor=new it(e),this._stackItem=new t8(e),this._wordTransformer=void 0}pushWordTransform(e){this._wordTransformer=new ie(this._wordTransformer,e)}popWordTransform(){if(!this._wordTransformer)return;let e=this._wordTransformer.transform;return this._wordTransformer=this._wordTransformer.next,e}startNoWrap(){this._stackItem.isNoWrap=!0}stopNoWrap(){this._stackItem.isNoWrap=!1}_getCombinedWordTransformer(){let e=this._wordTransformer?e=>(function e(t,i){return i?e(i.transform(t),i.next):t})(e,this._wordTransformer):void 0,t=this.options.encodeCharacters;return e?t?i=>t(e(i)):e:t}_popStackItem(){let e=this._stackItem;return this._stackItem=e.next,e}addLineBreak(){(this._stackItem instanceof t8||this._stackItem instanceof t6||this._stackItem instanceof t7)&&(this._stackItem.isPre?this._stackItem.rawText+="\n":this._stackItem.inlineTextBuilder.startNewLine())}addWordBreakOpportunity(){(this._stackItem instanceof t8||this._stackItem instanceof t6||this._stackItem instanceof t7)&&(this._stackItem.inlineTextBuilder.wordBreakOpportunity=!0)}addInline(e,{noWordTransform:t=!1}={}){if(this._stackItem instanceof t8||this._stackItem instanceof t6||this._stackItem instanceof t7){if(this._stackItem.isPre){this._stackItem.rawText+=e;return}if(0!==e.length&&(!this._stackItem.stashedLineBreaks||this.whitespaceProcessor.testContainsWords(e))){if(this.options.preserveNewlines){let t=this.whitespaceProcessor.countNewlinesNoWords(e);if(t>0)return void this._stackItem.inlineTextBuilder.startNewLine(t)}this._stackItem.stashedLineBreaks&&this._stackItem.inlineTextBuilder.startNewLine(this._stackItem.stashedLineBreaks),this.whitespaceProcessor.shrinkWrapAdd(e,this._stackItem.inlineTextBuilder,t?void 0:this._getCombinedWordTransformer(),this._stackItem.isNoWrap),this._stackItem.stashedLineBreaks=0}}}addLiteral(e){if((this._stackItem instanceof t8||this._stackItem instanceof t6||this._stackItem instanceof t7)&&0!==e.length){if(this._stackItem.isPre){this._stackItem.rawText+=e;return}this._stackItem.stashedLineBreaks&&this._stackItem.inlineTextBuilder.startNewLine(this._stackItem.stashedLineBreaks),this.whitespaceProcessor.addLiteral(e,this._stackItem.inlineTextBuilder,this._stackItem.isNoWrap),this._stackItem.stashedLineBreaks=0}}openBlock({leadingLineBreaks:e=1,reservedLineLength:t=0,isPre:i=!1}={}){let r=Math.max(20,this._stackItem.inlineTextBuilder.maxLineLength-t);this._stackItem=new t8(this.options,this._stackItem,e,r),i&&(this._stackItem.isPre=!0)}closeBlock({trailingLineBreaks:e=1,blockTransform:t}={}){let i=this._popStackItem(),r=t?t(ir(i)):ir(i);is(this._stackItem,r,i.leadingLineBreaks,Math.max(i.stashedLineBreaks,e))}openList({maxPrefixLength:e=0,prefixAlign:t="left",interRowLineBreaks:i=1,leadingLineBreaks:r=2}={}){this._stackItem=new t2(this.options,this._stackItem,{interRowLineBreaks:i,leadingLineBreaks:r,maxLineLength:this._stackItem.inlineTextBuilder.maxLineLength,maxPrefixLength:e,prefixAlign:t})}openListItem({prefix:e=""}={}){if(!(this._stackItem instanceof t2))throw Error("Can't add a list item to something that is not a list! Check the formatter.");let t=this._stackItem,i=Math.max(e.length,t.maxPrefixLength),r=Math.max(20,t.inlineTextBuilder.maxLineLength-i);this._stackItem=new t6(this.options,t,{prefix:e,maxLineLength:r,leadingLineBreaks:t.interRowLineBreaks})}closeListItem(){let e=this._popStackItem(),t=e.next,i=Math.max(e.prefix.length,t.maxPrefixLength),r="\n"+" ".repeat(i),n=("right"===t.prefixAlign?e.prefix.padStart(i):e.prefix.padEnd(i))+ir(e).replace(/\n/g,r);is(t,n,e.leadingLineBreaks,Math.max(e.stashedLineBreaks,t.interRowLineBreaks))}closeList({trailingLineBreaks:e=2}={}){let t=this._popStackItem(),i=ir(t);i&&is(this._stackItem,i,t.leadingLineBreaks,e)}openTable(){this._stackItem=new t4(this._stackItem)}openTableRow(){if(!(this._stackItem instanceof t4))throw Error("Can't add a table row to something that is not a table! Check the formatter.");this._stackItem=new t9(this._stackItem)}openTableCell({maxColumnWidth:e}={}){if(!(this._stackItem instanceof t9))throw Error("Can't add a table cell to something that is not a table row! Check the formatter.");this._stackItem=new t7(this.options,this._stackItem,e)}closeTableCell({colspan:e=1,rowspan:t=1}={}){let i=this._popStackItem(),r=tQ(ir(i),"\n");i.next.cells.push({colspan:e,rowspan:t,text:r})}closeTableRow(){let e=this._popStackItem();e.next.rows.push(e.cells)}closeTable({tableToString:e,leadingLineBreaks:t=2,trailingLineBreaks:i=2}){let r=e(this._popStackItem().rows);r&&is(this._stackItem,r,t,i)}toString(){return ir(this._stackItem.getRoot())}}function ir(e){if(!(e instanceof t8||e instanceof t6||e instanceof t7))throw Error("Only blocks, list items and table cells can be requested for text contents.");return e.inlineTextBuilder.isEmpty()?e.rawText:e.rawText+e.inlineTextBuilder.toString()}function is(e,t,i,r){if(!(e instanceof t8||e instanceof t6||e instanceof t7))throw Error("Only blocks, list items and table cells can contain text.");let n=ir(e),s=Math.max(e.stashedLineBreaks,i);e.inlineTextBuilder.clear(),n?e.rawText=n+"\n".repeat(s)+t:(e.rawText=t,e.leadingLineBreaks=s),e.stashedLineBreaks=r}function ia(e,t,i){if(!t)return;let r=i.options;for(let n of(t.length>r.limits.maxChildNodes&&(t=t.slice(0,r.limits.maxChildNodes)).push({data:r.limits.ellipsis,type:"text"}),t))switch(n.type){case"text":i.addInline(n.data);break;case"tag":{let t=i.picker.pick1(n);(0,r.formatters[t.format])(n,e,i,t.options||{})}}}function io(e){let t=e.attribs&&e.attribs.length?" "+Object.entries(e.attribs).map(([e,t])=>""===t?e:`${e}=${t.replace(/"/g,"&quot;")}`).join(" "):"";return`<${e.name}${t}>`}function il(e){return`</${e.name}>`}var ic=Object.freeze({__proto__:null,block:function(e,t,i,r){i.openBlock({leadingLineBreaks:r.leadingLineBreaks||2}),t(e.children,i),i.closeBlock({trailingLineBreaks:r.trailingLineBreaks||2})},blockHtml:function(e,t,i,r){i.openBlock({leadingLineBreaks:r.leadingLineBreaks||2}),i.startNoWrap(),i.addLiteral(tT(e,{decodeEntities:i.options.decodeEntities})),i.stopNoWrap(),i.closeBlock({trailingLineBreaks:r.trailingLineBreaks||2})},blockString:function(e,t,i,r){i.openBlock({leadingLineBreaks:r.leadingLineBreaks||2}),i.addLiteral(r.string||""),i.closeBlock({trailingLineBreaks:r.trailingLineBreaks||2})},blockTag:function(e,t,i,r){i.openBlock({leadingLineBreaks:r.leadingLineBreaks||2}),i.startNoWrap(),i.addLiteral(io(e)),i.stopNoWrap(),t(e.children,i),i.startNoWrap(),i.addLiteral(il(e)),i.stopNoWrap(),i.closeBlock({trailingLineBreaks:r.trailingLineBreaks||2})},inline:function(e,t,i,r){t(e.children,i)},inlineHtml:function(e,t,i,r){i.startNoWrap(),i.addLiteral(tT(e,{decodeEntities:i.options.decodeEntities})),i.stopNoWrap()},inlineString:function(e,t,i,r){i.addLiteral(r.string||"")},inlineSurround:function(e,t,i,r){i.addLiteral(r.prefix||""),t(e.children,i),i.addLiteral(r.suffix||"")},inlineTag:function(e,t,i,r){i.startNoWrap(),i.addLiteral(io(e)),i.stopNoWrap(),t(e.children,i),i.startNoWrap(),i.addLiteral(il(e)),i.stopNoWrap()},skip:function(e,t,i,r){}});function iu(e,t){return e[t]||(e[t]=[]),e[t]}function ih(e,t){return void 0===e[t]&&(e[t]=0===t?0:1+ih(e,t-1)),e[t]}function id(e,t,i,r){e[t+i]=Math.max(ih(e,t+i),ih(e,t)+r)}function ip(e,t){return t?("string"==typeof t[0]?t[0]:"[")+e+("string"==typeof t[1]?t[1]:"]"):e}function im(e,t,i,r,n){let s="function"==typeof t?t(e,r,n):e;return"/"===s[0]&&i?function(e,t){let i=e.length;for(;i>0&&"/"===e[i-1];)--i;return i<e.length?e.substring(0,i):e}(i,0)+s:s}function ig(e,t,i,r,n){let s="li"===tJ(e,["parent","name"]),a=0,o=(e.children||[]).filter(e=>"text"!==e.type||!/^\s*$/.test(e.data)).map(function(e){if("li"!==e.name)return{node:e,prefix:""};let t=s?n().trimStart():n();return t.length>a&&(a=t.length),{node:e,prefix:t}});if(o.length){for(let{node:e,prefix:n}of(i.openList({interRowLineBreaks:1,leadingLineBreaks:s?1:r.leadingLineBreaks||2,maxPrefixLength:a,prefixAlign:"left"}),o))i.openListItem({prefix:n}),t([e],i),i.closeListItem();i.closeList({trailingLineBreaks:s?1:r.trailingLineBreaks||2})}}function ib(e,t,i,r){function n(e){let n=+tJ(e,["attribs","colspan"])||1,s=+tJ(e,["attribs","rowspan"])||1;i.openTableCell({maxColumnWidth:r.maxColumnWidth}),t(e.children,i),i.closeTableCell({colspan:n,rowspan:s})}i.openTable(),e.children.forEach(function e(t){if("tag"!==t.type)return;let s=!1!==r.uppercaseHeaderCells?e=>{i.pushWordTransform(e=>e.toUpperCase()),n(e),i.popWordTransform()}:n;switch(t.name){case"thead":case"tbody":case"tfoot":case"center":t.children.forEach(e);return;case"tr":for(let e of(i.openTableRow(),t.children))if("tag"===e.type)switch(e.name){case"th":s(e);break;case"td":n(e)}i.closeTableRow()}}),i.closeTable({tableToString:e=>(function(e,t,i){let r=[],n=0,s=e.length,a=[0];for(let i=0;i<s;i++){let s=iu(r,i),c=e[i],u=0;for(let e=0;e<c.length;e++){let n=c[e];u=function(e,t=0){for(;e[t];)t++;return t}(s,u);var o=i,l=u;for(let e=0;e<n.rowspan;e++){let t=iu(r,o+e);for(let e=0;e<n.colspan;e++)t[l+e]=n}u+=n.colspan,n.lines=n.text.split("\n");let h=n.lines.length;id(a,i,n.rowspan,h+t)}n=s.length>n?s.length:n}!function(e,t){for(let i=0;i<t;i++){let t=iu(e,i);for(let r=0;r<i;r++){let n=iu(e,r);if(t[r]||n[i]){let e=t[r];t[r]=n[i],n[i]=e}}}}(r,s>n?s:n);let c=[],u=[0];for(let e=0;e<n;e++){let t,n=0,o=Math.min(s,r[e].length);for(;n<o;)if(t=r[e][n]){if(!t.rendered){let r=0;for(let i=0;i<t.lines.length;i++){let s=t.lines[i],o=a[n]+i;c[o]=(c[o]||"").padEnd(u[e])+s,r=s.length>r?s.length:r}id(u,e,t.colspan,r+i),t.rendered=!0}n+=t.rowspan}else{let e=a[n];c[e]=c[e]||"",n++}}return c.join("\n")})(e,r.rowSpacing??0,r.colSpacing??3),leadingLineBreaks:r.leadingLineBreaks,trailingLineBreaks:r.trailingLineBreaks})}var ix=Object.freeze({__proto__:null,anchor:function(e,t,i,r){let n=function(){if(r.ignoreHref||!e.attribs||!e.attribs.href)return"";let t=e.attribs.href.replace(/^mailto:/,"");return r.noAnchorUrl&&"#"===t[0]?"":t=im(t,r.pathRewrite,r.baseUrl,i.metadata,e)}();if(n){let s="";i.pushWordTransform(e=>(e&&(s+=e),e)),t(e.children,i),i.popWordTransform(),r.hideLinkHrefIfSameAsText&&n===s||i.addInline(s?" "+ip(n,r.linkBrackets):n,{noWordTransform:!0})}else t(e.children,i)},blockquote:function(e,t,i,r){i.openBlock({leadingLineBreaks:r.leadingLineBreaks||2,reservedLineLength:2}),t(e.children,i),i.closeBlock({trailingLineBreaks:r.trailingLineBreaks||2,blockTransform:e=>(!1!==r.trimEmptyLines?tQ(e,"\n"):e).split("\n").map(e=>"> "+e).join("\n")})},dataTable:ib,heading:function(e,t,i,r){i.openBlock({leadingLineBreaks:r.leadingLineBreaks||2}),!1!==r.uppercase?(i.pushWordTransform(e=>e.toUpperCase()),t(e.children,i),i.popWordTransform()):t(e.children,i),i.closeBlock({trailingLineBreaks:r.trailingLineBreaks||2})},horizontalLine:function(e,t,i,r){i.openBlock({leadingLineBreaks:r.leadingLineBreaks||2}),i.addInline("-".repeat(r.length||i.options.wordwrap||40)),i.closeBlock({trailingLineBreaks:r.trailingLineBreaks||2})},image:function(e,t,i,r){let n=e.attribs||{},s=n.alt?n.alt:"",a=n.src?im(n.src,r.pathRewrite,r.baseUrl,i.metadata,e):"",o=a?s?s+" "+ip(a,r.linkBrackets):ip(a,r.linkBrackets):s;i.addInline(o,{noWordTransform:!0})},lineBreak:function(e,t,i,r){i.addLineBreak()},orderedList:function(e,t,i,r){let n=Number(e.attribs.start||"1"),s=function(e="1"){switch(e){case"a":return e=>tY(e,"a");case"A":return e=>tY(e,"A");case"i":return e=>t1(e).toLowerCase();case"I":return e=>t1(e);default:return e=>e.toString()}}(e.attribs.type);return ig(e,t,i,r,()=>" "+s(n++)+". ")},paragraph:function(e,t,i,r){i.openBlock({leadingLineBreaks:r.leadingLineBreaks||2}),t(e.children,i),i.closeBlock({trailingLineBreaks:r.trailingLineBreaks||2})},pre:function(e,t,i,r){i.openBlock({isPre:!0,leadingLineBreaks:r.leadingLineBreaks||2}),t(e.children,i),i.closeBlock({trailingLineBreaks:r.trailingLineBreaks||2})},table:function(e,t,i,r){var n,s,a,o;return!function(e,t){if(!0===t)return!0;if(!e)return!1;let{classes:i,ids:r}=function(e){let t=[],i=[];for(let r of e)r.startsWith(".")?t.push(r.substring(1)):r.startsWith("#")&&i.push(r.substring(1));return{classes:t,ids:i}}(t),n=(e.class||"").split(" "),s=(e.id||"").split(" ");return n.some(e=>i.includes(e))||s.some(e=>r.includes(e))}(e.attribs,i.options.tables)?(n=e,s=t,a=i,o=r,void(a.openBlock({leadingLineBreaks:o.leadingLineBreaks}),s(n.children,a),a.closeBlock({trailingLineBreaks:o.trailingLineBreaks}))):ib(e,t,i,r)},unorderedList:function(e,t,i,r){let n=r.itemPrefix||" * ";return ig(e,t,i,r,()=>n)},wbr:function(e,t,i,r){i.addWordBreakOpportunity()}});let iy={baseElements:{selectors:["body"],orderBy:"selectors",returnDomByDefault:!0},decodeEntities:!0,encodeCharacters:{},formatters:{},limits:{ellipsis:"...",maxBaseElements:void 0,maxChildNodes:void 0,maxDepth:void 0,maxInputLength:0x1000000},longWordSplit:{forceWrapOnLimit:!1,wrapCharacters:[]},preserveNewlines:!1,selectors:[{selector:"*",format:"inline"},{selector:"a",format:"anchor",options:{baseUrl:null,hideLinkHrefIfSameAsText:!1,ignoreHref:!1,linkBrackets:["[","]"],noAnchorUrl:!0}},{selector:"article",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"aside",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"blockquote",format:"blockquote",options:{leadingLineBreaks:2,trailingLineBreaks:2,trimEmptyLines:!0}},{selector:"br",format:"lineBreak"},{selector:"div",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"footer",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"form",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"h1",format:"heading",options:{leadingLineBreaks:3,trailingLineBreaks:2,uppercase:!0}},{selector:"h2",format:"heading",options:{leadingLineBreaks:3,trailingLineBreaks:2,uppercase:!0}},{selector:"h3",format:"heading",options:{leadingLineBreaks:3,trailingLineBreaks:2,uppercase:!0}},{selector:"h4",format:"heading",options:{leadingLineBreaks:2,trailingLineBreaks:2,uppercase:!0}},{selector:"h5",format:"heading",options:{leadingLineBreaks:2,trailingLineBreaks:2,uppercase:!0}},{selector:"h6",format:"heading",options:{leadingLineBreaks:2,trailingLineBreaks:2,uppercase:!0}},{selector:"header",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"hr",format:"horizontalLine",options:{leadingLineBreaks:2,length:void 0,trailingLineBreaks:2}},{selector:"img",format:"image",options:{baseUrl:null,linkBrackets:["[","]"]}},{selector:"main",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"nav",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"ol",format:"orderedList",options:{leadingLineBreaks:2,trailingLineBreaks:2}},{selector:"p",format:"paragraph",options:{leadingLineBreaks:2,trailingLineBreaks:2}},{selector:"pre",format:"pre",options:{leadingLineBreaks:2,trailingLineBreaks:2}},{selector:"section",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"table",format:"table",options:{colSpacing:3,leadingLineBreaks:2,maxColumnWidth:60,rowSpacing:0,trailingLineBreaks:2,uppercaseHeaderCells:!0}},{selector:"ul",format:"unorderedList",options:{itemPrefix:" * ",leadingLineBreaks:2,trailingLineBreaks:2}},{selector:"wbr",format:"wbr"}],tables:[],whitespaceCharacters:" 	\r\n\f​",wordwrap:80},ik=(e,t,i)=>[...e,...t],iw=(e,t,i)=>[...t],iS=(e,t,i)=>e.some(e=>"object"==typeof e)?ik(e,t):iw(e,t);function iE(e,t={},i){return(function(e={}){return(e=tF(iy,e,{arrayMerge:iw,customMerge:e=>"selectors"===e?iS:void 0})).formatters=Object.assign({},ic,ix,e.formatters),e.selectors=tZ(e.selectors,e=>e.selector),function(e){if(e.tags){let t=Object.entries(e.tags).map(([e,t])=>({...t,selector:e||"*"}));e.selectors.push(...t),e.selectors=tZ(e.selectors,e=>e.selector)}function t(e,t,i){let r=t.pop();for(let i of t){let t=e[i];t||(t={},e[i]=t),e=t}e[r]=i}if(e.baseElement){let i=e.baseElement;t(e,["baseElements","selectors"],Array.isArray(i)?i:[i])}for(let i of(void 0!==e.returnDomByDefault&&t(e,["baseElements","returnDomByDefault"],e.returnDomByDefault),e.selectors))"anchor"===i.format&&tJ(i,["options","noLinkBrackets"])&&t(i,["options","linkBrackets"],!1)}(e),function(e={}){let t=e.selectors.filter(e=>!e.format);if(t.length)throw Error("Following selectors have no specified format: "+t.map(e=>`\`${e.selector}\``).join(", "));let i=new eP(e.selectors.map(e=>[e.selector,e])).build(eQ);"function"!=typeof e.encodeCharacters&&(e.encodeCharacters=function(e){if(!e||0===Object.keys(e).length)return;let t=Object.entries(e).filter(([,e])=>!1!==e),i=RegExp(t.map(([e])=>`(${[...e][0].replace(/[\s\S]/g,e=>"\\u"+e.charCodeAt().toString(16).padStart(4,"0"))})`).join("|"),"g"),r=t.map(([,e])=>e),n=(e,...t)=>r[t.findIndex(e=>e)];return e=>e.replace(i,n)}(e.encodeCharacters));let r=new eP(e.baseElements.selectors.map((e,t)=>[e,t+1])).build(eQ);function n(t){var i=t,n=e,s=r;let a=[];return tz(n.limits.maxDepth,function(e,t){for(let i of t=t.slice(0,n.limits.maxChildNodes)){if("tag"!==i.type)continue;let t=s.pick1(i);if(t>0?a.push({selectorIndex:t,element:i}):i.children&&e(i.children),a.length>=n.limits.maxBaseElements)return}})(i),"occurrence"!==n.baseElements.orderBy&&a.sort((e,t)=>e.selectorIndex-t.selectorIndex),n.baseElements.returnDomByDefault&&0===a.length?i:a.map(e=>e.element)}let s=tz(e.limits.maxDepth,ia,function(t,i){i.addInline(e.limits.ellipsis||"")});return function(t,r){var a=t,o=r,l=e,c=i,u=n,h=s;let d=l.limits.maxInputLength;d&&a&&a.length>d&&(console.warn(`Input length ${a.length} is above allowed limit of ${d}. Truncating without ellipsis.`),a=a.substring(0,d));let p=u(function(e,t){let i=new T(void 0,t);return new tp(i,t).end(e),i.root}(a,{decodeEntities:l.decodeEntities}).children),f=new ii(l,c,o);return h(p,f),f.toString()}}(e)})(t)(e,i)}}};