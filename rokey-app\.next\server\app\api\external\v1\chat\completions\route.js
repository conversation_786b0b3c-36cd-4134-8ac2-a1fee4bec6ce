(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[454],{5356:e=>{"use strict";e.exports=require("node:buffer")},5521:e=>{"use strict";e.exports=require("node:async_hooks")},9035:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>A,default:()=>P});var o,s={};r.r(s),r.d(s,{OPTIONS:()=>v,POST:()=>_,runtime:()=>g});var a={};r.r(a),r.d(a,{patchFetch:()=>C,routeModule:()=>b,serverHooks:()=>w,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>k});var i=r(8429),n=r(9874),c=r(8294),l=r(6567),p=r(4144),m=r(5421),u=r(974),d=r(4429),f=r(1109);let g="edge",h=f.z.object({model:f.z.string().optional().default("gpt-3.5-turbo"),messages:f.z.array(f.z.object({role:f.z.enum(["user","assistant","system"]),content:f.z.union([f.z.string(),f.z.array(f.z.any())])})).min(1,{message:"Messages array cannot be empty."}),stream:f.z.boolean().optional().default(!1),temperature:f.z.number().min(0).max(2).optional(),max_tokens:f.z.number().int().positive().optional(),top_p:f.z.number().min(0).max(1).optional(),frequency_penalty:f.z.number().min(-2).max(2).optional(),presence_penalty:f.z.number().min(-2).max(2).optional(),stop:f.z.union([f.z.string(),f.z.array(f.z.string())]).optional(),n:f.z.number().int().positive().optional().default(1),role:f.z.string().optional()}).catchall(f.z.any()),y=new d.S;async function _(e){try{let t,r=await y.authenticateRequest(e);if(!r.success)return u.Rp.json({error:{message:r.error,type:"authentication_error",code:"invalid_api_key"}},{status:r.statusCode||401});let{userApiKey:o,userConfig:s,ipAddress:a}=r;if(!y.hasPermission(o,"chat"))return u.Rp.json({error:{message:"API key does not have chat permission",type:"permission_error",code:"insufficient_permissions"}},{status:403});let i=await e.json(),n=h.safeParse(i);if(!n.success)return u.Rp.json({error:{message:"Invalid request body",type:"invalid_request_error",code:"invalid_request",details:n.error.flatten().fieldErrors}},{status:400});let c=n.data;if(c.stream&&!y.hasPermission(o,"streaming"))return u.Rp.json({error:{message:"API key does not have streaming permission",type:"permission_error",code:"streaming_not_allowed"}},{status:403});let l=!1;if(c.role||c.messages?.some(e=>e.content&&"string"==typeof e.content&&e.content.length>100))try{let e=await fetch("https://roukey.online/api/internal/classify-multi-role",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${process.env.ROKEY_API_ACCESS_TOKEN}`},body:JSON.stringify({messages:c.messages,role:c.role,config_id:s.id})});e.ok&&(l=(await e.json()).isMultiRole)}catch(e){}let p=null;l&&(p={type:"multi_role_detected",reason:"Gemini classifier detected this task requires multiple specialized roles working together",async_submit_url:"https://roukey.online/api/external/v1/async/submit",estimated_time_minutes:5,streaming_forced:!1,streaming_reason:"Multi-role tasks now support both streaming and non-streaming modes",benefits:["Supports both streaming and non-streaming modes","Real-time progress tracking with role detection (when streaming)","Better responsiveness and user experience","Proper handling of role coordination"]});let m={custom_api_config_id:s.id,messages:c.messages,stream:c.stream,temperature:c.temperature,max_tokens:c.max_tokens,role:c.role,model:c.model,top_p:c.top_p,frequency_penalty:c.frequency_penalty,presence_penalty:c.presence_penalty,stop:c.stop,n:c.n,_internal_user_id:s.user_id},d=new URL("/api/v1/chat/completions",e.url),f=new AbortController,g=setTimeout(()=>f.abort(),55e3);try{t=await fetch(d.toString(),{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${process.env.ROKEY_API_ACCESS_TOKEN}`,"X-Forwarded-For":a||"","X-User-API-Key-ID":o.id,"X-External-Request":"true"},body:JSON.stringify(m),signal:f.signal})}catch(e){if(clearTimeout(g),"AbortError"===e.name)return u.Rp.json({error:{message:"Request timeout. For complex multi-role tasks, consider using async processing or breaking down the request.",type:"timeout_error",code:"request_timeout",suggestion:"Try reducing complexity or use streaming for better responsiveness"}},{status:408});throw e}clearTimeout(g);let _=[],v=c.model;if(!c.stream&&t.ok)try{let e=t.clone(),r=await e.json();r.rokey_metadata?.roles_used&&(_=r.rokey_metadata.roles_used),r.model&&(v=r.model)}catch(e){}if(y.logApiUsage(o,e,{statusCode:t.status,modelUsed:v,providerUsed:_.length>0?_.join(", "):void 0},a).catch(e=>{}),c.stream){let e={"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key","X-RouKey-Config":s.name};return p&&(e["X-RouKey-Async-Recommendation"]=p.type,e["X-RouKey-Multi-Role-Detected"]="true"),new Response(t.body,{status:t.status,headers:e})}{let e=await t.json();if(!t.ok)return u.Rp.json({error:{message:e.error||"Internal server error",type:"server_error",code:"internal_error"}},{status:t.status});{let r={...e,rokey_metadata:{roles_used:e.rokey_metadata?.roles_used||[],routing_strategy:s.routing_strategy,config_name:s.name,api_key_name:o.key_name,...p&&{async_recommendation:p},...e.rokey_metadata||{}}},a={"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key","X-RouKey-Roles-Used":_.join(", ")||"none","X-RouKey-Config":s.name};return p&&(a["X-RouKey-Async-Recommendation"]=p.type,a["X-RouKey-Streaming-Suggestion"]="true"),u.Rp.json(r,{status:t.status,headers:a})}}}catch(e){return u.Rp.json({error:{message:"Internal server error",type:"server_error",code:"internal_error"}},{status:500})}}async function v(){return new Response(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key","Access-Control-Max-Age":"86400"}})}let b=new l.AppRouteRouteModule({definition:{kind:p.A.APP_ROUTE,page:"/api/external/v1/chat/completions/route",pathname:"/api/external/v1/chat/completions",filename:"route",bundlePath:"app/api/external/v1/chat/completions/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\external\\v1\\chat\\completions\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:k,serverHooks:w}=b;function C(){return(0,m.V5)({workAsyncStorage:x,workUnitAsyncStorage:k})}let S=null==(o=self.__RSC_MANIFEST)?void 0:o["/api/external/v1/chat/completions/route"],R=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);S&&R&&(0,n.fQ)({page:"/api/external/v1/chat/completions/route",clientReferenceManifest:S,serverActionsManifest:R,serverModuleMap:(0,i.e)({serverActionsManifest:R})});let A=a,P=c.s.wrap(b,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!0},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp","image/avif"],dangerouslyAllowSVG:!0,contentSecurityPolicy:"default-src 'self'; script-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"raw.githubusercontent.com",port:"",pathname:"/lobehub/lobe-icons/**"},{protocol:"https",hostname:"registry.npmmirror.com",port:"",pathname:"/@lobehub/icons-static-png/**"},{protocol:"https",hostname:"cdn.jsdelivr.net",port:"",pathname:"/npm/simple-icons@latest/icons/**"},{protocol:"https",hostname:"cdn.jsdelivr.net",port:"",pathname:"/npm/simple-icons@v11/icons/**"},{protocol:"https",hostname:"images.unsplash.com",port:"",pathname:"/**"},{protocol:"https",hostname:"cloud.gmelius.com",port:"",pathname:"/public/logos/**"},{protocol:"https",hostname:"upload.wikimedia.org",port:"",pathname:"/wikipedia/commons/**"},{protocol:"https",hostname:"kstatic.googleusercontent.com",port:"",pathname:"/files/**"}],unoptimized:!1},devIndicators:{position:"bottom-left"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"C:\\RoKey App\\rokey-app",experimental:{nodeMiddleware:!1,cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,dynamicOnHover:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!0,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!0,largePageDataBytes:128e3,typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,viewTransition:!1,routerBFCache:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,useCache:!1,optimizePackageImports:["@heroicons/react","@headlessui/react","react-markdown","react-syntax-highlighter","@supabase/supabase-js","lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},htmlLimitedBots:"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti",bundlePagesRouterDependencies:!1,configFile:"C:\\RoKey App\\rokey-app\\next.config.mjs",configFileName:"next.config.mjs",serverExternalPackages:["pdf-parse","mammoth"],turbopack:{rules:{"*.svg":{loaders:["@svgr/webpack"],as:"*.js"}},root:"C:\\RoKey App\\rokey-app"},compiler:{removeConsole:!0,reactRemoveProperties:!0},_originalRedirects:[]}})}},e=>{var t=t=>e(e.s=t);e.O(0,[580,918,109,833],()=>t(9035));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/external/v1/chat/completions/route"]=r}]);
//# sourceMappingURL=route.js.map