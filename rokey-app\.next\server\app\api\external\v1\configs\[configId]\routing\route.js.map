{"version": 3, "file": "app/api/external/v1/configs/[configId]/routing/route.js", "mappings": "gJEAA,8SFMO,IAAMA,EAAU,OAEjBC,EAAiB,IAAIC,EAAAA,CAAoBA,CASzCC,EAAuBC,EAAAA,CAAAA,CAAAA,IATTH,EASiB,CAAC,CACpCI,UAAWD,EAAAA,CAAAA,CAAAA,MAAQ,GAAGE,GAAG,CAAC,GAC1BC,WAAYH,EAAAA,CAAAA,CAAAA,MAAQ,GAAGI,IAAI,GAC3BC,SAAUL,EAAAA,CAAAA,CAAAA,MAAQ,GAAGM,GAAG,GAAGJ,GAAG,CAAC,GAAGK,QAAQ,GAAGC,OAAO,CAAC,EACvD,GAEMC,EAAwBT,EAAAA,CAAAA,CAAAA,MAAQ,CAAC,CACrCU,aAAcV,EAAAA,CAAAA,CAAAA,MAAQ,GAAGM,GAAG,GAAGJ,GAAG,CAAC,GAAGS,GAAG,CAAC,GAC1CR,WAAYH,EAAAA,CAAAA,CAAAA,MAAQ,GAAGI,IAAI,GAC3BQ,aAAcZ,EAAAA,CAAAA,CAAAA,MAAQ,GAAGO,QAAQ,EACnC,GAEMM,EAAsBb,EAAAA,CAAAA,CAAAA,MAAQ,CAAC,CACnCc,iBAAkBd,EAAAA,CAAAA,CAAAA,IAAM,CAAC,CAAC,iBAAkB,eAAgB,aAAa,EACzEe,SAAUf,EAAAA,CAAAA,CAAAA,MAAQ,CAAC,CAEjBgB,sBAAuBhB,EAAAA,CAAAA,CAAAA,IAAM,CAAC,CAAC,cAAe,SAAU,aAAa,EAAEO,QAAQ,GAG/EU,iBAAkBjB,EAAAA,CAAAA,CAAAA,KAAO,CAACD,GAAsBQ,QAAQ,GACxDW,gCAAiClB,EAAAA,CAAAA,CAAAA,OAAS,GAAGO,QAAQ,GACrDY,gBAAiBnB,EAAAA,CAAAA,CAAAA,MAAQ,GAAGO,QAAQ,GAGpCa,kBAAmBpB,EAAAA,CAAAA,CAAAA,KAAO,CAACS,GAAuBF,QAAQ,GAC1Dc,qBAAsBrB,EAAAA,CAAAA,CAAAA,MAAQ,GAAGM,GAAG,GAAGJ,GAAG,CAAC,GAAGS,GAAG,CAAC,GAAGJ,QAAQ,GAC7De,kBAAmBtB,EAAAA,CAAAA,CAAAA,MAAQ,GAAGM,GAAG,GAAGJ,GAAG,CAAC,GAAGS,GAAG,CAAC,IAAIJ,QAAQ,GAAGC,OAAO,CAAC,GACtEe,oBAAqBvB,EAAAA,CAAAA,CAAAA,MAAQ,GAAGE,GAAG,CAAC,IAAKS,GAAG,CAAC,GAAGJ,QAAQ,GAAGC,OAAO,CAAC,IAGnEgB,kBAAmBxB,EAAAA,CAAAA,CAAAA,IAAM,CAAC,CAAC,kBAAmB,SAAU,OAAO,EAAEO,QAAQ,GAAGC,OAAO,CAAC,mBACpFiB,eAAgBzB,EAAAA,CAAAA,CAAAA,OAAS,GAAGO,QAAQ,GAAGC,OAAO,EAAC,GAC/CkB,kBAAmB1B,EAAAA,CAAAA,CAAAA,MAAQ,GAAGM,GAAG,GAAGJ,GAAG,CAAC,GAAGS,GAAG,CAAC,MAAMJ,QAAQ,GAAGC,OAAO,CAAC,IACxEmB,gBAAiB3B,EAAAA,CAAAA,CAAAA,MAAQ,GAAGM,GAAG,GAAGJ,GAAG,CAAC,IAAIS,GAAG,CAAC,KAAKJ,QAAQ,GAAGC,OAAO,CAAC,GACxE,GAAGD,QAAQ,GAAGC,OAAO,CAAC,CAAC,EACzB,GAGO,eAAeoB,EAAIC,CAAoB,CAAE,QAAEC,CAAM,CAAe,EACrE,GAAI,CAEF,IAAMC,EAAa,MAAMlC,EAAemC,kBAADnC,CAAoB,CAACgC,GAE5D,GAAI,CAACE,EAAWE,OAAO,CACrB,CADuB,MAChBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAASN,EAAWK,KAAK,CACzBE,KAAM,uBACNC,KAAM,iBACR,CACF,EACA,CAAEC,OAAQT,EAAWU,UAAU,EAAI,GAAI,GAI3C,GAAM,CAAEC,YAAU,CAAEC,YAAU,WAAEC,CAAS,CAAE,CAAGb,EACxC,UAAEc,CAAQ,CAAE,CAAG,MAAMf,EAGrBgB,EAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAqCA,CAAClB,GAEjD,CAAEmB,KAAMC,CAAM,OAAEb,CAAK,CAAE,CAAG,MAAMU,EACnCI,IAAI,CAAC,sBACLC,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA6BT,CAAC,EACAC,EAAE,CAAC,KAAMP,GACTO,EAAE,CAAC,UAAWT,EAAYU,OAAO,EACjCC,MAAM,GAET,GAAIlB,GAAS,CAACa,EACZ,MADoB,CACbf,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,0BACTC,KAAM,kBACNC,KAAM,kBACR,CACF,EACA,CAAEC,OAAQ,GAAI,GAgBlB,OAXA3C,EAAe0D,WAAW,CACxBb,EACAb,EACA,CACEY,CAJU5C,UAIE,IACZ2D,UAAW,qBACXC,aAAc,WAChB,EACAb,GACAc,KAAK,CAACC,QAAQvB,KAAK,EAEdF,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvByB,GAAIX,EAAOW,EAAE,CACbC,OAAQ,iBACRC,KAAMb,EAAOa,IAAI,CACjBhD,iBAAkBmC,EAAOnC,gBAAgB,CACzCC,SAAUkC,EAAOlC,QAAQ,EAAI,CAAC,EAC9BE,iBAAkBgC,EAAOhC,gBAAgB,EAAI,EAAE,CAC/CG,kBAAmB6B,EAAO7B,iBAAiB,EAAI,EAAE,EAChD,CACD2C,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,oBAChC,+BAAgC,wCAClC,CACF,EAEF,CAAE,MAAO3B,EAAO,CAEd,OAAOF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,wBACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,EAElB,CACF,CAGO,eAAewB,EAAInC,CAAoB,CAAE,QAAEC,CAAM,CAAe,EACrE,GAAI,CAEF,IAAMC,EAAa,MAAMlC,EAAemC,kBAADnC,CAAoB,CAACgC,GAE5D,GAAI,CAACE,EAAWE,OAAO,CACrB,CADuB,MAChBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAASN,EAAWK,KAAK,CACzBE,KAAM,uBACNC,KAAM,iBACR,CACF,EACA,CAAEC,OAAQT,EAAWU,UAAU,EAAI,GAAI,GAI3C,GAAM,YAAEC,CAAU,YAAEC,CAAU,CAAEC,WAAS,CAAE,CAAGb,EACxC,UAAEc,CAAQ,CAAE,CAAG,MAAMf,EAGrBmC,EAAO,MAAMpC,EAAQM,IAAI,GACzB+B,EAAmBrD,EAAoBsD,SAAS,CAACF,GAEvD,GAAI,CAACC,EAAiBjC,OAAO,CAC3B,CAD6B,MACtBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,gCACTC,KAAM,mBACNC,KAAM,qBACN6B,QAASF,EAAiB9B,KAAK,CAACiC,MAAM,CAE1C,EACA,CAAE7B,OAAQ,GAAI,GAIlB,IAAM8B,EAAgBJ,EAAiBlB,IAAI,CAGrCF,EAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAqCA,CAAClB,GAEjD,CAAEmB,KAAMC,CAAM,CAAEb,MAAOmC,CAAW,CAAE,CAAG,MAAMzB,EAChDI,IAAI,CAAC,sBACLC,MAAM,CAAC,eACPC,EAAE,CAAC,KAAMP,GACTO,EAAE,CAAC,UAAWT,EAAYU,OAAO,EACjCC,MAAM,GAET,GAAIiB,GAAe,CAACtB,EAClB,MAD0B,CACnBf,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,0BACTC,KAAM,kBACNC,KAAM,kBACR,CACF,EACA,CAAEC,OAAQ,GAAI,GAKlB,GAAM,CAAEQ,KAAMwB,CAAa,CAAEpC,MAAOqC,CAAW,CAAE,CAAG,MAAM3B,EACvDI,IAAI,CAAC,sBACLwB,MAAM,CAAC,CACN5D,iBAAkBwD,EAAcxD,gBAAgB,CAChDC,SAAUuD,EAAcvD,QAAQ,CAChC4D,WAAY,IAAIC,OAAOC,WAAW,EACpC,GACCzB,EAAE,CAAC,KAAMP,GACTO,EAAE,CAAC,UAAWT,EAAYU,OAAO,EACjCF,MAAM,GACNG,MAAM,GAET,GAAImB,EAEF,OAAOvC,EAAAA,EAFQ,CAEKC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,yCACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,GAKlB,GAAI8B,EAAcvD,QAAQ,CAACE,gBAAgB,EAAE,CAE3C,MAAM6B,EACHI,IAAI,CAAC,oBACL4B,MAAM,GACN1B,EAAE,CAAC,uBAAwBP,GAG1ByB,EAAcvD,QAAQ,CAACE,gBAAgB,CAAC8D,MAAM,CAAG,GAAG,CACtD,IAAMC,EAAkBV,EAAcvD,QAAQ,CAACE,gBAAgB,CAACgE,GAAG,CAACC,GAAe,EACjFC,QADiF,aAC3DtC,EACtB5C,UAAWiF,EAAWjF,SAAS,CAC/BE,WAAY+E,EAAW/E,UAAU,CACjCE,SAAU6E,EAAW7E,QAAQ,CAC/B,EAEA,OAAMyC,EACHI,IAAI,CAAC,oBACLkC,MAAM,CAACJ,EACZ,CAIF,GAAIV,EAAcvD,QAAQ,CAACK,iBAAiB,EAAE,CAE5C,MAAM0B,EACHI,IAAI,CAAC,qBACL4B,MAAM,GACN1B,EAAE,CAAC,uBAAwBP,GAG1ByB,EAAcvD,QAAQ,CAACK,iBAAiB,CAAC2D,MAAM,CAAG,GAAG,CACvD,IAAMM,EAAmBf,EAAcvD,QAAQ,CAACK,iBAAiB,CAAC6D,GAAG,CAACC,GAAe,EACnFC,QADmF,aAC7DtC,EACtBnC,aAAcwE,EAAWxE,YAAY,CACrCP,WAAY+E,EAAW/E,UAAU,CACjCS,aAAcsE,EAAWtE,YAAY,CACvC,EAEA,OAAMkC,EACHI,IAAI,CAAC,qBACLkC,MAAM,CAACC,EACZ,CAeF,OAXAxF,EAAe0D,WAAW,CACxBb,EACAb,EACA,CACEY,CAJU5C,UAIE,IACZ2D,UAAW,qBACXC,aAAc,WAChB,EACAb,GACAc,KAAK,CAACC,QAAQvB,KAAK,EAEdF,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvByB,GAAIY,EAAcZ,EAAE,CACpBC,OAAQ,iBACRC,KAAMU,EAAcV,IAAI,CACxBhD,iBAAkB0D,EAAc1D,gBAAgB,CAChDC,SAAUyD,EAAczD,QAAQ,EAAI,CAAC,EACrC4D,WAAYH,EAAcG,UAAU,CACpCtC,QAAS,4CACX,EAAG,CACD0B,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,oBAChC,+BAAgC,wCAClC,CACF,EAEF,CAAE,MAAO3B,EAAO,CAEd,OAAOF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,wBACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,EAElB,CACF,CAGO,eAAe8C,IACpB,OAAO,IAAIpD,EAAAA,EAAYA,CAAC,KAAM,CAC5BM,OAAQ,IACRuB,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,oBAChC,+BAAgC,wCAClC,CACF,EACF,CCnWA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,yDACA,uDACA,iBACA,iEACA,CAAK,CACL,iHACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,kGACA,GAFA,2BAEA,2BACA,OACI,QAA8B,EAClC,yDACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA0B,aAAe,kDAAyD,wOAAuQ,ySAAoU,mBAAmB,QAAQ,uDAA2D,gGAAwG,EAAE,oGAA4G,EAAE,kGAA0G,EAAE,+FAAuG,EAAE,uEAA+E,EAAE,kFAA0F,EAAE,0FAAkG,EAAE,uFAA+F,iBAAsB,gBAAkB,uBAAyB,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,gEAAoE,6BAAoC,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,o/BAAmsC,qBAAyB,ykDAAkmD,idAAge,OAAS,SAAS,qCAAyC,iCAAmC,WAAa,0CAAkD,uBAiBpqM,CAAC,CAAC,EAAC,sBCvBH,wDCAA,mGC6CO,SAAShB,EAAsClB,CAAoB,EACxE,MAAO0D,CAAAA,EAAAA,EAAAA,kBAAAA,CAAkBA,CACvBC,0CAAoC,CACpCA,kNAAyC,CACzC,CACEC,QAAS,KACPC,GACS7D,CADO,CACC4D,OAAO,CAACC,GAAG,CAAC5B,IAAO6B,MAEpCC,IAAI9B,CAAY,CAAE6B,CAAa,CAAEE,CAAsB,EAGvD,EACAC,OAAOhC,CAAY,CAAE+B,CAAsB,EAG3C,CACF,CACF,EAEJ", "sources": ["webpack://_N_E/./src/app/api/external/v1/configs/[configId]/routing/route.ts", "webpack://_N_E/./src/app/api/external/v1/configs/[configId]/routing/route.ts?fe80", "webpack://_N_E/?1dfa", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/./src/lib/supabase/server.ts"], "sourcesContent": ["import { type NextRequest, NextResponse } from 'next/server';\nimport { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';\nimport { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';\nimport { z } from 'zod';\n\n// Use Edge Runtime for better performance\nexport const runtime = 'edge';\n\nconst authMiddleware = new ApiKeyAuthMiddleware();\n\ninterface RouteParams {\n  params: Promise<{\n    configId: string;\n  }>;\n}\n\n// Validation schemas for routing configuration\nconst RoleAssignmentSchema = z.object({\n  role_name: z.string().min(1),\n  api_key_id: z.string().uuid(),\n  priority: z.number().int().min(1).optional().default(1)\n});\n\nconst AgentAssignmentSchema = z.object({\n  agent_number: z.number().int().min(1).max(5),\n  api_key_id: z.string().uuid(),\n  agent_prompt: z.string().optional()\n});\n\nconst RoutingConfigSchema = z.object({\n  routing_strategy: z.enum(['load_balancing', 'role_routing', 'agent_mode']),\n  settings: z.object({\n    // Load balancing settings\n    load_balancing_method: z.enum(['round_robin', 'random', 'least_used']).optional(),\n    \n    // Role routing settings\n    role_assignments: z.array(RoleAssignmentSchema).optional(),\n    enable_multi_role_orchestration: z.boolean().optional(),\n    synthesis_model: z.string().optional(),\n    \n    // Agent mode settings\n    agent_assignments: z.array(AgentAssignmentSchema).optional(),\n    complexity_threshold: z.number().int().min(1).max(5).optional(),\n    max_debate_rounds: z.number().int().min(1).max(10).optional().default(3),\n    consensus_threshold: z.number().min(0.5).max(1).optional().default(0.6),\n    \n    // General settings\n    fallback_strategy: z.enum(['first_available', 'random', 'none']).optional().default('first_available'),\n    enable_caching: z.boolean().optional().default(true),\n    cache_ttl_minutes: z.number().int().min(1).max(1440).optional().default(60),\n    timeout_seconds: z.number().int().min(10).max(300).optional().default(60)\n  }).optional().default({})\n});\n\n// GET /api/external/v1/configs/{configId}/routing - Get routing configuration\nexport async function GET(request: NextRequest, { params }: RouteParams) {\n  try {\n    // 1. Authenticate using user-generated API key\n    const authResult = await authMiddleware.authenticateRequest(request);\n    \n    if (!authResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: authResult.error,\n            type: 'authentication_error',\n            code: 'invalid_api_key'\n          }\n        },\n        { status: authResult.statusCode || 401 }\n      );\n    }\n\n    const { userApiKey, userConfig, ipAddress } = authResult;\n    const { configId } = await params;\n\n    // 2. Get configuration with routing settings\n    const supabase = createSupabaseServerClientFromRequest(request);\n    \n    const { data: config, error } = await supabase\n      .from('custom_api_configs')\n      .select(`\n        id,\n        name,\n        routing_strategy,\n        settings,\n        role_assignments(\n          id,\n          role_name,\n          api_key_id,\n          priority,\n          api_keys(\n            id,\n            label,\n            provider,\n            status\n          )\n        ),\n        agent_assignments(\n          id,\n          agent_number,\n          api_key_id,\n          agent_prompt,\n          api_keys(\n            id,\n            label,\n            provider,\n            status\n          )\n        )\n      `)\n      .eq('id', configId)\n      .eq('user_id', userConfig!.user_id)\n      .single();\n\n    if (error || !config) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Configuration not found',\n            type: 'not_found_error',\n            code: 'config_not_found'\n          }\n        },\n        { status: 404 }\n      );\n    }\n\n    // 3. Log API usage\n    authMiddleware.logApiUsage(\n      userApiKey!,\n      request,\n      {\n        statusCode: 200,\n        modelUsed: 'routing_management',\n        providerUsed: 'rokey_api',\n      },\n      ipAddress\n    ).catch(console.error);\n\n    return NextResponse.json({\n      id: config.id,\n      object: 'routing_config',\n      name: config.name,\n      routing_strategy: config.routing_strategy,\n      settings: config.settings || {},\n      role_assignments: config.role_assignments || [],\n      agent_assignments: config.agent_assignments || []\n    }, {\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, PUT, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n      }\n    });\n\n  } catch (error) {\n    console.error('Error in routing config GET API:', error);\n    return NextResponse.json(\n      {\n        error: {\n          message: 'Internal server error',\n          type: 'server_error',\n          code: 'internal_error'\n        }\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// PUT /api/external/v1/configs/{configId}/routing - Update routing configuration\nexport async function PUT(request: NextRequest, { params }: RouteParams) {\n  try {\n    // 1. Authenticate using user-generated API key\n    const authResult = await authMiddleware.authenticateRequest(request);\n    \n    if (!authResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: authResult.error,\n            type: 'authentication_error',\n            code: 'invalid_api_key'\n          }\n        },\n        { status: authResult.statusCode || 401 }\n      );\n    }\n\n    const { userApiKey, userConfig, ipAddress } = authResult;\n    const { configId } = await params;\n\n    // 2. Validate request body\n    const body = await request.json();\n    const validationResult = RoutingConfigSchema.safeParse(body);\n\n    if (!validationResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Invalid routing configuration',\n            type: 'validation_error',\n            code: 'invalid_parameters',\n            details: validationResult.error.errors\n          }\n        },\n        { status: 400 }\n      );\n    }\n\n    const routingConfig = validationResult.data;\n\n    // 3. Verify config belongs to user\n    const supabase = createSupabaseServerClientFromRequest(request);\n    \n    const { data: config, error: configError } = await supabase\n      .from('custom_api_configs')\n      .select('id, user_id')\n      .eq('id', configId)\n      .eq('user_id', userConfig!.user_id)\n      .single();\n\n    if (configError || !config) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Configuration not found',\n            type: 'not_found_error',\n            code: 'config_not_found'\n          }\n        },\n        { status: 404 }\n      );\n    }\n\n    // 4. Update routing configuration\n    const { data: updatedConfig, error: updateError } = await supabase\n      .from('custom_api_configs')\n      .update({\n        routing_strategy: routingConfig.routing_strategy,\n        settings: routingConfig.settings,\n        updated_at: new Date().toISOString()\n      })\n      .eq('id', configId)\n      .eq('user_id', userConfig!.user_id)\n      .select()\n      .single();\n\n    if (updateError) {\n      console.error('Error updating routing config:', updateError);\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Failed to update routing configuration',\n            type: 'server_error',\n            code: 'database_error'\n          }\n        },\n        { status: 500 }\n      );\n    }\n\n    // 5. Update role assignments if provided\n    if (routingConfig.settings.role_assignments) {\n      // Delete existing role assignments\n      await supabase\n        .from('role_assignments')\n        .delete()\n        .eq('custom_api_config_id', configId);\n\n      // Insert new role assignments\n      if (routingConfig.settings.role_assignments.length > 0) {\n        const roleAssignments = routingConfig.settings.role_assignments.map(assignment => ({\n          custom_api_config_id: configId,\n          role_name: assignment.role_name,\n          api_key_id: assignment.api_key_id,\n          priority: assignment.priority\n        }));\n\n        await supabase\n          .from('role_assignments')\n          .insert(roleAssignments);\n      }\n    }\n\n    // 6. Update agent assignments if provided\n    if (routingConfig.settings.agent_assignments) {\n      // Delete existing agent assignments\n      await supabase\n        .from('agent_assignments')\n        .delete()\n        .eq('custom_api_config_id', configId);\n\n      // Insert new agent assignments\n      if (routingConfig.settings.agent_assignments.length > 0) {\n        const agentAssignments = routingConfig.settings.agent_assignments.map(assignment => ({\n          custom_api_config_id: configId,\n          agent_number: assignment.agent_number,\n          api_key_id: assignment.api_key_id,\n          agent_prompt: assignment.agent_prompt\n        }));\n\n        await supabase\n          .from('agent_assignments')\n          .insert(agentAssignments);\n      }\n    }\n\n    // 7. Log API usage\n    authMiddleware.logApiUsage(\n      userApiKey!,\n      request,\n      {\n        statusCode: 200,\n        modelUsed: 'routing_management',\n        providerUsed: 'rokey_api',\n      },\n      ipAddress\n    ).catch(console.error);\n\n    return NextResponse.json({\n      id: updatedConfig.id,\n      object: 'routing_config',\n      name: updatedConfig.name,\n      routing_strategy: updatedConfig.routing_strategy,\n      settings: updatedConfig.settings || {},\n      updated_at: updatedConfig.updated_at,\n      message: 'Routing configuration updated successfully'\n    }, {\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, PUT, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n      }\n    });\n\n  } catch (error) {\n    console.error('Error in routing config PUT API:', error);\n    return NextResponse.json(\n      {\n        error: {\n          message: 'Internal server error',\n          type: 'server_error',\n          code: 'internal_error'\n        }\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// OPTIONS handler for CORS\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, PUT, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n    },\n  });\n}\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\configs\\\\[configId]\\\\routing\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/external/v1/configs/[configId]/routing/route\",\n        pathname: \"/api/external/v1/configs/[configId]/routing\",\n        filename: \"route\",\n        bundlePath: \"app/api/external/v1/configs/[configId]/routing/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\configs\\\\[configId]\\\\routing\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fexternal%2Fv1%2Fconfigs%2F%5BconfigId%5D%2Frouting%2Froute&page=%2Fapi%2Fexternal%2Fv1%2Fconfigs%2F%5BconfigId%5D%2Frouting%2Froute&pagePath=private-next-app-dir%2Fapi%2Fexternal%2Fv1%2Fconfigs%2F%5BconfigId%5D%2Frouting%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&appPaths=%2Fapi%2Fexternal%2Fv1%2Fconfigs%2F%5BconfigId%5D%2Frouting%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/external/v1/configs/[configId]/routing/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":true},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":********,\"configOrigin\":\"next.config.mjs\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\",\"image/avif\"],\"dangerouslyAllowSVG\":true,\"contentSecurityPolicy\":\"default-src 'self'; script-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"raw.githubusercontent.com\",\"port\":\"\",\"pathname\":\"/lobehub/lobe-icons/**\"},{\"protocol\":\"https\",\"hostname\":\"registry.npmmirror.com\",\"port\":\"\",\"pathname\":\"/@lobehub/icons-static-png/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@latest/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@v11/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"images.unsplash.com\",\"port\":\"\",\"pathname\":\"/**\"},{\"protocol\":\"https\",\"hostname\":\"cloud.gmelius.com\",\"port\":\"\",\"pathname\":\"/public/logos/**\"},{\"protocol\":\"https\",\"hostname\":\"upload.wikimedia.org\",\"port\":\"\",\"pathname\":\"/wikipedia/commons/**\"},{\"protocol\":\"https\",\"hostname\":\"kstatic.googleusercontent.com\",\"port\":\"\",\"pathname\":\"/files/**\"}],\"unoptimized\":false},\"devIndicators\":{\"position\":\"bottom-left\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"C:\\\\RoKey App\\\\rokey-app\",\"experimental\":{\"nodeMiddleware\":false,\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"dynamicOnHover\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":3,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":true,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":true,\"largePageDataBytes\":128000,\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"viewTransition\":false,\"routerBFCache\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"useCache\":false,\"optimizePackageImports\":[\"@heroicons/react\",\"@headlessui/react\",\"react-markdown\",\"react-syntax-highlighter\",\"@supabase/supabase-js\",\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"htmlLimitedBots\":\"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti\",\"bundlePagesRouterDependencies\":false,\"configFile\":\"C:\\\\RoKey App\\\\rokey-app\\\\next.config.mjs\",\"configFileName\":\"next.config.mjs\",\"serverExternalPackages\":[\"pdf-parse\",\"mammoth\"],\"turbopack\":{\"rules\":{\"*.svg\":{\"loaders\":[\"@svgr/webpack\"],\"as\":\"*.js\"}},\"root\":\"C:\\\\RoKey App\\\\rokey-app\"},\"compiler\":{\"removeConsole\":true,\"reactRemoveProperties\":true},\"_originalRedirects\":[]}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/external/v1/configs/[configId]/routing/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/external/v1/configs/[configId]/routing/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "module.exports = require(\"node:buffer\");", "module.exports = require(\"node:async_hooks\");", "import { createServerClient, type CookieOptions } from '@supabase/ssr';\r\nimport { createClient } from '@supabase/supabase-js';\r\nimport { cookies } from 'next/headers';\r\nimport { NextRequest } from 'next/server';\r\n\r\n// This is the standard setup for creating a Supabase server client\r\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\r\n// Updated for Next.js 15 async cookies requirement\r\nexport async function createSupabaseServerClientOnRequest() {\r\n  const cookieStore = await cookies();\r\n\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        get(name: string) {\r\n          return cookieStore.get(name)?.value;\r\n        },\r\n        set(name: string, value: string, options: CookieOptions) {\r\n          try {\r\n            cookieStore.set({ name, value, ...options });\r\n          } catch (error) {\r\n            // This error can be ignored if running in a Server Component\r\n            // where cookies can't be set directly. Cookie setting should be\r\n            // handled in Server Actions or Route Handlers.\r\n            console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\r\n          }\r\n        },\r\n        remove(name: string, options: CookieOptions) {\r\n          try {\r\n            // To remove a cookie using the `set` method from `next/headers`,\r\n            // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\r\n            cookieStore.set({ name, value: '', ...options });\r\n          } catch (error) {\r\n            // Similar to set, this might fail in a Server Component.\r\n            console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\r\n          }\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n\r\n// Alternative method for API routes that need to handle cookies from request\r\nexport function createSupabaseServerClientFromRequest(request: NextRequest) {\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        get(name: string) {\r\n          return request.cookies.get(name)?.value;\r\n        },\r\n        set(name: string, value: string, options: CookieOptions) {\r\n          // In API routes, we can't set cookies directly on the request\r\n          // This will be handled by the response\r\n        },\r\n        remove(name: string, options: CookieOptions) {\r\n          // In API routes, we can't remove cookies directly on the request\r\n          // This will be handled by the response\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n\r\n// Service role client for admin operations (OAuth token storage, etc.)\r\nexport function createServiceRoleClient() {\r\n  return createClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.SUPABASE_SERVICE_ROLE_KEY!,\r\n    {\r\n      auth: {\r\n        autoRefreshToken: false,\r\n        persistSession: false\r\n      }\r\n    }\r\n  );\r\n}\r\n"], "names": ["runtime", "authMiddleware", "ApiKeyAuthMiddleware", "RoleAssignmentSchema", "z", "role_name", "min", "api_key_id", "uuid", "priority", "int", "optional", "default", "AgentAssignmentSchema", "agent_number", "max", "agent_prompt", "RoutingConfigSchema", "routing_strategy", "settings", "load_balancing_method", "role_assignments", "enable_multi_role_orchestration", "synthesis_model", "agent_assignments", "complexity_threshold", "max_debate_rounds", "consensus_threshold", "fallback_strategy", "enable_caching", "cache_ttl_minutes", "timeout_seconds", "GET", "request", "params", "authResult", "authenticateRequest", "success", "NextResponse", "json", "error", "message", "type", "code", "status", "statusCode", "userApiKey", "userConfig", "ip<PERSON><PERSON><PERSON>", "configId", "supabase", "createSupabaseServerClientFromRequest", "data", "config", "from", "select", "eq", "user_id", "single", "logApiUsage", "modelUsed", "providerUsed", "catch", "console", "id", "object", "name", "headers", "PUT", "body", "validationResult", "safeParse", "details", "errors", "routingConfig", "config<PERSON><PERSON>r", "updatedConfig", "updateError", "update", "updated_at", "Date", "toISOString", "delete", "length", "roleAssignments", "map", "assignment", "custom_api_config_id", "insert", "agentAssignments", "OPTIONS", "createServerClient", "process", "cookies", "get", "value", "set", "options", "remove"], "sourceRoot": "", "ignoreList": []}