{"version": 3, "file": "edge-chunks/833.js", "mappings": "mHACA,IAAMA,EAAY,UAIZC,EAAgCC,QAAQC,GAAG,CAACC,oBAAoB,CAKtE,GAAI,CAACH,GAA0E,IAAI,CAA7CA,EAA8BI,MAAM,CACxE,MAAM,MAAU,mIAIlB,SAASC,EAAgBC,CAAW,EAClC,IAAMC,EAAQ,IAAIC,WAAWF,EAAIF,MAAM,CAAG,GAC1C,IAAK,IAAIK,EAAI,EAAGA,EAAIH,EAAIF,MAAM,CAAEK,GAAK,EAAG,CACjC,CAACA,EAAI,EAAE,CAAGC,SAASJ,EAAIK,MAAM,CAACF,EAAG,GAAI,IAE5C,OAAOF,CACT,CAGA,SAASK,EAAgBL,CAAiB,EACxC,OAAOM,MAAMC,IAAI,CAACP,EAAOQ,GAAQA,EAAKC,QAAQ,CAAC,IAAIC,QAAQ,CAAC,EAAG,MAAMC,IAAI,CAAC,GAC5E,CAEA,IAAMC,EAAWd,EAAgBL,GAE1B,eAAeoB,EAAQC,CAAY,EACxC,GAAoB,UAAhB,OAAOA,GAAqC,GAAG,CAAnBA,EAAKjB,MAAM,CACzC,MAAM,MAAU,gDAIlB,IAAMkB,EAAKC,OAAOC,eAAe,CAAC,IAAIhB,WAAWiB,KAG3CC,EAAY,MAAMH,OAAOI,MAAM,CAACC,SAAS,CAC7C,MACAT,EACA,CAAEU,KAAM9B,CAAU,GAClB,EACA,CAAC,UAAU,EAIP+B,EAAc,IAAIC,cAAcC,MAAM,CAACX,GAUvCY,EAAiB,IAAIzB,WATH,MAAMe,OAAOI,MAAM,CAACP,OAAO,CACjD,CACES,KAAM9B,EACNuB,GAAIA,CACN,EACAI,EACAI,IAMII,EAAgBD,EAAeE,KAAK,CAAC,EAAG,CAAC,IACzCC,EAAUH,EAAeE,KAAK,CAAC,CAAC,IAGtC,MAAO,GAAGvB,EAAgBU,GAAI,CAAC,EAAEV,EAAgBwB,GAAS,CAAC,EAAExB,EAAgBsB,GAAAA,CAAgB,CAGxF,eAAeG,EAAQC,CAAqB,EACjD,GAA6B,UAAzB,OAAOA,GAAuD,GAAG,CAA5BA,EAAclC,MAAM,CAC3D,MAAUmC,MAAM,gDAGlB,IAAMC,EAAQF,EAAcG,KAAK,CAAC,KAClC,GAAqB,GAAG,CAApBD,EAAMpC,MAAM,CACd,MAAM,MAAU,oEAGlB,IAAMkB,EAAKjB,EAAgBmC,CAAK,CAAC,EAAE,EAC7BJ,EAAU/B,EAAgBmC,CAAK,CAAC,EAAE,EAClCN,EAAgB7B,EAAgBmC,CAAK,CAAC,EAAE,EAE9C,GAhFgB,IAAI,CAgFhBlB,EAAGlB,MAAM,CACX,IADgBqB,EACV,MAAU,CAAC,EADU,0BACkB,EAAEA,UAAU,GAEpC,IAF2C,CAE9DW,EAAQhC,MAAM,CAChB,MAAM,MAAU,CAAC,0CAA0C,CAAC,EAI9D,IAAMsB,EAAY,MAAMH,OAAOI,MAAM,CAACC,SAAS,CAC7C,MACAT,EACA,CAAEU,KAAM9B,CAAU,GAClB,EACA,CAAC,UAAU,EAIP2C,EAAe,IAAIlC,WAAW0B,EAAc9B,MAAM,CAAGgC,EAAQhC,MAAM,EACzEsC,EAAaC,GAAG,CAACT,GACjBQ,EAAaC,GAAG,CAACP,EAASF,EAAc9B,MAAM,EAG9C,IAAMwC,EAAkB,MAAMrB,OAAOI,MAAM,CAACU,OAAO,CACjD,CACER,KAAM9B,EACNuB,GAAIA,CACN,EACAI,EACAgB,GAGF,OAAO,IAAIG,cAAcC,MAAM,CAACF,EAClC,4DChHO,OAAMG,cACaC,UAAAA,CAAa,uBACbC,kBAAAA,CAAqB,EAAG,YACxBC,gBADoD,EACpDA,CAAqB,GAAI,aAMpCC,WAN4D,KAWtE,CAGD,IAAMC,EAAYvC,MAAMC,IAAI,CADRS,OAAOC,eAAe,CAAC,IAAIhB,WAAW,IAAI,CAACyC,kBAAkB,CAAG,IAC1ClC,GAAQA,EAAKC,QAAQ,CAAC,IAAIC,QAAQ,CAAC,EAAG,MAAMC,IAAI,CAAC,IAGrFmC,EAAa,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACJ,kBAAkB,EAG9DK,EAAS,GAAG,IAAI,CAACP,UAAU,GAAGI,EAAAA,CAAW,CACzCI,EAAU,GAAGD,EAAO,CAAC,EAAEF,EAAAA,CAAY,CAGnCI,EAAO,MAAM,IAAI,CAACC,UAAU,CAACF,GAEnC,MAAO,SACLA,SACAD,aACAF,OACAI,CACF,CACF,CAOA,OAAeH,qBAAqBlD,CAAc,CAAU,CAC1D,IAAMuD,EAAQ,iEACVC,EAAS,GAEb,IAAK,IAAInD,EAAI,EAAGA,EAAIL,EAAQK,IAAK,CAE/B,IAAMoD,EADctC,OAAOC,eAAe,CAAC,IAAIhB,WAAW,GAC3B,CAAC,EAAE,CAAGmD,EAAMvD,MAAM,CACjDwD,GAAUD,CAAK,CAACE,EAAY,CAG9B,OAAOD,CACT,CAOA,aAAaF,WAAWI,CAAc,CAAmB,CAEvD,IAAMC,EAAOC,IADOjC,cACCC,MAAM,CAAC8B,GAG5B,OAAOjD,MAAMC,IAAI,CADC,IAAIN,WADH,MAAMe,OAAOI,MAAM,CAACsC,MAAM,CAAC,UAAWF,IAE5BhD,GAAQA,EAAKC,QAAQ,CAAC,IAAIC,QAAQ,CAAC,EAAG,MAAMC,IAAI,CAAC,GAChF,CAOA,OAAOgD,cAAcJ,CAAc,CAAW,CAE5C,OADgB,OAAW,CAAC,CAAC,EAAE,IAAI,CAACd,UAAU,CAAC,SAAS,EAAE,IAAI,CAACC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAACC,kBAAkB,CAAC,EAAE,CAAC,EAC9GiB,IAAI,CAACL,EACtB,CAOA,OAAOM,cAAcN,CAAc,CAAU,CAC3C,IAAMtB,EAAQsB,EAAOrB,KAAK,CAAC,YAC3B,EAAUrC,MAAM,EAAI,EACX,CADc,EACXoC,CAAK,CAAC,EAAE,CAAC,CAAC,EAAEA,CAAK,CAAC,EAAE,CAAC,CAAC,EAAEA,CAAK,CAAC,EAAE,EAAE,CAEvC,EACT,CAOA,aAAa6B,cAAchB,CAAkB,CAAmB,CAE9D,IAAMiB,EAASjB,EAAWlB,KAAK,CAAC,CAAC,GACjC,OAAO,MAAMf,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,CAACkD,EACvB,CAOA,aAAaC,cAAcC,CAAuB,CAAmB,CACnE,GAAI,CACF,OAAO,MAAMnC,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,CAACmC,EACvB,CAAE,MAAOC,EAAO,CAGd,MAAO,MACT,CACF,CAQA,aAAaC,gBAAgBnB,CAAc,CAAEiB,CAAuB,CAAmB,CACrF,IAAMF,EAAS,MAAM,IAAI,CAACC,aAAa,CAACC,GAElCG,EAAe,IAAI,CAACzB,kBAAkB,CAAG,EAC/C,MAAO,GAAGK,EAAO,CAAC,EAAE,IAAIqB,MAAM,CAACD,GAAAA,EAAgBL,EAAAA,CAAQ,CASzD,OAAOO,2BACLC,CAAwB,CACxBC,CAAuB,CAKvB,CAGA,IAAMC,EAAS,CACbC,KAAM,EACNC,QAAS,GACTC,aAAc,OACdC,WAAY,MACd,CADqB,CAGfC,EAAQL,CAAM,CAACF,EAAwC,EAAIE,EAAOC,IAAI,QAE5E,GAAuBI,EACd,CACLC,IAF0B,KAEjB,QACTD,EACAE,QAAS,CAAC,gEAAgE,EAAEF,EAAM,WAAW,EAAEP,EAAiB,MAAM,CACxH,EAGK,CACLQ,SAAS,QACTD,CACF,CACF,CAGF,sECrKO,OAAMG,EAGXC,aAAc,CACZ,IAAI,CAACC,QAAQ,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAYA,CAC1B1F,0CAAoC,CACpCA,QAAQC,GAAG,CAAC0F,yBAAyB,CAEzC,CAQA,MAAMC,eACJ/B,CAAc,CACdgC,CAAkB,CACe,CACjC,GAAI,CAEF,GAAI,CAAC/C,EAAAA,CAAeA,CAACmB,aAAa,CAACJ,GACjC,MAAO,CACLiC,SAAS,EACTtB,MAAO,wBACT,EAIF,IAAMuB,EAAU,MAAMjD,EAAAA,CAAeA,CAACW,UAAU,CAACI,GAG3C,CAAEC,KAAMkC,CAAU,CAAExB,MAAOyB,CAAQ,CAAE,CAAG,MAAM,IAAI,CAACR,QAAQ,CAC9D5E,IAAI,CAAC,2BACLqF,MAAM,CAAC,CAAC;;;;;;;;;QAST,CAAC,EACAC,EAAE,CAAC,WAAYJ,GACfI,EAAE,CAAC,SAAU,UACbC,MAAM,GAET,GAAIH,GAAY,CAACD,EACf,MAAO,CACLF,GAFyB,MAEhB,EACTtB,MAAO,6BACT,EAIF,GAAIwB,EAAWK,UAAU,EAAI,IAAIC,KAAKN,EAAWK,UAAU,EAAI,IAAIC,KAOjE,GAPyE,IAEzE,MAAM,IAAI,CAACb,QAAQ,CAChB5E,IAAI,CAAC,2BACL0F,MAAM,CAAC,CAAEC,OAAQ,SAAU,GAC3BL,EAAE,CAAC,KAAMH,EAAWS,EAAE,EAElB,CACLX,QAAS,GACTtB,MAAO,qBACT,EAIF,GAAIwB,EAAWU,WAAW,EAAIV,EAAWU,WAAW,CAACvG,MAAM,CAAG,GAAK0F,GAE7D,CADgB,IAAI,CAACc,EADmD,MAE1D,MADqB,CAACd,EAAWG,EAAWU,WAAW,EAEvE,MAAO,CACLZ,SAAS,EACTtB,MAAO,yCACT,EAOJ,OAFA,MAAM,IAAI,CAACoC,cAAc,CAACZ,EAAWS,EAAE,CAAEZ,GAElC,CACLC,SAAS,EACTjC,OAAQmC,CACV,CAEF,CAAE,MAAOxB,EAAO,CAEd,MAAO,CACLsB,SAAS,EACTtB,MAAO,yCACT,CACF,CACF,CAQA,eAAuBqB,CAAiB,CAAEgB,CAAoB,CAAW,CAGvE,OAAOA,EAAWC,QAAQ,CAACjB,IAAcgB,EAAWC,QAAQ,CAAC,IAC/D,CASA,MAAcF,eAAeG,CAAgB,CAAElB,CAAkB,CAAiB,CAEhF,GAAM,CAAE/B,KAAMkD,CAAW,CAAE,CAAG,MAAM,IAAI,CAACvB,QAAQ,CAC9C5E,IAAI,CAAC,2BACLqF,MAAM,CAAC,kBACPC,EAAE,CAAC,KAAMY,GACTX,MAAM,GAEHa,EAAkB,CACtBC,aAAc,IAAIZ,OAAOa,WAAW,GACpCC,eAAgB,CAACJ,GAAaI,iBAAkB,EAAK,CACvD,EAEIvB,IACFoB,EAAWI,KADE,OACU,CAAGxB,CAAAA,EAG5B,MAAM,IAAI,CAACJ,QAAQ,CAChB5E,IAAI,CAAC,2BACL0F,MAAM,CAACU,GACPd,EAAE,CAAC,KAAMY,EACd,CAWA,MAAMO,YACJP,CAAgB,CAChBQ,CAAc,CACdC,CAAgB,CAChBC,CAeC,CACc,CACf,MAAM,IAAI,CAAChC,QAAQ,CAChB5E,IAAI,CAAC,2BACL6G,MAAM,CAAC,CACNC,0BAA2BZ,EAC3Ba,QAASL,EACTM,qBAAsBL,EACtBM,SAAUL,EAAQK,QAAQ,CAC1BC,YAAaN,EAAQO,MAAM,CAC3BC,YAAaR,EAAQS,UAAU,CAC/BC,WAAYV,EAAQ5B,SAAS,CAC7BuC,WAAYX,EAAQY,SAAS,CAC7BC,QAASb,EAAQa,OAAO,CACxBC,WAAYd,EAAQe,SAAS,CAC7BC,cAAehB,EAAQiB,YAAY,CACnCC,cAAelB,EAAQmB,YAAY,CACnCC,kBAAmBpB,EAAQqB,gBAAgB,CAC3CC,SAAUtB,EAAQuB,OAAO,CACzBC,iBAAkBxB,EAAQyB,cAAc,CACxCC,cAAe1B,EAAQ2B,YAAY,CACnCC,WAAY5B,EAAQ6B,SAAS,EAEnC,CACF,CCxLO,MAAMC,EAGX/D,aAAc,CACZ,IAAI,CAACgE,SAAS,CAAG,IAAIjE,CACvB,CAQA,aATsCA,CAShBkE,CAAoB,CAAiB,CAEzD,IAAMC,EAAaD,EAAQE,OAAO,CAACC,GAAG,CAAC,iBACvC,GAAIF,EAAY,CACd,IAAMG,EAAQH,EAAWG,KAAK,CAAC,oBAC/B,GAAIA,EACF,KADS,EACFA,CAAK,CAAC,EAAE,CAKnB,IAAMC,EAAeL,EAAQE,OAAO,CAACC,GAAG,CAAC,oBACzC,GAIO,IACT,CAOA,MAZoB,MAYAH,CAAoB,CAAU,CAEhD,IAAMM,EAAeN,EAAQE,OAAO,CAACC,GAAG,CAAC,mBACzC,GAAIG,EACF,OAAOA,EAAavH,GADJ,EACS,CAAC,IAAI,CAAC,EAAE,CAACwH,IAAI,GAGxC,IAAMC,EAASR,EAAQE,OAAO,CAACC,GAAG,CAAC,aACnC,GAAIK,EACF,MADU,CACHA,EAGT,IAAMC,EAAiBT,EAAQE,OAAO,CAACC,GAAG,CAAC,2BAC3C,GAKO,WACT,CAOA,CAbsB,KAahBO,oBAAoBV,CAAoB,CAO3C,CACD,GAAI,CAEF,IAAM5F,EAAS,IAAI,CAACuG,aAAa,CAACX,GAClC,GAAI,CAAC5F,EACH,MAAO,CACLwG,SAAS,EACT7F,MAAO,2GACP0D,WAAY,GACd,EAIF,IAAMrC,EAAY,IAAI,CAACyE,WAAW,CAACb,GAG7Bc,EAAmB,MAAM,IAAI,CAACf,SAAS,CAAC5D,cAAc,CAAC/B,EAAQgC,GAErE,GAAI,CAAC0E,EAAiBzE,OAAO,CAAE,CAC7B,IAAIoC,EAAa,IASjB,OANIqC,EAAiB/F,KAAK,EAAEsC,SAAS,WACnCoB,CAD+C,CAClC,IACJqC,EAAiB/F,KAAK,EAAEsC,SAAS,2BAA2B,CACrEoB,EAAa,KAGR,CACLmC,SAAS,EACT7F,MAAO+F,EAAiB/F,KAAK,EAAI,6BACjC0D,CACF,CACF,CAEA,MAAO,CACLmC,SAAS,EACTG,WAAYD,EAAiB1G,MAAM,CACnC4G,WAAY,EAAkB5G,MAAM,CAAS6G,kBAAkB,CAC/D7E,WACF,CAEF,CAAE,MAAOrB,EAAO,CAEd,MAAO,CACL6F,SAAS,EACT7F,MAAO,gCACP0D,WAAY,GACd,CACF,CACF,CAQA,MAAMZ,YACJkD,CAA+B,CAC/Bf,CAAoB,CACpBkB,CAUC,CACD9E,CAAkB,CACH,CACf,GAAI,CACF,IAAM+E,EAAM,IAAIC,IAAIpB,EAAQmB,GAAG,CAE/B,OAAM,IAAI,CAACpB,SAAS,CAAClC,WAAW,CAC9BkD,EAAW/D,EAAE,CACb+D,EAAW5C,OAAO,CAClB4C,EAAW3C,oBAAoB,CAC/B,CACEC,SAAU8C,EAAIE,QAAQ,CACtB9C,OAAQyB,EAAQzB,MAAM,CACtBE,WAAYyC,EAASzC,UAAU,WAC/BrC,EACAwC,UAAWoB,EAAQE,OAAO,CAACC,GAAG,CAAC,oBAAiBmB,EAChDzC,QAASmB,EAAQE,OAAO,CAACC,GAAG,CAAC,iBAAcmB,EAC3CvC,UAAWmC,EAASnC,SAAS,CAC7BE,aAAciC,EAASjC,YAAY,CACnCE,aAAc+B,EAAS/B,YAAY,CACnCE,iBAAkB6B,EAAS7B,gBAAgB,CAC3CE,QAAS2B,EAAS3B,OAAO,CACzBE,eAAgByB,EAASzB,cAAc,CACvCE,aAAcuB,EAASvB,YAAY,CACnCE,UAAWqB,EAASrB,SAAS,EAGnC,CAAE,MAAO9E,EAAO,CAGhB,CACF,CAQAwG,cAAcR,CAA+B,CAAES,CAAiB,CAAW,CACzE,IAAMC,EAAcV,EAAWU,WAAW,CAE1C,OAAQD,GACN,IAAK,OACH,OAA4B,IAArBC,EAAYC,IAAI,KACpB,YACH,OAAiC,IAA1BD,EAAYE,SAAS,KACzB,aACH,OAAkC,IAA3BF,EAAYG,UAAU,SAE7B,OAAO,CACX,CACF,CAQAC,gBAAgBd,CAA+B,CAAEe,CAAqB,CAAW,CAC/E,GAAI,CAACA,EAAQ,OAAO,EAEpB,IAF0B,EAEHf,EAAWgB,eAAe,OACjD,CAAKC,GAAD,GAAgD,CAA7BA,EAAetL,MAAM,EAKrCsL,EAAeC,IAAI,CAACC,IACzB,EATiF,CASlE,MAAXA,EAAgB,OAAO,EAC3B,GAAIA,EAAOC,UAAU,CAAC,MAAO,CAE3B,IAAMC,EAAaF,EAAOzJ,KAAK,CAAC,GAChC,OAAOqJ,EAAOO,QAAQ,CAACD,EACzB,CACA,OAAON,IAAWI,GAAUJ,IAAW,CAAC,QAAQ,EAAEI,EAAAA,CAAQ,EAAIJ,IAAW,CAAC,OAAO,EAAEI,EAAAA,CAAQ,EAE/F,CAQAI,oBACEvH,CAAa,CACb0D,CAAkB,CACR,CAeV,OAAO,IAAI8D,SAASC,KAAKC,SAAS,CAdhB,CAChB1H,MAAO,CACLc,QAASd,EACT2H,KAAM,IAAI,CAACC,YAAY,CAAClE,GACxBmE,KAAMnE,CACR,CACF,GAQ0C,CACxC1B,OAAQ0B,EACRyB,QARsC,CACtC,eAAgB,kBAClB,CAOA,EACF,CAOA,aAAqBzB,CAAkB,CAAU,CAC/C,OAAQA,GACN,KAAK,IACH,MAAO,sBACT,MAAK,IACH,MAAO,mBAET,MAAK,IACH,MAAO,gBACT,SACE,MAAO,WACX,CACF,CACF", "sources": ["webpack://_N_E/./src/lib/encryption.ts", "webpack://_N_E/./src/lib/userApiKeys/apiKeyGenerator.ts", "webpack://_N_E/./src/lib/userApiKeys/apiKeyValidator.ts", "webpack://_N_E/./src/lib/userApiKeys/authMiddleware.ts"], "sourcesContent": ["// Web Crypto API compatible encryption for Edge Runtime\r\nconst ALGORITHM = 'AES-GCM';\r\nconst IV_LENGTH = 12; // Recommended for GCM\r\n\r\n// Ensure your ROKEY_ENCRYPTION_KEY is a 64-character hex string (32 bytes)\r\nconst ROKEY_ENCRYPTION_KEY_FROM_ENV = process.env.ROKEY_ENCRYPTION_KEY;\r\n\r\nconsole.log('[DEBUG] ROKEY_ENCRYPTION_KEY from process.env:', ROKEY_ENCRYPTION_KEY_FROM_ENV);\r\nconsole.log('[DEBUG] Length:', ROKEY_ENCRYPTION_KEY_FROM_ENV?.length);\r\n\r\nif (!ROKEY_ENCRYPTION_KEY_FROM_ENV || ROKEY_ENCRYPTION_KEY_FROM_ENV.length !== 64) {\r\n  throw new Error('Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.');\r\n}\r\n\r\n// Convert hex string to Uint8Array for Web Crypto API\r\nfunction hexToUint8Array(hex: string): Uint8Array {\r\n  const bytes = new Uint8Array(hex.length / 2);\r\n  for (let i = 0; i < hex.length; i += 2) {\r\n    bytes[i / 2] = parseInt(hex.substr(i, 2), 16);\r\n  }\r\n  return bytes;\r\n}\r\n\r\n// Convert Uint8Array to hex string\r\nfunction uint8ArrayToHex(bytes: Uint8Array): string {\r\n  return Array.from(bytes, byte => byte.toString(16).padStart(2, '0')).join('');\r\n}\r\n\r\nconst keyBytes = hexToUint8Array(ROKEY_ENCRYPTION_KEY_FROM_ENV);\r\n\r\nexport async function encrypt(text: string): Promise<string> {\r\n  if (typeof text !== 'string' || text.length === 0) {\r\n    throw new Error('Encryption input must be a non-empty string.');\r\n  }\r\n\r\n  // Generate random IV\r\n  const iv = crypto.getRandomValues(new Uint8Array(IV_LENGTH));\r\n\r\n  // Import the key for Web Crypto API\r\n  const cryptoKey = await crypto.subtle.importKey(\r\n    'raw',\r\n    keyBytes,\r\n    { name: ALGORITHM },\r\n    false,\r\n    ['encrypt']\r\n  );\r\n\r\n  // Encrypt the text\r\n  const encodedText = new TextEncoder().encode(text);\r\n  const encryptedBuffer = await crypto.subtle.encrypt(\r\n    {\r\n      name: ALGORITHM,\r\n      iv: iv\r\n    },\r\n    cryptoKey,\r\n    encodedText\r\n  );\r\n\r\n  const encryptedArray = new Uint8Array(encryptedBuffer);\r\n\r\n  // For AES-GCM, the auth tag is included in the encrypted data (last 16 bytes)\r\n  const encryptedData = encryptedArray.slice(0, -16);\r\n  const authTag = encryptedArray.slice(-16);\r\n\r\n  // Return IV:authTag:encryptedData format\r\n  return `${uint8ArrayToHex(iv)}:${uint8ArrayToHex(authTag)}:${uint8ArrayToHex(encryptedData)}`;\r\n}\r\n\r\nexport async function decrypt(encryptedText: string): Promise<string> {\r\n  if (typeof encryptedText !== 'string' || encryptedText.length === 0) {\r\n    throw new Error('Decryption input must be a non-empty string.');\r\n  }\r\n\r\n  const parts = encryptedText.split(':');\r\n  if (parts.length !== 3) {\r\n    throw new Error('Invalid encrypted text format. Expected iv:authTag:encryptedData');\r\n  }\r\n\r\n  const iv = hexToUint8Array(parts[0]);\r\n  const authTag = hexToUint8Array(parts[1]);\r\n  const encryptedData = hexToUint8Array(parts[2]);\r\n\r\n  if (iv.length !== IV_LENGTH) {\r\n    throw new Error(`Invalid IV length. Expected ${IV_LENGTH} bytes.`);\r\n  }\r\n  if (authTag.length !== 16) {\r\n    throw new Error(`Invalid authTag length. Expected 16 bytes.`);\r\n  }\r\n\r\n  // Import the key for Web Crypto API\r\n  const cryptoKey = await crypto.subtle.importKey(\r\n    'raw',\r\n    keyBytes,\r\n    { name: ALGORITHM },\r\n    false,\r\n    ['decrypt']\r\n  );\r\n\r\n  // Combine encrypted data and auth tag for Web Crypto API\r\n  const combinedData = new Uint8Array(encryptedData.length + authTag.length);\r\n  combinedData.set(encryptedData);\r\n  combinedData.set(authTag, encryptedData.length);\r\n\r\n  // Decrypt the data\r\n  const decryptedBuffer = await crypto.subtle.decrypt(\r\n    {\r\n      name: ALGORITHM,\r\n      iv: iv\r\n    },\r\n    cryptoKey,\r\n    combinedData\r\n  );\r\n\r\n  return new TextDecoder().decode(decryptedBuffer);\r\n}", "import { encrypt, decrypt } from '@/lib/encryption';\n\nexport class Api<PERSON>eyGenerator {\n  private static readonly KEY_PREFIX = 'rk_live_';\n  private static readonly RANDOM_PART_LENGTH = 8; // hex chars for middle part\n  private static readonly SECRET_PART_LENGTH = 32; // chars for secret part\n\n  /**\n   * Generates a new API key with the format: rk_live_{8_hex_chars}_{32_random_chars}\n   * @returns Object containing the full key, prefix, and secret parts\n   */\n  static async generateApiKey(): Promise<{\n    fullKey: string;\n    prefix: string;\n    secretPart: string;\n    hash: string;\n  }> {\n    // Generate random hex for the middle part (visible in prefix)\n    const randomBytes = crypto.getRandomValues(new Uint8Array(this.RANDOM_PART_LENGTH / 2));\n    const randomHex = Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join('');\n\n    // Generate random alphanumeric for the secret part\n    const secretPart = this.generateRandomString(this.SECRET_PART_LENGTH);\n\n    // Construct the full key\n    const prefix = `${this.KEY_PREFIX}${randomHex}`;\n    const fullKey = `${prefix}_${secretPart}`;\n\n    // Generate hash for storage\n    const hash = await this.hashApiKey(fullKey);\n\n    return {\n      fullKey,\n      prefix,\n      secretPart,\n      hash\n    };\n  }\n\n  /**\n   * Generates a cryptographically secure random string\n   * @param length Length of the string to generate\n   * @returns Random alphanumeric string\n   */\n  private static generateRandomString(length: number): string {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n    let result = '';\n\n    for (let i = 0; i < length; i++) {\n      const randomBytes = crypto.getRandomValues(new Uint8Array(1));\n      const randomIndex = randomBytes[0] % chars.length;\n      result += chars[randomIndex];\n    }\n\n    return result;\n  }\n\n  /**\n   * Creates a SHA-256 hash of the API key for secure storage\n   * @param apiKey The full API key to hash\n   * @returns SHA-256 hash as hex string\n   */\n  static async hashApiKey(apiKey: string): Promise<string> {\n    const encoder = new TextEncoder();\n    const data = encoder.encode(apiKey);\n    const hashBuffer = await crypto.subtle.digest('SHA-256', data);\n    const hashArray = new Uint8Array(hashBuffer);\n    return Array.from(hashArray, byte => byte.toString(16).padStart(2, '0')).join('');\n  }\n\n  /**\n   * Validates the format of an API key\n   * @param apiKey The API key to validate\n   * @returns True if the format is valid\n   */\n  static isValidFormat(apiKey: string): boolean {\n    const pattern = new RegExp(`^${this.KEY_PREFIX}[a-f0-9]{${this.RANDOM_PART_LENGTH}}_[a-zA-Z0-9]{${this.SECRET_PART_LENGTH}}$`);\n    return pattern.test(apiKey);\n  }\n\n  /**\n   * Extracts the prefix from a full API key\n   * @param apiKey The full API key\n   * @returns The prefix part (e.g., \"rk_live_abc12345\")\n   */\n  static extractPrefix(apiKey: string): string {\n    const parts = apiKey.split('_');\n    if (parts.length >= 3) {\n      return `${parts[0]}_${parts[1]}_${parts[2]}`;\n    }\n    return '';\n  }\n\n  /**\n   * Encrypts the suffix part of an API key for partial display\n   * @param secretPart The secret part of the API key\n   * @returns Encrypted suffix for storage\n   */\n  static async encryptSuffix(secretPart: string): Promise<string> {\n    // Take last 4 characters for display purposes\n    const suffix = secretPart.slice(-4);\n    return await encrypt(suffix);\n  }\n\n  /**\n   * Decrypts the suffix for display\n   * @param encryptedSuffix The encrypted suffix from database\n   * @returns Decrypted suffix for display\n   */\n  static async decryptSuffix(encryptedSuffix: string): Promise<string> {\n    try {\n      return await decrypt(encryptedSuffix);\n    } catch (error) {\n      console.error('Failed to decrypt API key suffix:', error);\n      // Return a placeholder that looks like the last 4 chars\n      return 'xxxx';\n    }\n  }\n\n  /**\n   * Creates a masked version of the API key for display\n   * @param prefix The key prefix\n   * @param encryptedSuffix The encrypted suffix\n   * @returns Masked key for display (e.g., \"rk_live_abc12345_****xyz\")\n   */\n  static async createMaskedKey(prefix: string, encryptedSuffix: string): Promise<string> {\n    const suffix = await this.decryptSuffix(encryptedSuffix);\n    // Show 4 chars at the end, mask the rest (32 - 4 = 28 chars to mask)\n    const maskedLength = this.SECRET_PART_LENGTH - 4;\n    return `${prefix}_${'*'.repeat(maskedLength)}${suffix}`;\n  }\n\n  /**\n   * Validates subscription tier limits for API key generation\n   * @param subscriptionTier User's subscription tier\n   * @param currentKeyCount Current number of API keys for the user\n   * @returns Object indicating if generation is allowed and any limits\n   */\n  static validateSubscriptionLimits(\n    subscriptionTier: string,\n    currentKeyCount: number\n  ): {\n    allowed: boolean;\n    limit: number;\n    message?: string;\n  } {\n    // User-generated API keys limits (separate from API keys per config)\n    // These are total API keys a user can generate across all their configs\n    const limits = {\n      free: 3, // Free users get 3 total user-generated API keys\n      starter: 50, // Starter users get 50 total user-generated API keys\n      professional: 999999, // Professional users get unlimited user-generated API keys\n      enterprise: 999999 // Enterprise users get unlimited user-generated API keys\n    };\n\n    const limit = limits[subscriptionTier as keyof typeof limits] || limits.free;\n\n    if (currentKeyCount >= limit) {\n      return {\n        allowed: false,\n        limit,\n        message: `You have reached the maximum number of user-generated API keys (${limit}) for your ${subscriptionTier} plan.`\n      };\n    }\n\n    return {\n      allowed: true,\n      limit\n    };\n  }\n\n\n}\n", "import { createClient } from '@supabase/supabase-js';\nimport { Api<PERSON>eyGenerator } from './apiKeyGenerator';\nimport type {\n  UserGeneratedApiKey,\n  ApiKeyValidationResult\n} from '@/types/userApiKeys';\n\nexport class Api<PERSON>eyValidator {\n  private supabase;\n\n  constructor() {\n    this.supabase = createClient(\n      process.env.NEXT_PUBLIC_SUPABASE_URL!,\n      process.env.SUPABASE_SERVICE_ROLE_KEY!\n    );\n  }\n\n  /**\n   * Validates an API key and checks rate limits\n   * @param apiKey The API key to validate\n   * @param ipAddress The IP address making the request\n   * @returns Validation result with key data and rate limit status\n   */\n  async validateApiKey(\n    apiKey: string,\n    ipAddress?: string\n  ): Promise<ApiKeyValidationResult> {\n    try {\n      // First check format\n      if (!ApiKeyGenerator.isValidFormat(apiKey)) {\n        return {\n          isValid: false,\n          error: 'Invalid API key format'\n        };\n      }\n\n      // Hash the key for lookup\n      const keyHash = await ApiKeyGenerator.hashApiKey(apiKey);\n\n      // Look up the key in the database\n      const { data: apiKeyData, error: keyError } = await this.supabase\n        .from('user_generated_api_keys')\n        .select(`\n          *,\n          custom_api_configs!inner(\n            id,\n            name,\n            user_id,\n            routing_strategy,\n            routing_strategy_params\n          )\n        `)\n        .eq('key_hash', keyHash)\n        .eq('status', 'active')\n        .single();\n\n      if (keyError || !apiKeyData) {\n        return {\n          isValid: false,\n          error: 'Invalid or inactive API key'\n        };\n      }\n\n      // Check if key has expired\n      if (apiKeyData.expires_at && new Date(apiKeyData.expires_at) < new Date()) {\n        // Mark key as expired\n        await this.supabase\n          .from('user_generated_api_keys')\n          .update({ status: 'expired' })\n          .eq('id', apiKeyData.id);\n\n        return {\n          isValid: false,\n          error: 'API key has expired'\n        };\n      }\n\n      // Check IP restrictions\n      if (apiKeyData.allowed_ips && apiKeyData.allowed_ips.length > 0 && ipAddress) {\n        const isIpAllowed = this.checkIpAllowed(ipAddress, apiKeyData.allowed_ips);\n        if (!isIpAllowed) {\n          return {\n            isValid: false,\n            error: 'IP address not allowed for this API key'\n          };\n        }\n      }\n\n      // Update last used timestamp and IP\n      await this.updateLastUsed(apiKeyData.id, ipAddress);\n\n      return {\n        isValid: true,\n        apiKey: apiKeyData as UserGeneratedApiKey\n      };\n\n    } catch (error) {\n      console.error('Error validating API key:', error);\n      return {\n        isValid: false,\n        error: 'Internal server error during validation'\n      };\n    }\n  }\n\n  /**\n   * Checks if an IP address is allowed based on the allowed IPs list\n   * @param ipAddress The IP address to check\n   * @param allowedIps Array of allowed IP addresses/CIDR blocks\n   * @returns True if IP is allowed\n   */\n  private checkIpAllowed(ipAddress: string, allowedIps: string[]): boolean {\n    // For now, implement simple exact match\n    // TODO: Add CIDR block support for more advanced IP filtering\n    return allowedIps.includes(ipAddress) || allowedIps.includes('*');\n  }\n\n\n\n  /**\n   * Updates the last used timestamp and IP for an API key\n   * @param apiKeyId The API key ID\n   * @param ipAddress The IP address (optional)\n   */\n  private async updateLastUsed(apiKeyId: string, ipAddress?: string): Promise<void> {\n    // First get the current total_requests count\n    const { data: currentData } = await this.supabase\n      .from('user_generated_api_keys')\n      .select('total_requests')\n      .eq('id', apiKeyId)\n      .single();\n\n    const updateData: any = {\n      last_used_at: new Date().toISOString(),\n      total_requests: (currentData?.total_requests || 0) + 1\n    };\n\n    if (ipAddress) {\n      updateData.last_used_ip = ipAddress;\n    }\n\n    await this.supabase\n      .from('user_generated_api_keys')\n      .update(updateData)\n      .eq('id', apiKeyId);\n  }\n\n\n\n  /**\n   * Logs API usage for analytics and monitoring\n   * @param apiKeyId The API key ID\n   * @param userId The user ID\n   * @param configId The custom API config ID\n   * @param logData Usage log data\n   */\n  async logApiUsage(\n    apiKeyId: string,\n    userId: string,\n    configId: string,\n    logData: {\n      endpoint: string;\n      method: string;\n      statusCode?: number;\n      ipAddress?: string;\n      userAgent?: string;\n      referer?: string;\n      modelUsed?: string;\n      providerUsed?: string;\n      tokensPrompt?: number;\n      tokensCompletion?: number;\n      costUsd?: number;\n      responseTimeMs?: number;\n      errorMessage?: string;\n      errorType?: string;\n    }\n  ): Promise<void> {\n    await this.supabase\n      .from('user_api_key_usage_logs')\n      .insert({\n        user_generated_api_key_id: apiKeyId,\n        user_id: userId,\n        custom_api_config_id: configId,\n        endpoint: logData.endpoint,\n        http_method: logData.method,\n        status_code: logData.statusCode,\n        ip_address: logData.ipAddress,\n        user_agent: logData.userAgent,\n        referer: logData.referer,\n        model_used: logData.modelUsed,\n        provider_used: logData.providerUsed,\n        tokens_prompt: logData.tokensPrompt,\n        tokens_completion: logData.tokensCompletion,\n        cost_usd: logData.costUsd,\n        response_time_ms: logData.responseTimeMs,\n        error_message: logData.errorMessage,\n        error_type: logData.errorType\n      });\n  }\n}\n", "import { type NextRequest } from 'next/server';\nimport { ApiKeyValidator } from './apiKeyValidator';\nimport type { UserGeneratedApiKey, ApiKeyValidationResult } from '@/types/userApiKeys';\n\nexport interface AuthenticatedRequest extends NextRequest {\n  userApiKey?: UserGeneratedApiKey;\n  userConfig?: {\n    id: string;\n    name: string;\n    user_id: string;\n    routing_strategy: string;\n    routing_strategy_params: any;\n  };\n}\n\nexport class ApiKeyAuthMiddleware {\n  private validator: ApiKeyValidator;\n\n  constructor() {\n    this.validator = new ApiKeyValidator();\n  }\n\n  /**\n   * Extracts API key from request headers\n   * Supports both Authorization header and x-api-key header\n   * @param request The incoming request\n   * @returns The API key or null if not found\n   */\n  private extractApiKey(request: NextRequest): string | null {\n    // Check Authorization header (Bearer token format)\n    const authHeader = request.headers.get('authorization');\n    if (authHeader) {\n      const match = authHeader.match(/^Bearer\\s+(.+)$/i);\n      if (match) {\n        return match[1];\n      }\n    }\n\n    // Check x-api-key header\n    const apiKeyHeader = request.headers.get('x-api-key');\n    if (apiKeyHeader) {\n      return apiKeyHeader;\n    }\n\n    return null;\n  }\n\n  /**\n   * Gets the client IP address from the request\n   * @param request The incoming request\n   * @returns The IP address\n   */\n  private getClientIp(request: NextRequest): string {\n    // Check various headers for the real IP\n    const forwardedFor = request.headers.get('x-forwarded-for');\n    if (forwardedFor) {\n      return forwardedFor.split(',')[0].trim();\n    }\n\n    const realIp = request.headers.get('x-real-ip');\n    if (realIp) {\n      return realIp;\n    }\n\n    const cfConnectingIp = request.headers.get('cf-connecting-ip');\n    if (cfConnectingIp) {\n      return cfConnectingIp;\n    }\n\n    // Fallback to a default IP if none found\n    return '127.0.0.1';\n  }\n\n  /**\n   * Authenticates a request using user-generated API key\n   * @param request The incoming request\n   * @returns Authentication result with user data and config\n   */\n  async authenticateRequest(request: NextRequest): Promise<{\n    success: boolean;\n    error?: string;\n    statusCode?: number;\n    userApiKey?: UserGeneratedApiKey;\n    userConfig?: any;\n    ipAddress?: string;\n  }> {\n    try {\n      // Extract API key from request\n      const apiKey = this.extractApiKey(request);\n      if (!apiKey) {\n        return {\n          success: false,\n          error: 'API key is required. Provide it in Authorization header as \"Bearer YOUR_API_KEY\" or in x-api-key header.',\n          statusCode: 401\n        };\n      }\n\n      // Get client IP\n      const ipAddress = this.getClientIp(request);\n\n      // Validate the API key\n      const validationResult = await this.validator.validateApiKey(apiKey, ipAddress);\n\n      if (!validationResult.isValid) {\n        let statusCode = 401;\n\n        // Set appropriate status code based on error type\n        if (validationResult.error?.includes('expired')) {\n          statusCode = 401;\n        } else if (validationResult.error?.includes('IP address not allowed')) {\n          statusCode = 403;\n        }\n\n        return {\n          success: false,\n          error: validationResult.error || 'Invalid API key',\n          statusCode\n        };\n      }\n\n      return {\n        success: true,\n        userApiKey: validationResult.apiKey!,\n        userConfig: (validationResult.apiKey as any).custom_api_configs,\n        ipAddress\n      };\n\n    } catch (error) {\n      console.error('Error in API key authentication:', error);\n      return {\n        success: false,\n        error: 'Internal authentication error',\n        statusCode: 500\n      };\n    }\n  }\n\n  /**\n   * Logs API usage for monitoring and analytics\n   * @param userApiKey The authenticated API key\n   * @param request The request object\n   * @param response Response details\n   */\n  async logApiUsage(\n    userApiKey: UserGeneratedApiKey,\n    request: NextRequest,\n    response: {\n      statusCode: number;\n      modelUsed?: string;\n      providerUsed?: string;\n      tokensPrompt?: number;\n      tokensCompletion?: number;\n      costUsd?: number;\n      responseTimeMs?: number;\n      errorMessage?: string;\n      errorType?: string;\n    },\n    ipAddress?: string\n  ): Promise<void> {\n    try {\n      const url = new URL(request.url);\n      \n      await this.validator.logApiUsage(\n        userApiKey.id,\n        userApiKey.user_id,\n        userApiKey.custom_api_config_id,\n        {\n          endpoint: url.pathname,\n          method: request.method,\n          statusCode: response.statusCode,\n          ipAddress,\n          userAgent: request.headers.get('user-agent') || undefined,\n          referer: request.headers.get('referer') || undefined,\n          modelUsed: response.modelUsed,\n          providerUsed: response.providerUsed,\n          tokensPrompt: response.tokensPrompt,\n          tokensCompletion: response.tokensCompletion,\n          costUsd: response.costUsd,\n          responseTimeMs: response.responseTimeMs,\n          errorMessage: response.errorMessage,\n          errorType: response.errorType\n        }\n      );\n    } catch (error) {\n      console.error('Error logging API usage:', error);\n      // Don't throw here as this shouldn't break the main request\n    }\n  }\n\n  /**\n   * Checks if the API key has permission for a specific operation\n   * @param userApiKey The API key to check\n   * @param operation The operation to check (e.g., 'chat', 'streaming')\n   * @returns True if permission is granted\n   */\n  hasPermission(userApiKey: UserGeneratedApiKey, operation: string): boolean {\n    const permissions = userApiKey.permissions;\n    \n    switch (operation) {\n      case 'chat':\n        return permissions.chat === true;\n      case 'streaming':\n        return permissions.streaming === true;\n      case 'all_models':\n        return permissions.all_models === true;\n      default:\n        return false;\n    }\n  }\n\n  /**\n   * Validates CORS origin against allowed domains\n   * @param userApiKey The API key to check\n   * @param origin The request origin\n   * @returns True if origin is allowed\n   */\n  isOriginAllowed(userApiKey: UserGeneratedApiKey, origin: string | null): boolean {\n    if (!origin) return true; // Allow requests without origin (e.g., server-to-server)\n    \n    const allowedDomains = userApiKey.allowed_domains;\n    if (!allowedDomains || allowedDomains.length === 0) {\n      return true; // No restrictions\n    }\n\n    // Check if origin matches any allowed domain\n    return allowedDomains.some(domain => {\n      if (domain === '*') return true;\n      if (domain.startsWith('*.')) {\n        // Wildcard subdomain matching\n        const baseDomain = domain.slice(2);\n        return origin.endsWith(baseDomain);\n      }\n      return origin === domain || origin === `https://${domain}` || origin === `http://${domain}`;\n    });\n  }\n\n  /**\n   * Creates a standardized error response for API key authentication failures\n   * @param error The error message\n   * @param statusCode The HTTP status code\n   * @returns Response object\n   */\n  createErrorResponse(\n    error: string,\n    statusCode: number\n  ): Response {\n    const body: any = {\n      error: {\n        message: error,\n        type: this.getErrorType(statusCode),\n        code: statusCode\n      }\n    };\n\n    const headers: Record<string, string> = {\n      'Content-Type': 'application/json'\n    };\n\n\n\n    return new Response(JSON.stringify(body), {\n      status: statusCode,\n      headers\n    });\n  }\n\n  /**\n   * Gets the error type based on status code\n   * @param statusCode The HTTP status code\n   * @returns Error type string\n   */\n  private getErrorType(statusCode: number): string {\n    switch (statusCode) {\n      case 401:\n        return 'authentication_error';\n      case 403:\n        return 'permission_denied';\n\n      case 500:\n        return 'internal_error';\n      default:\n        return 'api_error';\n    }\n  }\n}\n"], "names": ["ALGORITHM", "ROKEY_ENCRYPTION_KEY_FROM_ENV", "process", "env", "ROKEY_ENCRYPTION_KEY", "length", "hexToUint8Array", "hex", "bytes", "Uint8Array", "i", "parseInt", "substr", "uint8ArrayToHex", "Array", "from", "byte", "toString", "padStart", "join", "keyBytes", "encrypt", "text", "iv", "crypto", "getRandomValues", "IV_LENGTH", "cryptoKey", "subtle", "importKey", "name", "encodedText", "TextEncoder", "encode", "encryptedArray", "encryptedData", "slice", "authTag", "decrypt", "encryptedText", "Error", "parts", "split", "combinedData", "set", "decryptedBuffer", "TextDecoder", "decode", "ApiKeyGenerator", "KEY_PREFIX", "RANDOM_PART_LENGTH", "SECRET_PART_LENGTH", "generateApiKey", "randomHex", "secretPart", "generateRandomString", "prefix", "<PERSON><PERSON><PERSON>", "hash", "hashApiKey", "chars", "result", "randomIndex", "<PERSON><PERSON><PERSON><PERSON>", "data", "encoder", "digest", "isValidFormat", "test", "extractPrefix", "encryptSuffix", "suffix", "decryptSuffix", "encryptedSuffix", "error", "createMaskedKey", "<PERSON><PERSON><PERSON><PERSON>", "repeat", "validateSubscriptionLimits", "subscriptionTier", "currentKeyCount", "limits", "free", "starter", "professional", "enterprise", "limit", "allowed", "message", "ApiKeyValidator", "constructor", "supabase", "createClient", "SUPABASE_SERVICE_ROLE_KEY", "validate<PERSON><PERSON><PERSON><PERSON>", "ip<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "keyHash", "apiKeyData", "keyError", "select", "eq", "single", "expires_at", "Date", "update", "status", "id", "allowed_ips", "checkIpAllowed", "updateLastUsed", "allowedIps", "includes", "apiKeyId", "currentData", "updateData", "last_used_at", "toISOString", "total_requests", "last_used_ip", "logApiUsage", "userId", "configId", "logData", "insert", "user_generated_api_key_id", "user_id", "custom_api_config_id", "endpoint", "http_method", "method", "status_code", "statusCode", "ip_address", "user_agent", "userAgent", "referer", "model_used", "modelUsed", "provider_used", "providerUsed", "tokens_prompt", "tokensPrompt", "tokens_completion", "tokensCompletion", "cost_usd", "costUsd", "response_time_ms", "responseTimeMs", "error_message", "errorMessage", "error_type", "errorType", "ApiKeyAuthMiddleware", "validator", "request", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "headers", "get", "match", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardedFor", "trim", "realIp", "cfConnectingIp", "authenticateRequest", "extractApiKey", "success", "getClientIp", "validationResult", "userApiKey", "userConfig", "custom_api_configs", "response", "url", "URL", "pathname", "undefined", "hasPermission", "operation", "permissions", "chat", "streaming", "all_models", "isOriginAllowed", "origin", "allowed_domains", "allowedDomains", "some", "domain", "startsWith", "baseDomain", "endsWith", "createErrorResponse", "Response", "JSON", "stringify", "type", "getErrorType", "code"], "sourceRoot": "", "ignoreList": []}