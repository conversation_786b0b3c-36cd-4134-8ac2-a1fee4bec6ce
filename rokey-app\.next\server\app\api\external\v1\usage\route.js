(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[857],{5356:e=>{"use strict";e.exports=require("node:buffer")},5521:e=>{"use strict";e.exports=require("node:async_hooks")},7823:(e,t,s)=>{"use strict";s.r(t),s.d(t,{ComponentMod:()=>R,default:()=>O});var r,a={};s.r(a),s.d(a,{GET:()=>y,OPTIONS:()=>k,runtime:()=>_});var o={};s.r(o),s.d(o,{patchFetch:()=>I,routeModule:()=>S,serverHooks:()=>x,workAsyncStorage:()=>b,workUnitAsyncStorage:()=>w});var i=s(8429),n=s(9874),c=s(8294),l=s(6567),u=s(4144),p=s(5421),d=s(974),m=s(4429),f=s(9975),g=s(1109);let _="edge",h=new m.S,v=g.z.object({start_date:g.z.string().datetime().optional(),end_date:g.z.string().datetime().optional(),granularity:g.z.enum(["hour","day","week","month"]).default("day"),config_id:g.z.string().uuid().optional(),provider:g.z.string().optional(),model:g.z.string().optional()});async function y(e){try{let t=await h.authenticateRequest(e);if(!t.success)return d.Rp.json({error:{message:t.error,type:"authentication_error",code:"invalid_api_key"}},{status:t.statusCode||401});let{userApiKey:s,userConfig:r,ipAddress:a}=t,{searchParams:o}=new URL(e.url),i={start_date:o.get("start_date")||void 0,end_date:o.get("end_date")||void 0,granularity:o.get("granularity")||"day",config_id:o.get("config_id")||void 0,provider:o.get("provider")||void 0,model:o.get("model")||void 0},n=v.safeParse(i);if(!n.success)return d.Rp.json({error:{message:"Invalid query parameters",type:"validation_error",code:"invalid_parameters",details:n.error.errors}},{status:400});let{start_date:c,end_date:l,granularity:u,config_id:p,provider:m,model:g}=n.data,_=l?new Date(l):new Date,y=new Date(c||Date.now()-2592e6),k=(0,f.Qb)(e).from("user_api_key_usage_logs").select(`
        id,
        created_at,
        status_code,
        model_used,
        provider_used,
        request_tokens,
        response_tokens,
        total_tokens,
        cost_usd,
        user_generated_api_keys!inner(
          id,
          key_name,
          custom_api_config_id,
          custom_api_configs!inner(
            id,
            name,
            user_id
          )
        )
      `).gte("created_at",y.toISOString()).lte("created_at",_.toISOString()).order("created_at",{ascending:!1});k=k.eq("user_generated_api_keys.custom_api_configs.user_id",r.user_id),p&&(k=k.eq("user_generated_api_keys.custom_api_config_id",p)),m&&(k=k.eq("provider_used",m)),g&&(k=k.eq("model_used",g));let{data:S,error:b}=await k;if(b)return d.Rp.json({error:{message:"Failed to fetch usage data",type:"server_error",code:"database_error"}},{status:500});let w=S||[],x=w.reduce((e,t)=>({total_requests:e.total_requests+1,total_tokens:e.total_tokens+(t.total_tokens||0),total_cost:e.total_cost+(t.cost_usd||0),successful_requests:e.successful_requests+ +(200===t.status_code),failed_requests:e.failed_requests+ +(200!==t.status_code)}),{total_requests:0,total_tokens:0,total_cost:0,successful_requests:0,failed_requests:0}),I=w.reduce((e,t)=>{let s,r=new Date(t.created_at);switch(u){case"hour":s=r.toISOString().slice(0,13)+":00:00.000Z";break;case"day":default:s=r.toISOString().slice(0,10)+"T00:00:00.000Z";break;case"week":let a=new Date(r);a.setDate(r.getDate()-r.getDay()),s=a.toISOString().slice(0,10)+"T00:00:00.000Z";break;case"month":s=r.toISOString().slice(0,7)+"-01T00:00:00.000Z"}return e[s]||(e[s]={timestamp:s,requests:0,tokens:0,cost:0,success_rate:0,successful:0,failed:0}),e[s].requests+=1,e[s].tokens+=t.total_tokens||0,e[s].cost+=t.cost_usd||0,200===t.status_code?e[s].successful+=1:e[s].failed+=1,e[s].success_rate=e[s].requests>0?e[s].successful/e[s].requests*100:0,e},{}),q=Object.values(I).sort((e,t)=>new Date(e.timestamp).getTime()-new Date(t.timestamp).getTime()),C=w.reduce((e,t)=>{let s=t.provider_used||"unknown";return e[s]||(e[s]={provider:s,requests:0,tokens:0,cost:0}),e[s].requests+=1,e[s].tokens+=t.total_tokens||0,e[s].cost+=t.cost_usd||0,e},{}),R=w.reduce((e,t)=>{let s=t.model_used||"unknown";return e[s]||(e[s]={model:s,requests:0,tokens:0,cost:0}),e[s].requests+=1,e[s].tokens+=t.total_tokens||0,e[s].cost+=t.cost_usd||0,e},{});return h.logApiUsage(s,e,{statusCode:200,modelUsed:"usage_analytics",providerUsed:"rokey_api"},a).catch(console.error),d.Rp.json({object:"usage_report",period:{start_date:y.toISOString(),end_date:_.toISOString(),granularity:u},totals:{...x,success_rate:x.total_requests>0?x.successful_requests/x.total_requests*100:0},time_series:q,breakdowns:{by_provider:Object.values(C),by_model:Object.values(R)},filters_applied:{config_id:p,provider:m,model:g}},{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key","X-RouKey-Usage-Period":`${y.toISOString()} to ${_.toISOString()}`,"X-RouKey-Total-Requests":x.total_requests.toString()}})}catch(e){return d.Rp.json({error:{message:"Internal server error",type:"server_error",code:"internal_error"}},{status:500})}}async function k(){return new d.Rp(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}let S=new l.AppRouteRouteModule({definition:{kind:u.A.APP_ROUTE,page:"/api/external/v1/usage/route",pathname:"/api/external/v1/usage",filename:"route",bundlePath:"app/api/external/v1/usage/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\external\\v1\\usage\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:b,workUnitAsyncStorage:w,serverHooks:x}=S;function I(){return(0,p.V5)({workAsyncStorage:b,workUnitAsyncStorage:w})}let q=null==(r=self.__RSC_MANIFEST)?void 0:r["/api/external/v1/usage/route"],C=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);q&&C&&(0,n.fQ)({page:"/api/external/v1/usage/route",clientReferenceManifest:q,serverActionsManifest:C,serverModuleMap:(0,i.e)({serverActionsManifest:C})});let R=o,O=c.s.wrap(S,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!0},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp","image/avif"],dangerouslyAllowSVG:!0,contentSecurityPolicy:"default-src 'self'; script-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"raw.githubusercontent.com",port:"",pathname:"/lobehub/lobe-icons/**"},{protocol:"https",hostname:"registry.npmmirror.com",port:"",pathname:"/@lobehub/icons-static-png/**"},{protocol:"https",hostname:"cdn.jsdelivr.net",port:"",pathname:"/npm/simple-icons@latest/icons/**"},{protocol:"https",hostname:"cdn.jsdelivr.net",port:"",pathname:"/npm/simple-icons@v11/icons/**"},{protocol:"https",hostname:"images.unsplash.com",port:"",pathname:"/**"},{protocol:"https",hostname:"cloud.gmelius.com",port:"",pathname:"/public/logos/**"},{protocol:"https",hostname:"upload.wikimedia.org",port:"",pathname:"/wikipedia/commons/**"},{protocol:"https",hostname:"kstatic.googleusercontent.com",port:"",pathname:"/files/**"}],unoptimized:!1},devIndicators:{position:"bottom-left"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"C:\\RoKey App\\rokey-app",experimental:{nodeMiddleware:!1,cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,dynamicOnHover:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!0,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!0,largePageDataBytes:128e3,typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,viewTransition:!1,routerBFCache:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,useCache:!1,optimizePackageImports:["@heroicons/react","@headlessui/react","react-markdown","react-syntax-highlighter","@supabase/supabase-js","lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},htmlLimitedBots:"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti",bundlePagesRouterDependencies:!1,configFile:"C:\\RoKey App\\rokey-app\\next.config.mjs",configFileName:"next.config.mjs",serverExternalPackages:["pdf-parse","mammoth"],turbopack:{rules:{"*.svg":{loaders:["@svgr/webpack"],as:"*.js"}},root:"C:\\RoKey App\\rokey-app"},compiler:{removeConsole:!0,reactRemoveProperties:!0},_originalRedirects:[]}})},9975:(e,t,s)=>{"use strict";s.d(t,{Qb:()=>a});var r=s(3339);function a(e){return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,s){},remove(e,t){}}})}s(2710)}},e=>{var t=t=>e(e.s=t);e.O(0,[580,918,109,44,833],()=>t(7823));var s=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/external/v1/usage/route"]=s}]);
//# sourceMappingURL=route.js.map