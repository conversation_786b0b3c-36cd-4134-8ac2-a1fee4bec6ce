(()=>{var e={};e.id=1666,e.ids=[1489,1666],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{Q:()=>a,createSupabaseServerClientOnRequest:()=>i});var s=r(34386),n=r(44999);async function i(){let e=await (0,n.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}function a(e){return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},43156:(e,t,r)=>{"use strict";r.d(t,{Nu:()=>o,iK:()=>a,zX:()=>i});var s=r(94473);let n={free:{name:"Free",price:"$0",priceId:s.Dm.FREE,productId:s.Zu.FREE,features:["Unlimited API requests","1 Custom Configuration","3 API Keys per config","All 300+ AI models","Strict fallback routing only","Basic analytics only","No custom roles, basic router only","Limited logs","Community support"],limits:{configurations:1,apiKeysPerConfig:3,apiRequests:999999,canUseAdvancedRouting:!1,canUseCustomRoles:!1,maxCustomRoles:0,canUsePromptEngineering:!1,canUseKnowledgeBase:!1,knowledgeBaseDocuments:0,canUseSemanticCaching:!1}},starter:{name:"Starter",price:"$19",priceId:s.Dm.STARTER,productId:s.Zu.STARTER,features:["Unlimited API requests","15 Custom Configurations","5 API Keys per config","All 300+ AI models","Intelligent routing strategies","Up to 3 custom roles","Intelligent role routing","Prompt engineering (no file upload)","Enhanced logs and analytics","Community support"],limits:{configurations:15,apiKeysPerConfig:5,apiRequests:999999,canUseAdvancedRouting:!0,canUseCustomRoles:!0,maxCustomRoles:3,canUsePromptEngineering:!0,canUseKnowledgeBase:!1,knowledgeBaseDocuments:0,canUseSemanticCaching:!1}},professional:{name:"Professional",price:"$49",priceId:s.Dm.PROFESSIONAL,productId:s.Zu.PROFESSIONAL,features:["Unlimited API requests","Unlimited Custom Configurations","Unlimited API Keys per config","All 300+ AI models","All advanced routing strategies","Unlimited custom roles","Prompt engineering + Knowledge base (5 documents)","Semantic caching","Advanced analytics and logging","Priority email support"],limits:{configurations:999999,apiKeysPerConfig:999999,apiRequests:999999,canUseAdvancedRouting:!0,canUseCustomRoles:!0,maxCustomRoles:999999,canUsePromptEngineering:!0,canUseKnowledgeBase:!0,knowledgeBaseDocuments:5,canUseSemanticCaching:!0}}};function i(e){return n[e]}function a(e,t,r){let s=n[e].limits;switch(t){case"create_config":return r<s.configurations;case"create_api_key":return r<s.apiKeysPerConfig;default:return!0}}function o(e,t){let r=n[e].limits;switch(t){case"custom_roles":return r.canUseCustomRoles;case"knowledge_base":return r.canUseKnowledgeBase;case"advanced_routing":return r.canUseAdvancedRouting;case"prompt_engineering":return r.canUsePromptEngineering;case"semantic_caching":return r.canUseSemanticCaching;case"configurations":return r.configurations>0;default:return!1}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56534:(e,t,r)=>{"use strict";r.d(t,{Y:()=>c,w:()=>u});let s="AES-GCM",n=process.env.ROKEY_ENCRYPTION_KEY;if(!n||64!==n.length)throw Error("Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.");function i(e){let t=new Uint8Array(e.length/2);for(let r=0;r<e.length;r+=2)t[r/2]=parseInt(e.substr(r,2),16);return t}function a(e){return Array.from(e,e=>e.toString(16).padStart(2,"0")).join("")}let o=i(n);async function u(e){if("string"!=typeof e||0===e.length)throw Error("Encryption input must be a non-empty string.");let t=crypto.getRandomValues(new Uint8Array(12)),r=await crypto.subtle.importKey("raw",o,{name:s},!1,["encrypt"]),n=new TextEncoder().encode(e),i=new Uint8Array(await crypto.subtle.encrypt({name:s,iv:t},r,n)),u=i.slice(0,-16),c=i.slice(-16);return`${a(t)}:${a(c)}:${a(u)}`}async function c(e){if("string"!=typeof e||0===e.length)throw Error("Decryption input must be a non-empty string.");let t=e.split(":");if(3!==t.length)throw Error("Invalid encrypted text format. Expected iv:authTag:encryptedData");let r=i(t[0]),n=i(t[1]),a=i(t[2]);if(12!==r.length)throw Error("Invalid IV length. Expected 12 bytes.");if(16!==n.length)throw Error("Invalid authTag length. Expected 16 bytes.");let u=await crypto.subtle.importKey("raw",o,{name:s},!1,["decrypt"]),c=new Uint8Array(a.length+n.length);c.set(a),c.set(n,a.length);let d=await crypto.subtle.decrypt({name:s,iv:r},u,c);return new TextDecoder().decode(d)}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88502:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>f,serverHooks:()=>E,workAsyncStorage:()=>I,workUnitAsyncStorage:()=>R});var s={};r.r(s),r.d(s,{GET:()=>_,POST:()=>m,PUT:()=>g});var n=r(96559),i=r(48088),a=r(37719),o=r(32190),u=r(2507),c=r(56534),d=r(43156),l=r(55511),p=r.n(l);async function m(e){let t=(0,u.Q)(e),{data:{user:r},error:s}=await t.auth.getUser();if(s||!r)return o.NextResponse.json({error:"Unauthorized: You must be logged in to create API keys."},{status:401});try{let{custom_api_config_id:s,provider:n,predefined_model_id:i,api_key_raw:a,label:u,temperature:l=1}=await e.json();if(!s||!n||!i||!a||!u)return o.NextResponse.json({error:"Missing required fields: custom_api_config_id, provider, predefined_model_id, api_key_raw, label"},{status:400});if(l<0||l>2)return o.NextResponse.json({error:"Temperature must be between 0.0 and 2.0"},{status:400});if("string"!=typeof a||0===a.trim().length)return o.NextResponse.json({error:"API key cannot be empty."},{status:400});let{data:m}=await t.from("subscriptions").select("tier").eq("user_id",r.id).eq("status","active").single(),_=m?.tier||"free",{count:g}=await t.from("api_keys").select("*",{count:"exact",head:!0}).eq("custom_api_config_id",s).eq("user_id",r.id);if(!(0,d.iK)(_,"create_api_key",g||0)){let e=(0,d.zX)(_);return o.NextResponse.json({error:`You have reached the maximum number of API keys (${e.limits.apiKeysPerConfig}) per configuration for your ${_} plan. Please upgrade to add more API keys.`},{status:403})}let f=await (0,c.w)(a),I=p().createHash("sha256").update(a).digest("hex"),R={custom_api_config_id:s,provider:n,predefined_model_id:i,encrypted_api_key:f,label:u,api_key_hash:I,status:"active",is_default_general_chat_model:!1,temperature:l,user_id:r.id},{data:E,error:y}=await t.from("api_keys").insert(R).select().single();if(y){if("23503"===y.code)return o.NextResponse.json({error:"Invalid custom_api_config_id or predefined_model_id.",details:y.message},{status:400});if("23505"===y.code){if(y.message.includes("unique_model_per_config"))return o.NextResponse.json({error:"This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.",details:y.message},{status:409});return o.NextResponse.json({error:"A unique constraint was violated.",details:y.message},{status:409})}return o.NextResponse.json({error:"Failed to save API key",details:y.message},{status:500})}if(E){let{data:e,error:n}=await t.from("api_keys").select("id").eq("custom_api_config_id",s).eq("user_id",r.id).eq("is_default_general_chat_model",!0).neq("id",E.id).limit(1);if(n);else if(!e||0===e.length){let{data:e,error:s}=await t.from("api_keys").update({is_default_general_chat_model:!0}).eq("id",E.id).eq("user_id",r.id).select().single();if(!s)return o.NextResponse.json(e,{status:201})}}return o.NextResponse.json(E,{status:201})}catch(e){if("SyntaxError"===e.name)return o.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});if(e.message.includes("Invalid ROKEY_ENCRYPTION_KEY")||e.message.includes("Encryption input must be a non-empty string"))return o.NextResponse.json({error:"Server-side encryption error",details:e.message},{status:500});return o.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function _(e){let t=(0,u.Q)(e),{data:{user:r},error:s}=await t.auth.getUser();if(s||!r)return o.NextResponse.json({error:"Unauthorized: You must be logged in to view API keys."},{status:401});let{searchParams:n}=new URL(e.url),i=n.get("custom_config_id");if(!i)return o.NextResponse.json({error:"custom_config_id query parameter is required"},{status:400});try{let{data:e,error:s}=await t.from("api_keys").select("id, custom_api_config_id, provider, predefined_model_id, label, status, temperature, created_at, last_used_at, is_default_general_chat_model").eq("custom_api_config_id",i).eq("user_id",r.id).order("created_at",{ascending:!1});if(s)return o.NextResponse.json({error:"Failed to fetch API keys",details:s.message},{status:500});let n=(e||[]).map(e=>({id:e.id,custom_api_config_id:e.custom_api_config_id,provider:e.provider,predefined_model_id:e.predefined_model_id,label:e.label,status:e.status,temperature:e.temperature,created_at:e.created_at,last_used_at:e.last_used_at,is_default_general_chat_model:e.is_default_general_chat_model}));return o.NextResponse.json(n,{status:200})}catch(e){return o.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function g(e){let t=(0,u.Q)(e),{data:{session:r},error:s}=await t.auth.getSession();if(s||!r?.user)return o.NextResponse.json({error:"Unauthorized: You must be logged in to update API keys."},{status:401});let n=r.user,{searchParams:i}=new URL(e.url),a=i.get("id");if(!a)return o.NextResponse.json({error:"id query parameter is required"},{status:400});try{let{temperature:r,predefined_model_id:s}=await e.json(),i={};if(void 0!==r){if(r<0||r>2)return o.NextResponse.json({error:"Temperature must be between 0.0 and 2.0"},{status:400});i.temperature=r}if(void 0!==s){if("string"!=typeof s||0===s.trim().length)return o.NextResponse.json({error:"Model ID must be a non-empty string"},{status:400});i.predefined_model_id=s}if(0===Object.keys(i).length)return o.NextResponse.json({error:"No valid fields provided for update"},{status:400});let{data:u,error:c}=await t.from("api_keys").update(i).eq("id",a).eq("user_id",n.id).select("id, custom_api_config_id, provider, predefined_model_id, label, status, temperature, created_at, last_used_at, is_default_general_chat_model").single();if(c){if("23505"===c.code&&c.message.includes("unique_model_per_config"))return o.NextResponse.json({error:"This model is already configured in this setup. Each model can only be used once per configuration.",details:c.message},{status:409});return o.NextResponse.json({error:"Failed to update API key",details:c.message},{status:500})}return o.NextResponse.json(u,{status:200})}catch(e){if("SyntaxError"===e.name)return o.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});return o.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}let f=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/keys/route",pathname:"/api/keys",filename:"route",bundlePath:"app/api/keys/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\keys\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:I,workUnitAsyncStorage:R,serverHooks:E}=f;function y(){return(0,a.patchFetch)({workAsyncStorage:I,workUnitAsyncStorage:R})}},91645:e=>{"use strict";e.exports=require("net")},94473:(e,t,r)=>{"use strict";r.d(t,{Dm:()=>n,Lj:()=>s,Zu:()=>i});let s={publishableKey:process.env.STRIPE_LIVE_PUBLISHABLE_KEY,secretKey:process.env.STRIPE_LIVE_SECRET_KEY,webhookSecret:process.env.STRIPE_LIVE_WEBHOOK_SECRET},n={FREE:process.env.STRIPE_LIVE_FREE_PRICE_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRICE_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID},i={FREE:process.env.STRIPE_LIVE_FREE_PRODUCT_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRODUCT_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID};process.env.STRIPE_LIVE_FREE_PRICE_ID,process.env.STRIPE_LIVE_STARTER_PRICE_ID,process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID,s.publishableKey&&s.publishableKey.substring(0,20),s.secretKey&&s.secretKey.substring(0,20),s.webhookSecret&&s.webhookSecret.substring(0,15)},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410],()=>r(88502));module.exports=s})();