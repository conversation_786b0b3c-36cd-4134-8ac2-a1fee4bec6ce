(()=>{var e={};e.id=8923,e.ids=[1489,8923],e.modules={2507:(e,s,t)=>{"use strict";t.d(s,{Q:()=>i,createSupabaseServerClientOnRequest:()=>o});var r=t(34386),n=t(44999);async function o(){let e=await (0,n.UL)();return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:s=>e.get(s)?.value,set(s,t,r){try{e.set({name:s,value:t,...r})}catch(e){}},remove(s,t){try{e.set({name:s,value:"",...t})}catch(e){}}}})}function i(e){return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:s=>e.cookies.get(s)?.value,set(e,s,t){},remove(e,s){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},43156:(e,s,t)=>{"use strict";t.d(s,{Nu:()=>a,iK:()=>i,zX:()=>o});var r=t(94473);let n={free:{name:"Free",price:"$0",priceId:r.Dm.FREE,productId:r.Zu.FREE,features:["Unlimited API requests","1 Custom Configuration","3 API Keys per config","All 300+ AI models","Strict fallback routing only","Basic analytics only","No custom roles, basic router only","Limited logs","Community support"],limits:{configurations:1,apiKeysPerConfig:3,apiRequests:999999,canUseAdvancedRouting:!1,canUseCustomRoles:!1,maxCustomRoles:0,canUsePromptEngineering:!1,canUseKnowledgeBase:!1,knowledgeBaseDocuments:0,canUseSemanticCaching:!1}},starter:{name:"Starter",price:"$19",priceId:r.Dm.STARTER,productId:r.Zu.STARTER,features:["Unlimited API requests","15 Custom Configurations","5 API Keys per config","All 300+ AI models","Intelligent routing strategies","Up to 3 custom roles","Intelligent role routing","Prompt engineering (no file upload)","Enhanced logs and analytics","Community support"],limits:{configurations:15,apiKeysPerConfig:5,apiRequests:999999,canUseAdvancedRouting:!0,canUseCustomRoles:!0,maxCustomRoles:3,canUsePromptEngineering:!0,canUseKnowledgeBase:!1,knowledgeBaseDocuments:0,canUseSemanticCaching:!1}},professional:{name:"Professional",price:"$49",priceId:r.Dm.PROFESSIONAL,productId:r.Zu.PROFESSIONAL,features:["Unlimited API requests","Unlimited Custom Configurations","Unlimited API Keys per config","All 300+ AI models","All advanced routing strategies","Unlimited custom roles","Prompt engineering + Knowledge base (5 documents)","Semantic caching","Advanced analytics and logging","Priority email support"],limits:{configurations:999999,apiKeysPerConfig:999999,apiRequests:999999,canUseAdvancedRouting:!0,canUseCustomRoles:!0,maxCustomRoles:999999,canUsePromptEngineering:!0,canUseKnowledgeBase:!0,knowledgeBaseDocuments:5,canUseSemanticCaching:!0}}};function o(e){return n[e]}function i(e,s,t){let r=n[e].limits;switch(s){case"create_config":return t<r.configurations;case"create_api_key":return t<r.apiKeysPerConfig;default:return!0}}function a(e,s){let t=n[e].limits;switch(s){case"custom_roles":return t.canUseCustomRoles;case"knowledge_base":return t.canUseKnowledgeBase;case"advanced_routing":return t.canUseAdvancedRouting;case"prompt_engineering":return t.canUsePromptEngineering;case"semantic_caching":return t.canUseSemanticCaching;case"configurations":return t.configurations>0;default:return!1}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function s(e){var s=Error("Cannot find module '"+e+"'");throw s.code="MODULE_NOT_FOUND",s}s.keys=()=>[],s.resolve=s,s.id=51906,e.exports=s},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72059:(e,s,t)=>{"use strict";t.r(s),t.d(s,{patchFetch:()=>R,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>I,workUnitAsyncStorage:()=>d});var r={};t.r(r),t.d(r,{DELETE:()=>p});var n=t(96559),o=t(48088),i=t(37719),a=t(32190),u=t(2507),c=t(43156);async function p(e,{params:s}){let t=(0,u.Q)(e),{apiKeyId:r,roleName:n}=await s,{data:{session:o},error:i}=await t.auth.getSession();if(i||!o?.user)return a.NextResponse.json({error:"Unauthorized: You must be logged in to unassign roles."},{status:401});let p=o.user.id;if(!r||!n)return a.NextResponse.json({error:"API Key ID and Role Name are required"},{status:400});try{let{data:e,error:s}=await t.from("api_keys").select(`
        custom_api_config_id,
        custom_api_configs ( user_id )
      `).eq("id",r).single();if(s||!e)return a.NextResponse.json({error:"API Key not found or failed to fetch its details for authorization"},{status:404});let o=e.custom_api_configs?.user_id;if(!o)return a.NextResponse.json({error:"Could not determine the config owner for the API Key."},{status:500});if(o&&p!==o)return a.NextResponse.json({error:"Forbidden. You do not own the configuration this API key belongs to."},{status:403});let{data:i}=await t.from("subscriptions").select("tier").eq("user_id",p).eq("status","active").single(),u=i?.tier||"free";if(!(0,c.Nu)(u,"custom_roles"))return a.NextResponse.json({error:`Role management is not available on the ${u} plan. Please upgrade to manage roles for your API keys.`},{status:403});let{error:l,count:I}=await t.from("api_key_role_assignments").delete({count:"exact"}).eq("api_key_id",r).eq("role_name",n);if(l)return a.NextResponse.json({error:"Failed to unassign role from API key",details:l.message},{status:500});if(0===I)return a.NextResponse.json({error:"Role assignment not found for this API key or already unassigned."},{status:404});return a.NextResponse.json({message:`Role '${n}' unassigned successfully from API key ${r}`},{status:200})}catch(e){return a.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}let l=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/keys/[apiKeyId]/roles/[roleName]/route",pathname:"/api/keys/[apiKeyId]/roles/[roleName]",filename:"route",bundlePath:"app/api/keys/[apiKeyId]/roles/[roleName]/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\keys\\[apiKeyId]\\roles\\[roleName]\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:I,workUnitAsyncStorage:d,serverHooks:m}=l;function R(){return(0,i.patchFetch)({workAsyncStorage:I,workUnitAsyncStorage:d})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94473:(e,s,t)=>{"use strict";t.d(s,{Dm:()=>n,Lj:()=>r,Zu:()=>o});let r={publishableKey:process.env.STRIPE_LIVE_PUBLISHABLE_KEY,secretKey:process.env.STRIPE_LIVE_SECRET_KEY,webhookSecret:process.env.STRIPE_LIVE_WEBHOOK_SECRET},n={FREE:process.env.STRIPE_LIVE_FREE_PRICE_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRICE_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID},o={FREE:process.env.STRIPE_LIVE_FREE_PRODUCT_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRODUCT_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID};process.env.STRIPE_LIVE_FREE_PRICE_ID,process.env.STRIPE_LIVE_STARTER_PRICE_ID,process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID,r.publishableKey&&r.publishableKey.substring(0,20),r.secretKey&&r.secretKey.substring(0,20),r.webhookSecret&&r.webhookSecret.substring(0,15)},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var s=require("../../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,580,9398,3410],()=>t(72059));module.exports=r})();