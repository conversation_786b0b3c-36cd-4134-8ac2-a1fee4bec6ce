{"/api/custom-configs/route": "app/api/custom-configs/route.js", "/api/system-status/route": "app/api/system-status/route.js", "/api/pricing/tiers/route": "app/api/pricing/tiers/route.js", "/playground/page": "app/playground/page.js", "/routing-setup/[configId]/page": "app/routing-setup/[configId]/page.js", "/my-models/page": "app/my-models/page.js", "/my-models/[configId]/page": "app/my-models/[configId]/page.js", "/page": "app/page.js", "/logs/page": "app/logs/page.js", "/dashboard/page": "app/dashboard/page.js", "/pricing/page": "app/pricing/page.js", "/auth/signup/page": "app/auth/signup/page.js", "/_not-found/page": "app/_not-found/page.js", "/features/page": "app/features/page.js", "/blog/page": "app/blog/page.js"}