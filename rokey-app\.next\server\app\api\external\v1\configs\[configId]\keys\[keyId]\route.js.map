{"version": 3, "file": "app/api/external/v1/configs/[configId]/keys/[keyId]/route.js", "mappings": "gJEAA,oUFOO,IAAMA,EAAU,OAEjBC,EAAiB,IAAIC,EAAAA,CAAoBA,CAUzCC,EAA0BC,EAAAA,CAAAA,CAAAA,IAVZH,EAUoB,CAAC,CACvCI,QAASD,EAAAA,CAAAA,CAAAA,MAAQ,GAAGE,GAAG,CAAC,GAAGC,QAAQ,GACnCC,MAAOJ,EAAAA,CAAAA,CAAAA,MAAQ,GAAGE,GAAG,CAAC,GAAGG,GAAG,CAAC,KAAKF,QAAQ,GAC1CG,YAAaN,EAAAA,CAAAA,CAAAA,MAAQ,GAAGE,GAAG,CAAC,GAAGG,GAAG,CAAC,GAAGF,QAAQ,GAC9CI,8BAA+BP,EAAAA,CAAAA,CAAAA,OAAS,GAAGG,QAAQ,GACnDK,OAAQR,EAAAA,CAAAA,CAAAA,IAAM,CAAC,CAAC,SAAU,WAAW,EAAEG,QAAQ,EACjD,GAGO,eAAeM,EAAIC,CAAoB,CAAE,QAAEC,CAAM,CAAe,EACrE,GAAI,CAEF,IAAMC,EAAa,MAAMf,EAAegB,kBAADhB,CAAoB,CAACa,GAE5D,GAAI,CAACE,EAAWE,OAAO,CACrB,CADuB,MAChBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAASN,EAAWK,KAAK,CACzBE,KAAM,uBACNC,KAAM,iBACR,CACF,EACA,CAAEZ,OAAQI,EAAWS,UAAU,EAAI,GAAI,GAI3C,GAAM,YAAEC,CAAU,YAAEC,CAAU,CAAEC,WAAS,CAAE,CAAGZ,EACxC,UAAEa,CAAQ,OAAEC,CAAK,CAAE,CAAG,MAAMf,EAG5BgB,EAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAqCA,CAAClB,GAEjD,CAAEmB,KAAMC,CAAG,OAAEb,CAAK,CAAE,CAAG,MAAMU,EAChCI,IAAI,CAAC,YACLC,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;MAoBT,CAAC,EACAC,EAAE,CAAC,KAAMP,GACTO,EAAE,CAAC,uBAAwBR,GAC3BS,MAAM,GAET,GAAIjB,GAAS,CAACa,GAAO,EAAKK,kBAAkB,CAASC,OAAO,GAAKb,EAAYa,OAAO,CAClF,CADoF,MAC7ErB,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,yBACTC,KAAM,kBACNC,KAAM,eACR,CACF,EACA,CAAEZ,OAAQ,GAAI,GAgBlB,OAXAX,EAAewC,WAAW,CACxBf,EACAZ,EACA,CACEW,CAJUxB,UAIE,IACZyC,UAAW,iBACXC,aAAc,WAChB,EACAf,GACAgB,KAAK,CAACC,QAAQxB,KAAK,EAEdF,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvB0B,GAAIZ,EAAIY,EAAE,CACVC,OAAQ,eACRC,SAAUd,EAAIc,QAAQ,CACtBxC,MAAO0B,EAAI1B,KAAK,CAChBI,OAAQsB,EAAItB,MAAM,CAClBD,8BAA+BuB,EAAIvB,6BAA6B,CAChED,YAAawB,EAAIxB,WAAW,CAC5BuC,WAAYf,EAAIe,UAAU,CAC1BC,WAAYhB,EAAIgB,UAAU,CAC1BC,aAAcjB,EAAIiB,YAAY,CAC9BC,MAAOlB,EAAImB,iBAAiB,EAC3B,CACDC,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,4BAChC,+BAAgC,wCAClC,CACF,EAEF,CAAE,MAAOjC,EAAO,CAEd,OAAOF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,wBACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEZ,OAAQ,GAAI,EAElB,CACF,CAGO,eAAe2C,EAAIzC,CAAoB,CAAE,QAAEC,CAAM,CAAe,EACrE,GAAI,CAEF,IAAMC,EAAa,MAAMf,EAAegB,kBAADhB,CAAoB,CAACa,GAE5D,GAAI,CAACE,EAAWE,OAAO,CACrB,CADuB,MAChBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAASN,EAAWK,KAAK,CACzBE,KAAM,uBACNC,KAAM,iBACR,CACF,EACA,CAAEZ,OAAQI,EAAWS,UAAU,EAAI,GAAI,GAI3C,GAAM,YAAEC,CAAU,YAAEC,CAAU,WAAEC,CAAS,CAAE,CAAGZ,EACxC,CAAEa,UAAQ,CAAEC,OAAK,CAAE,CAAG,MAAMf,EAG5ByC,EAAO,MAAM1C,EAAQM,IAAI,GACzBqC,EAAmBtD,EAAwBuD,SAAS,CAACF,GAE3D,GAAI,CAACC,EAAiBvC,OAAO,CAC3B,CAD6B,MACtBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,uBACTC,KAAM,mBACNC,KAAM,qBACNmC,QAASF,EAAiBpC,KAAK,CAACuC,MAAM,CAE1C,EACA,CAAEhD,OAAQ,GAAI,GAIlB,IAAMiD,EAAaJ,EAAiBxB,IAAI,CAGlCF,EAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAqCA,CAAClB,GACnDgD,EAAoB,CACtB,GAAGD,CAAU,CACbX,WAAY,IAAIa,OAAOC,WAAW,EACpC,EAGA,GAAIH,EAAWxD,OAAO,CAAE,CACtB,IAAM4D,EAAkB,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,CAACL,EAAWxD,OAAO,EAIlD4B,EAAOkC,IADOC,cACCC,MAAM,CAACR,EAAWxD,OAAO,EACxCiE,EAAa,MAAMC,OAAOC,MAAM,CAACC,MAAM,CAAC,UAAWxC,GAEnDyC,EADYC,MAAMxC,IAAI,CAAC,IAAIyC,WAAWN,IACfO,GAAG,CAACC,GAAKA,EAAEC,QAAQ,CAAC,IAAIC,QAAQ,CAAC,EAAG,MAAMC,IAAI,CAAC,IAE5EnB,EAAaoB,iBAAiB,CAAGjB,EACjCH,EAAaqB,YAAY,CAAGT,EAC5B,OAAOZ,EAAazD,OAAO,CAI7B,CAJ+B,EAIzB,CAAE4B,KAAMmD,CAAU,OAAE/D,CAAK,CAAE,CAAG,IAJkB,EAIZU,EACvCI,IAAI,CAAC,YACLkD,MAAM,CAACvB,GACPzB,EAAE,CAAC,KAAMP,GACTO,EAAE,CAAC,uBAAwBR,GAC3BQ,EAAE,CAAC,UAAWV,EAAYa,OAAO,EACjCJ,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;MAeT,CAAC,EACAE,MAAM,GAET,GAAIjB,GAAS,CAAC+D,EACZ,OAAOjE,EAAAA,CADiB,CACLA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,6CACTC,KAAM,kBACNC,KAAM,eACR,CACF,EACA,CAAEZ,OAAQ,GAAI,GAgBlB,OAXAX,EAAewC,WAAW,CACxBf,EACAZ,EACA,CACEW,CAJUxB,UAIE,IACZyC,UAAW,iBACXC,aAAc,WAChB,EACAf,GACAgB,KAAK,CAACC,QAAQxB,KAAK,EAEdF,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvB0B,GAAIsC,EAAWtC,EAAE,CACjBC,OAAQ,eACRC,SAAUoC,EAAWpC,QAAQ,CAC7BxC,MAAO4E,EAAW5E,KAAK,CACvBI,OAAQwE,EAAWxE,MAAM,CACzBD,8BAA+ByE,EAAWzE,6BAA6B,CACvED,YAAa0E,EAAW1E,WAAW,CACnCuC,WAAYmC,EAAWnC,UAAU,CACjCC,WAAYkC,EAAWlC,UAAU,CACjCE,MAAOgC,EAAW/B,iBAAiB,EAClC,CACDC,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,4BAChC,+BAAgC,wCAClC,CACF,EAEF,CAAE,MAAOjC,EAAO,CAEd,OAAOF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,wBACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEZ,OAAQ,GAAI,EAElB,CACF,CAGO,eAAe0E,EAAOxE,CAAoB,CAAE,QAAEC,CAAM,CAAe,EACxE,GAAI,CAEF,IAAMC,EAAa,MAAMf,EAAegB,kBAADhB,CAAoB,CAACa,GAE5D,GAAI,CAACE,EAAWE,OAAO,CACrB,CADuB,MAChBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAASN,EAAWK,KAAK,CACzBE,KAAM,uBACNC,KAAM,iBACR,CACF,EACA,CAAEZ,OAAQI,EAAWS,UAAU,EAAI,GAAI,GAI3C,GAAM,YAAEC,CAAU,YAAEC,CAAU,WAAEC,CAAS,CAAE,CAAGZ,EACxC,UAAEa,CAAQ,OAAEC,CAAK,CAAE,CAAG,MAAMf,EAG5BgB,EAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAqCA,CAAClB,GAEjD,CAAEmB,KAAMC,CAAG,CAAEb,MAAOkE,CAAU,CAAE,CAAG,MAAMxD,EAC5CI,IAAI,CAAC,YACLC,MAAM,CAAC,CAAC;;;;;;MAMT,CAAC,EACAC,EAAE,CAAC,KAAMP,GACTO,EAAE,CAAC,uBAAwBR,GAC3BS,MAAM,GAET,GAAIiD,GAAc,CAACrD,GAAO,EAAKK,kBAAkB,CAASC,OAAO,GAAKb,EAAYa,OAAO,CACvF,CADyF,MAClFrB,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,yBACTC,KAAM,kBACNC,KAAM,eACR,CACF,EACA,CAAEZ,OAAQ,GAAI,GAKlB,GAAM,CAAES,MAAOmE,CAAW,CAAE,CAAG,MAAMzD,EAClCI,IAAI,CAAC,YACLsD,MAAM,GACNpD,EAAE,CAAC,KAAMP,GACTO,EAAE,CAAC,uBAAwBR,GAC3BQ,EAAE,CAAC,UAAWV,EAAYa,OAAO,EAEpC,GAAIgD,EAEF,OAAOrE,EAAAA,EAFQ,CAEKC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,gCACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEZ,OAAQ,GAAI,GAgBlB,OAXAX,EAAewC,WAAW,CACxBf,EACAZ,EACA,CACEW,CAJUxB,UAIE,IACZyC,UAAW,iBACXC,aAAc,WAChB,EACAf,GACAgB,KAAK,CAACC,QAAQxB,KAAK,EAEdF,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvB0B,GAAIhB,EACJiB,OAAQ,eACR2C,SAAS,CACX,EAAG,CACDpC,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,4BAChC,+BAAgC,wCAClC,CACF,EAEF,CAAE,MAAOjC,EAAO,CAEd,OAAOF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,wBACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEZ,OAAQ,GAAI,EAElB,CACF,CAGO,eAAe+E,IACpB,OAAO,IAAIxE,EAAAA,EAAYA,CAAC,KAAM,CAC5BP,OAAQ,IACR0C,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,4BAChC,+BAAgC,wCAClC,CACF,EACF,CCnZA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,8DACA,4DACA,iBACA,sEACA,CAAK,CACL,uHACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,uGACA,GAFA,2BAEA,2BACA,OACI,QAA8B,EAClC,8DACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA0B,aAAe,kDAAyD,wOAAuQ,ySAAoU,mBAAmB,QAAQ,uDAA2D,gGAAwG,EAAE,oGAA4G,EAAE,kGAA0G,EAAE,+FAAuG,EAAE,uEAA+E,EAAE,kFAA0F,EAAE,0FAAkG,EAAE,uFAA+F,iBAAsB,gBAAkB,uBAAyB,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,gEAAoE,6BAAoC,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,o/BAAmsC,qBAAyB,ykDAAkmD,idAAge,OAAS,SAAS,qCAAyC,iCAAmC,WAAa,0CAAkD,uBAiBpqM,CAAC,CAAC,EAAC,sBCvBH,uDCAA,oGC6CO,SAAStB,EAAsClB,CAAoB,EACxE,MAAO8E,CAAAA,EAAAA,EAAAA,kBAAAA,CAAkBA,CACvBC,0CAAoC,CACpCA,kNAAyC,CACzC,CACEC,QAAS,KACPC,GACSjF,CADO,CACCgF,OAAO,CAACC,GAAG,CAACC,IAAOC,MAEpCC,IAAIF,CAAY,CAAEC,CAAa,CAAEE,CAAsB,EAGvD,EACAC,OAAOJ,CAAY,CAAEG,CAAsB,EAG3C,CACF,CACF,EAEJ", "sources": ["webpack://_N_E/./src/app/api/external/v1/configs/[configId]/keys/[keyId]/route.ts", "webpack://_N_E/./src/app/api/external/v1/configs/[configId]/keys/[keyId]/route.ts?52a5", "webpack://_N_E/?43e1", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/./src/lib/supabase/server.ts"], "sourcesContent": ["import { type NextRequest, NextResponse } from 'next/server';\nimport { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';\nimport { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';\nimport { encrypt } from '@/lib/encryption';\nimport { z } from 'zod';\n\n// Use Edge Runtime for better performance\nexport const runtime = 'edge';\n\nconst authMiddleware = new ApiKeyAuthMiddleware();\n\ninterface RouteParams {\n  params: Promise<{\n    configId: string;\n    keyId: string;\n  }>;\n}\n\n// Validation schema for updating provider keys\nconst UpdateProviderKeySchema = z.object({\n  api_key: z.string().min(1).optional(),\n  label: z.string().min(1).max(100).optional(),\n  temperature: z.number().min(0).max(2).optional(),\n  is_default_general_chat_model: z.boolean().optional(),\n  status: z.enum(['active', 'inactive']).optional()\n});\n\n// GET /api/external/v1/configs/{configId}/keys/{keyId} - Get specific provider key\nexport async function GET(request: NextRequest, { params }: RouteParams) {\n  try {\n    // 1. Authenticate using user-generated API key\n    const authResult = await authMiddleware.authenticateRequest(request);\n    \n    if (!authResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: authResult.error,\n            type: 'authentication_error',\n            code: 'invalid_api_key'\n          }\n        },\n        { status: authResult.statusCode || 401 }\n      );\n    }\n\n    const { userApiKey, userConfig, ipAddress } = authResult;\n    const { configId, keyId } = await params;\n\n    // 2. Get provider key\n    const supabase = createSupabaseServerClientFromRequest(request);\n    \n    const { data: key, error } = await supabase\n      .from('api_keys')\n      .select(`\n        id,\n        provider,\n        label,\n        status,\n        is_default_general_chat_model,\n        temperature,\n        created_at,\n        updated_at,\n        last_used_at,\n        predefined_models(\n          id,\n          name,\n          display_name,\n          provider_id\n        ),\n        custom_api_configs!inner(\n          id,\n          user_id\n        )\n      `)\n      .eq('id', keyId)\n      .eq('custom_api_config_id', configId)\n      .single();\n\n    if (error || !key || (key.custom_api_configs as any).user_id !== userConfig!.user_id) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Provider key not found',\n            type: 'not_found_error',\n            code: 'key_not_found'\n          }\n        },\n        { status: 404 }\n      );\n    }\n\n    // 3. Log API usage\n    authMiddleware.logApiUsage(\n      userApiKey!,\n      request,\n      {\n        statusCode: 200,\n        modelUsed: 'key_management',\n        providerUsed: 'rokey_api',\n      },\n      ipAddress\n    ).catch(console.error);\n\n    return NextResponse.json({\n      id: key.id,\n      object: 'provider_key',\n      provider: key.provider,\n      label: key.label,\n      status: key.status,\n      is_default_general_chat_model: key.is_default_general_chat_model,\n      temperature: key.temperature,\n      created_at: key.created_at,\n      updated_at: key.updated_at,\n      last_used_at: key.last_used_at,\n      model: key.predefined_models\n    }, {\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n      }\n    });\n\n  } catch (error) {\n    console.error('Error in provider key GET API:', error);\n    return NextResponse.json(\n      {\n        error: {\n          message: 'Internal server error',\n          type: 'server_error',\n          code: 'internal_error'\n        }\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// PUT /api/external/v1/configs/{configId}/keys/{keyId} - Update provider key\nexport async function PUT(request: NextRequest, { params }: RouteParams) {\n  try {\n    // 1. Authenticate using user-generated API key\n    const authResult = await authMiddleware.authenticateRequest(request);\n    \n    if (!authResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: authResult.error,\n            type: 'authentication_error',\n            code: 'invalid_api_key'\n          }\n        },\n        { status: authResult.statusCode || 401 }\n      );\n    }\n\n    const { userApiKey, userConfig, ipAddress } = authResult;\n    const { configId, keyId } = await params;\n\n    // 2. Validate request body\n    const body = await request.json();\n    const validationResult = UpdateProviderKeySchema.safeParse(body);\n\n    if (!validationResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Invalid request data',\n            type: 'validation_error',\n            code: 'invalid_parameters',\n            details: validationResult.error.errors\n          }\n        },\n        { status: 400 }\n      );\n    }\n\n    const updateData = validationResult.data;\n\n    // 3. Prepare update object\n    const supabase = createSupabaseServerClientFromRequest(request);\n    let updateObject: any = {\n      ...updateData,\n      updated_at: new Date().toISOString()\n    };\n\n    // If updating API key, encrypt it\n    if (updateData.api_key) {\n      const encryptedApiKey = await encrypt(updateData.api_key);\n      \n      // Create new API key hash\n      const encoder = new TextEncoder();\n      const data = encoder.encode(updateData.api_key);\n      const hashBuffer = await crypto.subtle.digest('SHA-256', data);\n      const hashArray = Array.from(new Uint8Array(hashBuffer));\n      const apiKeyHash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');\n\n      updateObject.encrypted_api_key = encryptedApiKey;\n      updateObject.api_key_hash = apiKeyHash;\n      delete updateObject.api_key; // Remove plaintext key\n    }\n\n    // 4. Update provider key\n    const { data: updatedKey, error } = await supabase\n      .from('api_keys')\n      .update(updateObject)\n      .eq('id', keyId)\n      .eq('custom_api_config_id', configId)\n      .eq('user_id', userConfig!.user_id)\n      .select(`\n        id,\n        provider,\n        label,\n        status,\n        is_default_general_chat_model,\n        temperature,\n        created_at,\n        updated_at,\n        predefined_models(\n          id,\n          name,\n          display_name,\n          provider_id\n        )\n      `)\n      .single();\n\n    if (error || !updatedKey) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Provider key not found or failed to update',\n            type: 'not_found_error',\n            code: 'key_not_found'\n          }\n        },\n        { status: 404 }\n      );\n    }\n\n    // 5. Log API usage\n    authMiddleware.logApiUsage(\n      userApiKey!,\n      request,\n      {\n        statusCode: 200,\n        modelUsed: 'key_management',\n        providerUsed: 'rokey_api',\n      },\n      ipAddress\n    ).catch(console.error);\n\n    return NextResponse.json({\n      id: updatedKey.id,\n      object: 'provider_key',\n      provider: updatedKey.provider,\n      label: updatedKey.label,\n      status: updatedKey.status,\n      is_default_general_chat_model: updatedKey.is_default_general_chat_model,\n      temperature: updatedKey.temperature,\n      created_at: updatedKey.created_at,\n      updated_at: updatedKey.updated_at,\n      model: updatedKey.predefined_models\n    }, {\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n      }\n    });\n\n  } catch (error) {\n    console.error('Error in provider key PUT API:', error);\n    return NextResponse.json(\n      {\n        error: {\n          message: 'Internal server error',\n          type: 'server_error',\n          code: 'internal_error'\n        }\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// DELETE /api/external/v1/configs/{configId}/keys/{keyId} - Delete provider key\nexport async function DELETE(request: NextRequest, { params }: RouteParams) {\n  try {\n    // 1. Authenticate using user-generated API key\n    const authResult = await authMiddleware.authenticateRequest(request);\n    \n    if (!authResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: authResult.error,\n            type: 'authentication_error',\n            code: 'invalid_api_key'\n          }\n        },\n        { status: authResult.statusCode || 401 }\n      );\n    }\n\n    const { userApiKey, userConfig, ipAddress } = authResult;\n    const { configId, keyId } = await params;\n\n    // 2. Verify key exists and belongs to user\n    const supabase = createSupabaseServerClientFromRequest(request);\n    \n    const { data: key, error: fetchError } = await supabase\n      .from('api_keys')\n      .select(`\n        id,\n        label,\n        custom_api_configs!inner(\n          user_id\n        )\n      `)\n      .eq('id', keyId)\n      .eq('custom_api_config_id', configId)\n      .single();\n\n    if (fetchError || !key || (key.custom_api_configs as any).user_id !== userConfig!.user_id) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Provider key not found',\n            type: 'not_found_error',\n            code: 'key_not_found'\n          }\n        },\n        { status: 404 }\n      );\n    }\n\n    // 3. Delete provider key\n    const { error: deleteError } = await supabase\n      .from('api_keys')\n      .delete()\n      .eq('id', keyId)\n      .eq('custom_api_config_id', configId)\n      .eq('user_id', userConfig!.user_id);\n\n    if (deleteError) {\n      console.error('Error deleting provider key:', deleteError);\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Failed to delete provider key',\n            type: 'server_error',\n            code: 'database_error'\n          }\n        },\n        { status: 500 }\n      );\n    }\n\n    // 4. Log API usage\n    authMiddleware.logApiUsage(\n      userApiKey!,\n      request,\n      {\n        statusCode: 200,\n        modelUsed: 'key_management',\n        providerUsed: 'rokey_api',\n      },\n      ipAddress\n    ).catch(console.error);\n\n    return NextResponse.json({\n      id: keyId,\n      object: 'provider_key',\n      deleted: true\n    }, {\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n      }\n    });\n\n  } catch (error) {\n    console.error('Error in provider key DELETE API:', error);\n    return NextResponse.json(\n      {\n        error: {\n          message: 'Internal server error',\n          type: 'server_error',\n          code: 'internal_error'\n        }\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// OPTIONS handler for CORS\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n    },\n  });\n}\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\configs\\\\[configId]\\\\keys\\\\[keyId]\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/external/v1/configs/[configId]/keys/[keyId]/route\",\n        pathname: \"/api/external/v1/configs/[configId]/keys/[keyId]\",\n        filename: \"route\",\n        bundlePath: \"app/api/external/v1/configs/[configId]/keys/[keyId]/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\configs\\\\[configId]\\\\keys\\\\[keyId]\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fexternal%2Fv1%2Fconfigs%2F%5BconfigId%5D%2Fkeys%2F%5BkeyId%5D%2Froute&page=%2Fapi%2Fexternal%2Fv1%2Fconfigs%2F%5BconfigId%5D%2Fkeys%2F%5BkeyId%5D%2Froute&pagePath=private-next-app-dir%2Fapi%2Fexternal%2Fv1%2Fconfigs%2F%5BconfigId%5D%2Fkeys%2F%5BkeyId%5D%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&appPaths=%2Fapi%2Fexternal%2Fv1%2Fconfigs%2F%5BconfigId%5D%2Fkeys%2F%5BkeyId%5D%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/external/v1/configs/[configId]/keys/[keyId]/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":true},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.mjs\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\",\"image/avif\"],\"dangerouslyAllowSVG\":true,\"contentSecurityPolicy\":\"default-src 'self'; script-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"raw.githubusercontent.com\",\"port\":\"\",\"pathname\":\"/lobehub/lobe-icons/**\"},{\"protocol\":\"https\",\"hostname\":\"registry.npmmirror.com\",\"port\":\"\",\"pathname\":\"/@lobehub/icons-static-png/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@latest/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@v11/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"images.unsplash.com\",\"port\":\"\",\"pathname\":\"/**\"},{\"protocol\":\"https\",\"hostname\":\"cloud.gmelius.com\",\"port\":\"\",\"pathname\":\"/public/logos/**\"},{\"protocol\":\"https\",\"hostname\":\"upload.wikimedia.org\",\"port\":\"\",\"pathname\":\"/wikipedia/commons/**\"},{\"protocol\":\"https\",\"hostname\":\"kstatic.googleusercontent.com\",\"port\":\"\",\"pathname\":\"/files/**\"}],\"unoptimized\":false},\"devIndicators\":{\"position\":\"bottom-left\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"C:\\\\RoKey App\\\\rokey-app\",\"experimental\":{\"nodeMiddleware\":false,\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"dynamicOnHover\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":3,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":true,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":true,\"largePageDataBytes\":128000,\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"viewTransition\":false,\"routerBFCache\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"useCache\":false,\"optimizePackageImports\":[\"@heroicons/react\",\"@headlessui/react\",\"react-markdown\",\"react-syntax-highlighter\",\"@supabase/supabase-js\",\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"htmlLimitedBots\":\"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti\",\"bundlePagesRouterDependencies\":false,\"configFile\":\"C:\\\\RoKey App\\\\rokey-app\\\\next.config.mjs\",\"configFileName\":\"next.config.mjs\",\"serverExternalPackages\":[\"pdf-parse\",\"mammoth\"],\"turbopack\":{\"rules\":{\"*.svg\":{\"loaders\":[\"@svgr/webpack\"],\"as\":\"*.js\"}},\"root\":\"C:\\\\RoKey App\\\\rokey-app\"},\"compiler\":{\"removeConsole\":true,\"reactRemoveProperties\":true},\"_originalRedirects\":[]}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/external/v1/configs/[configId]/keys/[keyId]/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/external/v1/configs/[configId]/keys/[keyId]/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "module.exports = require(\"node:buffer\");", "module.exports = require(\"node:async_hooks\");", "import { createServerClient, type CookieOptions } from '@supabase/ssr';\r\nimport { createClient } from '@supabase/supabase-js';\r\nimport { cookies } from 'next/headers';\r\nimport { NextRequest } from 'next/server';\r\n\r\n// This is the standard setup for creating a Supabase server client\r\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\r\n// Updated for Next.js 15 async cookies requirement\r\nexport async function createSupabaseServerClientOnRequest() {\r\n  const cookieStore = await cookies();\r\n\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        get(name: string) {\r\n          return cookieStore.get(name)?.value;\r\n        },\r\n        set(name: string, value: string, options: CookieOptions) {\r\n          try {\r\n            cookieStore.set({ name, value, ...options });\r\n          } catch (error) {\r\n            // This error can be ignored if running in a Server Component\r\n            // where cookies can't be set directly. Cookie setting should be\r\n            // handled in Server Actions or Route Handlers.\r\n            console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\r\n          }\r\n        },\r\n        remove(name: string, options: CookieOptions) {\r\n          try {\r\n            // To remove a cookie using the `set` method from `next/headers`,\r\n            // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\r\n            cookieStore.set({ name, value: '', ...options });\r\n          } catch (error) {\r\n            // Similar to set, this might fail in a Server Component.\r\n            console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\r\n          }\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n\r\n// Alternative method for API routes that need to handle cookies from request\r\nexport function createSupabaseServerClientFromRequest(request: NextRequest) {\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        get(name: string) {\r\n          return request.cookies.get(name)?.value;\r\n        },\r\n        set(name: string, value: string, options: CookieOptions) {\r\n          // In API routes, we can't set cookies directly on the request\r\n          // This will be handled by the response\r\n        },\r\n        remove(name: string, options: CookieOptions) {\r\n          // In API routes, we can't remove cookies directly on the request\r\n          // This will be handled by the response\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n\r\n// Service role client for admin operations (OAuth token storage, etc.)\r\nexport function createServiceRoleClient() {\r\n  return createClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.SUPABASE_SERVICE_ROLE_KEY!,\r\n    {\r\n      auth: {\r\n        autoRefreshToken: false,\r\n        persistSession: false\r\n      }\r\n    }\r\n  );\r\n}\r\n"], "names": ["runtime", "authMiddleware", "ApiKeyAuthMiddleware", "UpdateProviderKeySchema", "z", "api_key", "min", "optional", "label", "max", "temperature", "is_default_general_chat_model", "status", "GET", "request", "params", "authResult", "authenticateRequest", "success", "NextResponse", "json", "error", "message", "type", "code", "statusCode", "userApiKey", "userConfig", "ip<PERSON><PERSON><PERSON>", "configId", "keyId", "supabase", "createSupabaseServerClientFromRequest", "data", "key", "from", "select", "eq", "single", "custom_api_configs", "user_id", "logApiUsage", "modelUsed", "providerUsed", "catch", "console", "id", "object", "provider", "created_at", "updated_at", "last_used_at", "model", "predefined_models", "headers", "PUT", "body", "validationResult", "safeParse", "details", "errors", "updateData", "updateObject", "Date", "toISOString", "encryptedApiKey", "encrypt", "encoder", "TextEncoder", "encode", "hash<PERSON><PERSON><PERSON>", "crypto", "subtle", "digest", "apiKeyHash", "Array", "Uint8Array", "map", "b", "toString", "padStart", "join", "encrypted_api_key", "api_key_hash", "updated<PERSON>ey", "update", "DELETE", "fetchError", "deleteError", "delete", "deleted", "OPTIONS", "createServerClient", "process", "cookies", "get", "name", "value", "set", "options", "remove"], "sourceRoot": "", "ignoreList": []}